module.exports=[36870,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(54159),c=a.i(81453),d=a.i(54472),e=a.i(41825),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},81453,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactDOM},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},59844,a=>{"use strict";a.s(["User",()=>b],59844);let b=(0,a.i(32639).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},32639,a=>{"use strict";a.s(["default",()=>g],32639);var b=a.i(54159);let c=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},d=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(0,b.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:f=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,b.createElement)("svg",{ref:l,...e,width:c,height:c,stroke:a,strokeWidth:g?24*Number(f)/Number(c):f,className:d("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,c])=>(0,b.createElement)(a,c)),...Array.isArray(i)?i:[i]])),g=(a,e)=>{let g=(0,b.forwardRef)(({className:g,...h},i)=>(0,b.createElement)(f,{ref:i,iconNode:e,className:d(`lucide-${c(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,g),...h}));return g.displayName=c(a),g}},40770,a=>{"use strict";a.s(["Home",()=>b],40770);let b=(0,a.i(32639).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},95788,a=>{"use strict";a.s(["adminNavigation",()=>b,"getRoleDashboardUrl",()=>e,"studentNavigation",()=>d,"teacherNavigation",()=>c]);let b=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"FileText"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],c=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],d=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function e(a){switch(a){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},43626,a=>{"use strict";a.s(["Loader2",()=>b],43626);let b=(0,a.i(32639).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},66710,a=>{"use strict";a.s(["TrendingUp",()=>b],66710);let b=(0,a.i(32639).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29847,a=>{"use strict";a.s(["Clock",()=>b],29847);let b=(0,a.i(32639).default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},58959,a=>{"use strict";a.s(["default",()=>o]);var b=a.i(41825),c=a.i(25384),d=a.i(54159),e=a.i(4082),f=a.i(62821),g=a.i(95788),h=a.i(66710),i=a.i(32541),j=a.i(72613),k=a.i(78402),l=a.i(29847),m=a.i(43626),n=a.i(92761);function o(){let{data:a}=(0,c.useSession)(),[o,p]=(0,d.useState)(null),[q,r]=(0,d.useState)(!0),[s,t]=(0,d.useState)(null);(0,d.useEffect)(()=>{let b=async()=>{try{let a=await fetch("/api/student/dashboard/stats");if(!a.ok)throw Error("Failed to fetch dashboard statistics");let b=await a.json();p(b)}catch(a){t(a instanceof Error?a.message:"An error occurred"),console.error("Error fetching student dashboard stats:",a)}finally{r(!1)}};a?.user?.role==="STUDENT"&&b()},[a]);let u=[{title:"View Marks",description:"Check your latest grades",icon:i.Award,href:"/student/marks",color:"bg-blue-500"},{title:"Attendance History",description:"View your attendance record",icon:j.Calendar,href:"/student/attendance",color:"bg-green-500"},{title:"Download Report",description:"Get your report card",icon:k.FileText,href:"/student/reports",color:"bg-purple-500"},{title:"View Schedule",description:"Check your timetable",icon:l.Clock,href:"/student/schedule",color:"bg-orange-500"}];return(0,b.jsx)(f.default,{title:"Student Dashboard",navigation:g.studentNavigation,children:(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,b.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:["Welcome back, ",a?.user?.firstName||"Student","!"]}),(0,b.jsx)("p",{className:"text-gray-600",children:"Here's your academic overview and progress summary."})]}),q&&(0,b.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,b.jsx)(m.Loader2,{className:"h-8 w-8 animate-spin"}),(0,b.jsx)("span",{className:"ml-2",children:"Loading dashboard statistics..."})]}),s&&(0,b.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,b.jsxs)("p",{className:"text-red-800",children:["Error loading dashboard: ",s]})}),o&&(0,b.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Personal Information"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Class:"}),(0,b.jsx)("span",{className:"font-medium",children:o.currentClass})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Section:"}),(0,b.jsx)("span",{className:"font-medium",children:o.currentSection})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Roll Number:"}),(0,b.jsx)("span",{className:"font-medium",children:o.rollNumber})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Total Subjects:"}),(0,b.jsx)("span",{className:"font-medium",children:o.totalSubjects})]})]}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Attendance Rate:"}),(0,b.jsxs)("span",{className:"font-medium text-green-600",children:[o.attendanceRate,"%"]})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Average Marks:"}),(0,b.jsxs)("span",{className:"font-medium text-blue-600",children:[o.averageMarks,"%"]})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Upcoming Exams:"}),(0,b.jsx)("span",{className:"font-medium",children:o.upcomingExams})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Total Records:"}),(0,b.jsxs)("span",{className:"font-medium",children:[o.totalMarksRecords," marks, ",o.totalAttendanceRecords," attendance"]})]})]})]})]}),o&&(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Attendance"}),(0,b.jsx)(h.TrendingUp,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(e.CardContent,{children:[(0,b.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[o.attendanceRate,"%"]}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"Last 30 days"})]})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Average Marks"}),(0,b.jsx)(i.Award,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(e.CardContent,{children:[(0,b.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[o.averageMarks,"%"]}),(0,b.jsxs)("p",{className:"text-xs text-muted-foreground",children:[o.totalMarksRecords," exams taken"]})]})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Total Subjects"}),(0,b.jsx)(n.BookOpen,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(e.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:o.totalSubjects}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"Current semester"})]})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Upcoming Exams"}),(0,b.jsx)(j.Calendar,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(e.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:o.upcomingExams}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"Scheduled exams"})]})]})]}),o&&o.recentMarks.length>0&&(0,b.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Marks"}),(0,b.jsx)("div",{className:"space-y-3",children:o.recentMarks.slice(0,5).map(a=>(0,b.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-medium",children:a.subject}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:a.examName})]})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Score"}),(0,b.jsxs)("p",{className:"font-medium text-blue-600",children:[a.obtainedMarks,"/",a.maxMarks]})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Percentage"}),(0,b.jsxs)("p",{className:"font-medium text-green-600",children:[a.percentage,"%"]})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Date"}),(0,b.jsx)("p",{className:"font-medium text-purple-600",children:new Date(a.date).toLocaleDateString()})]})]})]},a.id))})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,b.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:u.map(a=>(0,b.jsx)(e.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,b.jsx)(e.CardContent,{className:"p-4",children:(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:`p-2 rounded-lg ${a.color}`,children:(0,b.jsx)(a.icon,{className:"h-5 w-5 text-white"})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium text-sm",children:a.title}),(0,b.jsx)("p",{className:"text-xs text-gray-500",children:a.description})]})]})})},a.title))})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Activities"}),(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-green-50 rounded-lg",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("p",{className:"text-sm font-medium",children:"Marks uploaded"}),(0,b.jsx)("p",{className:"text-xs text-gray-500",children:"Mathematics - Unit Test 1: 92%"})]}),(0,b.jsx)("span",{className:"text-xs text-gray-500",children:"2 days ago"})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-blue-50 rounded-lg",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("p",{className:"text-sm font-medium",children:"Attendance marked"}),(0,b.jsx)("p",{className:"text-xs text-gray-500",children:"Present in all classes today"})]}),(0,b.jsx)("span",{className:"text-xs text-gray-500",children:"1 day ago"})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("p",{className:"text-sm font-medium",children:"Report card generated"}),(0,b.jsx)("p",{className:"text-xs text-gray-500",children:"Term 1 report available for download"})]}),(0,b.jsx)("span",{className:"text-xs text-gray-500",children:"1 week ago"})]})]})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__075f2b0d._.js.map