(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["chunks/[root-of-the-server]__b388f26c._.js",1923,(e,t,r)=>{"use strict";var n=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,s={};function c(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function l(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,a]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=a?a:"true"))}catch{}}return t}function u(e){if(!e)return;let[[t,r],...n]=l(e),{domain:a,expires:i,httponly:o,maxage:s,path:c,samesite:u,secure:h,partitioned:f,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var y,m,b={name:t,value:decodeURIComponent(r),domain:a,...i&&{expires:new Date(i)},...o&&{httpOnly:!0},..."string"==typeof s&&{maxAge:Number(s)},path:c,...u&&{sameSite:d.includes(y=(y=u).toLowerCase())?y:void 0},...h&&{secure:!0},...g&&{priority:p.includes(m=(m=g).toLowerCase())?m:void 0},...f&&{partitioned:!0}};let e={};for(let t in b)b[t]&&(e[t]=b[t]);return e}}((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(s,{RequestCookies:()=>h,ResponseCookies:()=>f,parseCookie:()=>l,parseSetCookie:()=>u,stringifyCookie:()=>c}),t.exports=((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let c of i(t))o.call(e,c)||c===r||n(e,c,{get:()=>t[c],enumerable:!(s=a(t,c))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s);var d=["strict","lax","none"],p=["low","medium","high"],h=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of l(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>c(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>c(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},f=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let a=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(a)?a:function(e){if(!e)return[];var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}(a)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,a=this._parsed;return a.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=c(r);t.append("set-cookie",e)}}(a,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(c).join("; ")}}},12431,(e,t,r)=>{(()=>{"use strict";var r={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),a=r(172),i=r(930),o="context",s=new n.NoopContextManager;class c{constructor(){}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalContextManager(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,a.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.ContextAPI=c},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),a=r(912),i=r(957),o=r(172);class s{constructor(){function e(e){return function(...t){let r=(0,o.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:i.DiagLogLevel.INFO})=>{var n,s,c;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let l=(0,o.getGlobal)("diag"),u=(0,a.createLogLevelDiagLogger)(null!=(s=r.logLevel)?s:i.DiagLogLevel.INFO,e);if(l&&!r.suppressOverrideMessage){let e=null!=(c=Error().stack)?c:"<failed to generate stacktrace>";l.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),a=r(172),i=r(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}getMeterProvider(){return(0,a.getGlobal)(o)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),a=r(874),i=r(194),o=r(277),s=r(369),c=r(930),l="propagation",u=new a.NoopTextMapPropagator;class d{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(l,e,c.DiagAPI.instance())}inject(e,t,r=i.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=i.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(l,c.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(l)||u}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),a=r(846),i=r(139),o=r(607),s=r(930),c="trace";class l{constructor(){this._proxyTracerProvider=new a.ProxyTracerProvider,this.wrapSpanContext=i.wrapSpanContext,this.isSpanContextValid=i.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(c,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(c)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(c,s.DiagAPI.instance()),this._proxyTracerProvider=new a.ProxyTracerProvider}}t.TraceAPI=l},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),a=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function i(e){return e.getValue(a)||void 0}t.getBaggage=i,t.getActiveBaggage=function(){return i(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(a,t)},t.deleteBaggage=function(e){return e.deleteValue(a)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),a=r(993),i=r(830),o=n.DiagAPI.instance();t.createBaggage=function(e={}){return new a.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:i.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);t.NoopContextManager=class{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let a=new r(t._currentContext);return a._currentContext.set(e,n),a},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);function a(e,t,r){let a=(0,n.getGlobal)("diag");if(a)return r.unshift(t),a[e](...r)}t.DiagComponentLogger=class{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return a("debug",this._namespace,e)}error(...e){return a("error",this._namespace,e)}info(...e){return a("info",this._namespace,e)}warn(...e){return a("warn",this._namespace,e)}verbose(...e){return a("verbose",this._namespace,e)}}},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];t.DiagConsoleLogger=class{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let a=t[r];return"function"==typeof a&&e>=n?a.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),a=r(521),i=r(130),o=a.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),c=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var i;let o=c[s]=null!=(i=c[s])?i:{version:a.VERSION};if(!n&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(o.version!==a.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${a.VERSION}`);return r.error(t.stack||t.message),!1}return o[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=c[s])?void 0:t.version;if(n&&(0,i.isCompatible)(n))return null==(r=c[s])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);let r=c[s];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),a=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function i(e){let t=new Set([e]),r=new Set,n=e.match(a);if(!n)return()=>!1;let i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=i.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(a);if(!n)return o(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease||i.major!==s.major)return o(e);if(0===i.major)return i.minor===s.minor&&i.patch<=s.patch?(t.add(e),!0):o(e);return i.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=i,t.isCompatible=i(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class a extends n{add(e,t){}}t.NoopCounterMetric=a;class i extends n{add(e,t){}}t.NoopUpDownCounterMetric=i;class o extends n{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class c extends s{}t.NoopObservableCounterMetric=c;class l extends s{}t.NoopObservableGaugeMetric=l;class u extends s{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new a,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new i,t.NOOP_OBSERVABLE_COUNTER_METRIC=new c,t.NOOP_OBSERVABLE_GAUGE_METRIC=new l,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class a{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=a,t.NOOP_METER_PROVIDER=new a},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(46),t)},651:(t,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r._globalThis=void 0,r._globalThis="object"==typeof globalThis?globalThis:e.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0,t.NoopTextMapPropagator=class{inject(e,t){}extract(e,t){return e}fields(){return[]}}},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);t.NonRecordingSpan=class{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),a=r(607),i=r(403),o=r(139),s=n.ContextAPI.getInstance();t.NoopTracer=class{startSpan(e,t,r=s.active()){var n;if(null==t?void 0:t.root)return new i.NonRecordingSpan;let c=r&&(0,a.getSpanContext)(r);return"object"==typeof(n=c)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,o.isSpanContextValid)(c)?new i.NonRecordingSpan(c):new i.NonRecordingSpan}startActiveSpan(e,t,r,n){let i,o,c;if(arguments.length<2)return;2==arguments.length?c=t:3==arguments.length?(i=t,c=r):(i=t,o=r,c=n);let l=null!=o?o:s.active(),u=this.startSpan(e,i,l),d=(0,a.setSpan)(l,u);return s.with(d,c,void 0,u)}}},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);t.NoopTracerProvider=class{getTracer(e,t,r){return new n.NoopTracer}}},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;t.ProxyTracer=class{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let a=this._getTracer();return Reflect.apply(a.startActiveSpan,a,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),a=new(r(124)).NoopTracerProvider;t.ProxyTracerProvider=class{getTracer(e,t,r){var a;return null!=(a=this.getDelegateTracer(e,t,r))?a:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:a}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),a=r(403),i=r(491),o=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function c(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(i.ContextAPI.getInstance().active())},t.setSpan=c,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return c(e,new a.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=s(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class a{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),a=r.indexOf("=");if(-1!==a){let i=r.slice(0,a),o=r.slice(a+1,t.length);(0,n.validateKey)(i)&&(0,n.validateValue)(o)&&e.set(i,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new a;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=a},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,a=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,i=RegExp(`^(?:${n}|${a})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return i.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),a=r(403),i=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return i.test(e)&&e!==n.INVALID_TRACEID}function c(e){return o.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=c,t.isSpanContextValid=function(e){return s(e.traceId)&&c(e.spanId)},t.wrapSpanContext=function(e){return new a.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;var i=n[e]={exports:{}},o=!0;try{r[e].call(i.exports,i,i.exports,a),o=!1}finally{o&&delete n[e]}return i.exports}a.ab="/ROOT/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/compiled/@opentelemetry/api/";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0}),i.trace=i.propagation=i.metrics=i.diag=i.context=i.INVALID_SPAN_CONTEXT=i.INVALID_TRACEID=i.INVALID_SPANID=i.isValidSpanId=i.isValidTraceId=i.isSpanContextValid=i.createTraceState=i.TraceFlags=i.SpanStatusCode=i.SpanKind=i.SamplingDecision=i.ProxyTracerProvider=i.ProxyTracer=i.defaultTextMapSetter=i.defaultTextMapGetter=i.ValueType=i.createNoopMeter=i.DiagLogLevel=i.DiagConsoleLogger=i.ROOT_CONTEXT=i.createContextKey=i.baggageEntryMetadataFromString=void 0;var e=a(369);Object.defineProperty(i,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=a(780);Object.defineProperty(i,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(i,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=a(972);Object.defineProperty(i,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=a(957);Object.defineProperty(i,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var o=a(102);Object.defineProperty(i,"createNoopMeter",{enumerable:!0,get:function(){return o.createNoopMeter}});var s=a(901);Object.defineProperty(i,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var c=a(194);Object.defineProperty(i,"defaultTextMapGetter",{enumerable:!0,get:function(){return c.defaultTextMapGetter}}),Object.defineProperty(i,"defaultTextMapSetter",{enumerable:!0,get:function(){return c.defaultTextMapSetter}});var l=a(125);Object.defineProperty(i,"ProxyTracer",{enumerable:!0,get:function(){return l.ProxyTracer}});var u=a(846);Object.defineProperty(i,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var d=a(996);Object.defineProperty(i,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var p=a(357);Object.defineProperty(i,"SpanKind",{enumerable:!0,get:function(){return p.SpanKind}});var h=a(847);Object.defineProperty(i,"SpanStatusCode",{enumerable:!0,get:function(){return h.SpanStatusCode}});var f=a(475);Object.defineProperty(i,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=a(98);Object.defineProperty(i,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var y=a(139);Object.defineProperty(i,"isSpanContextValid",{enumerable:!0,get:function(){return y.isSpanContextValid}}),Object.defineProperty(i,"isValidTraceId",{enumerable:!0,get:function(){return y.isValidTraceId}}),Object.defineProperty(i,"isValidSpanId",{enumerable:!0,get:function(){return y.isValidSpanId}});var m=a(476);Object.defineProperty(i,"INVALID_SPANID",{enumerable:!0,get:function(){return m.INVALID_SPANID}}),Object.defineProperty(i,"INVALID_TRACEID",{enumerable:!0,get:function(){return m.INVALID_TRACEID}}),Object.defineProperty(i,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return m.INVALID_SPAN_CONTEXT}});let b=a(67);Object.defineProperty(i,"context",{enumerable:!0,get:function(){return b.context}});let w=a(506);Object.defineProperty(i,"diag",{enumerable:!0,get:function(){return w.diag}});let v=a(886);Object.defineProperty(i,"metrics",{enumerable:!0,get:function(){return v.metrics}});let _=a(939);Object.defineProperty(i,"propagation",{enumerable:!0,get:function(){return _.propagation}});let E=a(845);Object.defineProperty(i,"trace",{enumerable:!0,get:function(){return E.trace}}),i.default={context:b.context,diag:w.diag,metrics:v.metrics,propagation:_.propagation,trace:E.trace}})(),t.exports=i})()},68507,(e,t,r)=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="/ROOT/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/compiled/cookie/");var e={};(()=>{e.parse=function(e,r){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var a={},i=e.split(n),o=(r||{}).decode||t,s=0;s<i.length;s++){var c=i[s],l=c.indexOf("=");if(!(l<0)){var u=c.substr(0,l).trim(),d=c.substr(++l,c.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==a[u]&&(a[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return a},e.serialize=function(e,t,n){var i=n||{},o=i.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!a.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!a.test(s))throw TypeError("argument val is invalid");var c=e+"="+s;if(null!=i.maxAge){var l=i.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(i.domain){if(!a.test(i.domain))throw TypeError("option domain is invalid");c+="; Domain="+i.domain}if(i.path){if(!a.test(i.path))throw TypeError("option path is invalid");c+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(c+="; HttpOnly"),i.secure&&(c+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var t=decodeURIComponent,r=encodeURIComponent,n=/; */,a=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),t.exports=e})()},63567,(e,t,r)=>{(()=>{"use strict";var e={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function a(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var s=new a(n,i||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,a=[];if(0===this._eventsCount)return a;for(n in e=this._events)t.call(e,n)&&a.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?a.concat(Object.getOwnPropertySymbols(e)):a},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var a=0,i=n.length,o=Array(i);a<i;a++)o[a]=n[a].fn;return o},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,a,i,o){var s=r?r+e:e;if(!this._events[s])return!1;var c,l,u=this._events[s],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,a),!0;case 5:return u.fn.call(u.context,t,n,a,i),!0;case 6:return u.fn.call(u.context,t,n,a,i,o),!0}for(l=1,c=Array(d-1);l<d;l++)c[l-1]=arguments[l];u.fn.apply(u.context,c)}else{var p,h=u.length;for(l=0;l<h;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),d){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,n);break;case 4:u[l].fn.call(u[l].context,t,n,a);break;default:if(!c)for(p=1,c=Array(d-1);p<d;p++)c[p-1]=arguments[p];u[l].fn.apply(u[l].context,c)}}return!0},s.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,a){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return o(this,i),this;var s=this._events[i];if(s.fn)s.fn!==t||a&&!s.once||n&&s.context!==n||o(this,i);else{for(var c=0,l=[],u=s.length;c<u;c++)(s[c].fn!==t||a&&!s[c].once||n&&s[c].context!==n)&&l.push(s[c]);l.length?this._events[i]=1===l.length?l[0]:l:o(this,i)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,a=e.length;for(;a>0;){let i=a/2|0,o=n+i;0>=r(e[o],t)?(n=++o,a-=i+1):a=i}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);t.default=class{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let a=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(a,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}},816:(e,t,r)=>{let n=r(213);class a extends Error{constructor(e){super(e),this.name="TimeoutError"}}let i=(e,t,r)=>new Promise((i,o)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void i(e);let s=setTimeout(()=>{if("function"==typeof r){try{i(r())}catch(e){o(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,s=r instanceof Error?r:new a(n);"function"==typeof e.cancel&&e.cancel(),o(s)},t);n(e.then(i,o),()=>{clearTimeout(s)})});e.exports=i,e.exports.default=i,e.exports.TimeoutError=a}},r={};function n(t){var a=r[t];if(void 0!==a)return a.exports;var i=r[t]={exports:{}},o=!0;try{e[t](i,i.exports,n),o=!1}finally{o&&delete r[t]}return i.exports}n.ab="/ROOT/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/compiled/p-queue/";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),i=()=>{},o=new t.TimeoutError;a.default=class extends e{constructor(e){var t,n,a,o;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=i,this._resolveIdle=i,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(o=null==(a=e.interval)?void 0:a.toString())?o:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=i,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=i,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,a)=>{let i=async()=>{this._pendingCount++,this._intervalCount++;try{let i=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&a(o)});n(await i)}catch(e){a(e)}this._next()};this._queue.enqueue(i,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}})(),t.exports=a})()},51615,(e,t,r)=>{t.exports=e.x("node:buffer",()=>require("node:buffer"))},78500,(e,t,r)=>{t.exports=e.x("node:async_hooks",()=>require("node:async_hooks"))},31245,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{getTestReqInfo:function(){return o},withRequest:function(){return i}});let n=new(e.r(78500)).AsyncLocalStorage;function a(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function i(e,t,r){let i=a(e,t);return i?n.run(i,r):r()}function o(e,t){let r=n.getStore();return r||(e&&t?a(e,t):void 0)}},87251,(e,t,r)=>{"use strict";var n=e.i(51615);Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{handleFetch:function(){return s},interceptFetch:function(){return c},reader:function(){return i}});let a=e.r(31245),i={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function o(e,t){let{url:r,method:a,headers:i,body:o,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:h}=t;return{testData:e,api:"fetch",request:{url:r,method:a,headers:[...Array.from(i),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:o?n.Buffer.from(await t.arrayBuffer()).toString("base64"):null,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:h}}}async function s(e,t){let r=(0,a.getTestReqInfo)(t,i);if(!r)return e(t);let{testData:s,proxyPort:c}=r,l=await o(s,t),u=await e(`http://localhost:${c}`,{method:"POST",body:JSON.stringify(l),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await u.json(),{api:p}=d;switch(p){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0});case"fetch":let{status:h,headers:f,body:g}=d.response;return new Response(g?n.Buffer.from(g,"base64"):null,{status:h,headers:new Headers(f)});default:return p}}function c(t){return e.g.fetch=function(e,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?t(e,r):s(t,new Request(e,r))},()=>{e.g.fetch=t}}},92137,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{interceptTestApis:function(){return i},wrapRequestHandler:function(){return o}});let n=e.r(31245),a=e.r(87251);function i(){return(0,a.interceptFetch)(e.g.fetch)}function o(e){return(t,r)=>(0,n.withRequest)(t,a.reader,()=>e(t,r))}},18862,(e,t,r)=>{t.exports=function(e){return e&&e.__esModule?e:{default:e}},t.exports.__esModule=!0,t.exports.default=t.exports},62265,(e,t,r)=>{(()=>{var r={226:function(t,r){!function(n,a){"use strict";var i="function",o="undefined",s="object",c="string",l="major",u="model",d="name",p="type",h="vendor",f="version",g="architecture",y="console",m="mobile",b="tablet",w="smarttv",v="wearable",_="embedded",E="Amazon",S="Apple",x="ASUS",A="BlackBerry",R="Browser",C="Chrome",P="Firefox",T="Google",O="Huawei",k="Microsoft",N="Motorola",I="Opera",H="Samsung",M="Sharp",D="Sony",j="Xiaomi",U="Zebra",W="Facebook",L="Chromium OS",K="Mac OS",$=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},J=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},B=function(e,t){return typeof e===c&&-1!==q(t).indexOf(q(e))},q=function(e){return e.toLowerCase()},V=function(e,t){if(typeof e===c)return e=e.replace(/^\s\s*/,""),typeof t===o?e:e.substring(0,350)},G=function(e,t){for(var r,n,o,c,l,u,d=0;d<t.length&&!l;){var p=t[d],h=t[d+1];for(r=n=0;r<p.length&&!l&&p[r];)if(l=p[r++].exec(e))for(o=0;o<h.length;o++)u=l[++n],typeof(c=h[o])===s&&c.length>0?2===c.length?typeof c[1]==i?this[c[0]]=c[1].call(this,u):this[c[0]]=c[1]:3===c.length?typeof c[1]!==i||c[1].exec&&c[1].test?this[c[0]]=u?u.replace(c[1],c[2]):void 0:this[c[0]]=u?c[1].call(this,u,c[2]):void 0:4===c.length&&(this[c[0]]=u?c[3].call(this,u.replace(c[1],c[2])):a):this[c]=u||a;d+=2}},z=function(e,t){for(var r in t)if(typeof t[r]===s&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(B(t[r][n],e))return"?"===r?a:r}else if(B(t[r],e))return"?"===r?a:r;return e},F={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},X={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[f,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[f,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,f],[/opios[\/ ]+([\w\.]+)/i],[f,[d,I+" Mini"]],[/\bopr\/([\w\.]+)/i],[f,[d,I]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[d,f],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[f,[d,"UC"+R]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[f,[d,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[f,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[f,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[f,[d,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[f,[d,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure "+R],f],[/\bfocus\/([\w\.]+)/i],[f,[d,P+" Focus"]],[/\bopt\/([\w\.]+)/i],[f,[d,I+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[f,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[f,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[f,[d,I+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[f,[d,"MIUI "+R]],[/fxios\/([-\w\.]+)/i],[f,[d,P]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 "+R]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 "+R],f],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],f],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[d,f],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,W],f],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[d,f],[/\bgsa\/([\w\.]+) .*safari\//i],[f,[d,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[f,[d,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[f,[d,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,C+" WebView"],f],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[f,[d,"Android "+R]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,f],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[f,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[f,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[f,z,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,f],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],f],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[f,[d,P+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[d,f],[/(cobalt)\/([\w\.]+)/i],[d,[f,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,q]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",q]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,q]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[u,[h,H],[p,b]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[u,[h,H],[p,m]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[u,[h,S],[p,m]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[u,[h,S],[p,b]],[/(macintosh);/i],[u,[h,S]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[u,[h,M],[p,m]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[u,[h,O],[p,b]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[u,[h,O],[p,m]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[u,/_/g," "],[h,j],[p,m]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[u,/_/g," "],[h,j],[p,b]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[u,[h,"OPPO"],[p,m]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[u,[h,"Vivo"],[p,m]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[u,[h,"Realme"],[p,m]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[u,[h,N],[p,m]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[u,[h,N],[p,b]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[u,[h,"LG"],[p,b]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[u,[h,"LG"],[p,m]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[u,[h,"Lenovo"],[p,b]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[u,/_/g," "],[h,"Nokia"],[p,m]],[/(pixel c)\b/i],[u,[h,T],[p,b]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[u,[h,T],[p,m]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[h,D],[p,m]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[u,"Xperia Tablet"],[h,D],[p,b]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[u,[h,"OnePlus"],[p,m]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[u,[h,E],[p,b]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[u,/(.+)/g,"Fire Phone $1"],[h,E],[p,m]],[/(playbook);[-\w\),; ]+(rim)/i],[u,h,[p,b]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[u,[h,A],[p,m]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[u,[h,x],[p,b]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[u,[h,x],[p,m]],[/(nexus 9)/i],[u,[h,"HTC"],[p,b]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[h,[u,/_/g," "],[p,m]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[u,[h,"Acer"],[p,b]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[u,[h,"Meizu"],[p,m]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[h,u,[p,m]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[h,u,[p,b]],[/(surface duo)/i],[u,[h,k],[p,b]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[u,[h,"Fairphone"],[p,m]],[/(u304aa)/i],[u,[h,"AT&T"],[p,m]],[/\bsie-(\w*)/i],[u,[h,"Siemens"],[p,m]],[/\b(rct\w+) b/i],[u,[h,"RCA"],[p,b]],[/\b(venue[\d ]{2,7}) b/i],[u,[h,"Dell"],[p,b]],[/\b(q(?:mv|ta)\w+) b/i],[u,[h,"Verizon"],[p,b]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[u,[h,"Barnes & Noble"],[p,b]],[/\b(tm\d{3}\w+) b/i],[u,[h,"NuVision"],[p,b]],[/\b(k88) b/i],[u,[h,"ZTE"],[p,b]],[/\b(nx\d{3}j) b/i],[u,[h,"ZTE"],[p,m]],[/\b(gen\d{3}) b.+49h/i],[u,[h,"Swiss"],[p,m]],[/\b(zur\d{3}) b/i],[u,[h,"Swiss"],[p,b]],[/\b((zeki)?tb.*\b) b/i],[u,[h,"Zeki"],[p,b]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[h,"Dragon Touch"],u,[p,b]],[/\b(ns-?\w{0,9}) b/i],[u,[h,"Insignia"],[p,b]],[/\b((nxa|next)-?\w{0,9}) b/i],[u,[h,"NextBook"],[p,b]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[h,"Voice"],u,[p,m]],[/\b(lvtel\-)?(v1[12]) b/i],[[h,"LvTel"],u,[p,m]],[/\b(ph-1) /i],[u,[h,"Essential"],[p,m]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[u,[h,"Envizen"],[p,b]],[/\b(trio[-\w\. ]+) b/i],[u,[h,"MachSpeed"],[p,b]],[/\btu_(1491) b/i],[u,[h,"Rotor"],[p,b]],[/(shield[\w ]+) b/i],[u,[h,"Nvidia"],[p,b]],[/(sprint) (\w+)/i],[h,u,[p,m]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[h,k],[p,m]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[u,[h,U],[p,b]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[u,[h,U],[p,m]],[/smart-tv.+(samsung)/i],[h,[p,w]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[h,H],[p,w]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[h,"LG"],[p,w]],[/(apple) ?tv/i],[h,[u,S+" TV"],[p,w]],[/crkey/i],[[u,C+"cast"],[h,T],[p,w]],[/droid.+aft(\w)( bui|\))/i],[u,[h,E],[p,w]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[u,[h,M],[p,w]],[/(bravia[\w ]+)( bui|\))/i],[u,[h,D],[p,w]],[/(mitv-\w{5}) bui/i],[u,[h,j],[p,w]],[/Hbbtv.*(technisat) (.*);/i],[h,u,[p,w]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[h,V],[u,V],[p,w]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[h,u,[p,y]],[/droid.+; (shield) bui/i],[u,[h,"Nvidia"],[p,y]],[/(playstation [345portablevi]+)/i],[u,[h,D],[p,y]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[u,[h,k],[p,y]],[/((pebble))app/i],[h,u,[p,v]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[u,[h,S],[p,v]],[/droid.+; (glass) \d/i],[u,[h,T],[p,v]],[/droid.+; (wt63?0{2,3})\)/i],[u,[h,U],[p,v]],[/(quest( 2| pro)?)/i],[u,[h,W],[p,v]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[h,[p,_]],[/(aeobc)\b/i],[u,[h,E],[p,_]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[u,[p,m]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[u,[p,b]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,b]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,m]],[/(android[-\w\. ]{0,9});.+buil/i],[u,[h,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[f,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[f,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[d,f],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[f,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,f],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[d,[f,z,F]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[d,"Windows"],[f,z,F]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[f,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,K],[f,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[f,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,f],[/\(bb(10);/i],[f,[d,A]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[f,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[f,[d,P+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[f,[d,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[f,[d,"watchOS"]],[/crkey\/([\d\.]+)/i],[f,[d,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[d,L],f],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,f],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],f],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[d,f]]},Y=function(e,t){if(typeof e===s&&(t=e,e=a),!(this instanceof Y))return new Y(e,t).getResult();var r=typeof n!==o&&n.navigator?n.navigator:a,y=e||(r&&r.userAgent?r.userAgent:""),w=r&&r.userAgentData?r.userAgentData:a,v=t?$(X,t):X,_=r&&r.userAgent==y;return this.getBrowser=function(){var e,t={};return t[d]=a,t[f]=a,G.call(t,y,v.browser),t[l]=typeof(e=t[f])===c?e.replace(/[^\d\.]/g,"").split(".")[0]:a,_&&r&&r.brave&&typeof r.brave.isBrave==i&&(t[d]="Brave"),t},this.getCPU=function(){var e={};return e[g]=a,G.call(e,y,v.cpu),e},this.getDevice=function(){var e={};return e[h]=a,e[u]=a,e[p]=a,G.call(e,y,v.device),_&&!e[p]&&w&&w.mobile&&(e[p]=m),_&&"Macintosh"==e[u]&&r&&typeof r.standalone!==o&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[u]="iPad",e[p]=b),e},this.getEngine=function(){var e={};return e[d]=a,e[f]=a,G.call(e,y,v.engine),e},this.getOS=function(){var e={};return e[d]=a,e[f]=a,G.call(e,y,v.os),_&&!e[d]&&w&&"Unknown"!=w.platform&&(e[d]=w.platform.replace(/chrome os/i,L).replace(/macos/i,K)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return y},this.setUA=function(e){return y=typeof e===c&&e.length>350?V(e,350):e,this},this.setUA(y),this};if(Y.VERSION="1.0.35",Y.BROWSER=J([d,f,l]),Y.CPU=J([g]),Y.DEVICE=J([u,h,p,y,m,w,b,v,_]),Y.ENGINE=Y.OS=J([d,f]),typeof r!==o)t.exports&&(r=t.exports=Y),r.UAParser=Y;else if(typeof define===i&&define.amd)e.r,void 0!==Y&&e.v(Y);else typeof n!==o&&(n.UAParser=Y);var Q=typeof n!==o&&(n.jQuery||n.Zepto);if(Q&&!Q.ua){var Z=new Y;Q.ua=Z.getResult(),Q.ua.get=function(){return Z.getUA()},Q.ua.set=function(e){Z.setUA(e);var t=Z.getResult();for(var r in t)Q.ua[r]=t[r]}}}(this)}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;var i=n[e]={exports:{}},o=!0;try{r[e].call(i.exports,i,i.exports,a),o=!1}finally{o&&delete n[e]}return i.exports}a.ab="/ROOT/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/compiled/ua-parser-js/",t.exports=a(226)})()},50923,(e,t,r)=>{"use strict";var n={H:null,A:null};function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray;function o(){}var s=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),p=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),y=Symbol.iterator,m=Object.prototype.hasOwnProperty,b=Object.assign;function w(e,t,r){var n=r.ref;return{$$typeof:s,type:e,key:t,ref:void 0!==n?n:null,props:r}}function v(e){return"object"==typeof e&&null!==e&&e.$$typeof===s}var _=/\/+/g;function E(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function S(e,t,r){if(null==e)return e;var n=[],l=0;return!function e(t,r,n,l,u){var d,p,h,f=typeof t;("undefined"===f||"boolean"===f)&&(t=null);var m=!1;if(null===t)m=!0;else switch(f){case"bigint":case"string":case"number":m=!0;break;case"object":switch(t.$$typeof){case s:case c:m=!0;break;case g:return e((m=t._init)(t._payload),r,n,l,u)}}if(m)return u=u(t),m=""===l?"."+E(t,0):l,i(u)?(n="",null!=m&&(n=m.replace(_,"$&/")+"/"),e(u,r,n,"",function(e){return e})):null!=u&&(v(u)&&(d=u,p=n+(null==u.key||t&&t.key===u.key?"":(""+u.key).replace(_,"$&/")+"/")+m,u=w(d.type,p,d.props)),r.push(u)),1;m=0;var b=""===l?".":l+":";if(i(t))for(var S=0;S<t.length;S++)f=b+E(l=t[S],S),m+=e(l,r,n,f,u);else if("function"==typeof(S=null===(h=t)||"object"!=typeof h?null:"function"==typeof(h=y&&h[y]||h["@@iterator"])?h:null))for(t=S.call(t),S=0;!(l=t.next()).done;)f=b+E(l=l.value,S++),m+=e(l,r,n,f,u);else if("object"===f){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(o,o):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,n,l,u);throw Error(a(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return m}(e,n,"","",function(e){return t.call(r,e,l++)}),n}function x(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function A(){return new WeakMap}function R(){return{s:0,v:void 0,o:null,p:null}}r.Children={map:S,forEach:function(e,t,r){S(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return S(e,function(){t++}),t},toArray:function(e){return S(e,function(e){return e})||[]},only:function(e){if(!v(e))throw Error(a(143));return e}},r.Fragment=l,r.Profiler=d,r.StrictMode=u,r.Suspense=h,r.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=n,r.cache=function(e){return function(){var t=n.A;if(!t)return e.apply(null,arguments);var r=t.getCacheForType(A);void 0===(t=r.get(e))&&(t=R(),r.set(e,t)),r=0;for(var a=arguments.length;r<a;r++){var i=arguments[r];if("function"==typeof i||"object"==typeof i&&null!==i){var o=t.o;null===o&&(t.o=o=new WeakMap),void 0===(t=o.get(i))&&(t=R(),o.set(i,t))}else null===(o=t.p)&&(t.p=o=new Map),void 0===(t=o.get(i))&&(t=R(),o.set(i,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var s=e.apply(null,arguments);return(r=t).s=1,r.v=s}catch(e){throw(s=t).s=2,s.v=e,e}}},r.cacheSignal=function(){var e=n.A;return e?e.cacheSignal():null},r.captureOwnerStack=function(){return null},r.cloneElement=function(e,t,r){if(null==e)throw Error(a(267,e));var n=b({},e.props),i=e.key;if(null!=t)for(o in void 0!==t.key&&(i=""+t.key),t)m.call(t,o)&&"key"!==o&&"__self"!==o&&"__source"!==o&&("ref"!==o||void 0!==t.ref)&&(n[o]=t[o]);var o=arguments.length-2;if(1===o)n.children=r;else if(1<o){for(var s=Array(o),c=0;c<o;c++)s[c]=arguments[c+2];n.children=s}return w(e.type,i,n)},r.createElement=function(e,t,r){var n,a={},i=null;if(null!=t)for(n in void 0!==t.key&&(i=""+t.key),t)m.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(a[n]=t[n]);var o=arguments.length-2;if(1===o)a.children=r;else if(1<o){for(var s=Array(o),c=0;c<o;c++)s[c]=arguments[c+2];a.children=s}if(e&&e.defaultProps)for(n in o=e.defaultProps)void 0===a[n]&&(a[n]=o[n]);return w(e,i,a)},r.createRef=function(){return{current:null}},r.forwardRef=function(e){return{$$typeof:p,render:e}},r.isValidElement=v,r.lazy=function(e){return{$$typeof:g,_payload:{_status:-1,_result:e},_init:x}},r.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},r.use=function(e){return n.H.use(e)},r.useCallback=function(e,t){return n.H.useCallback(e,t)},r.useDebugValue=function(){},r.useId=function(){return n.H.useId()},r.useMemo=function(e,t){return n.H.useMemo(e,t)},r.version="19.2.0-canary-0bdb9206-20250818"},45210,(e,t,r)=>{"use strict";t.exports=e.r(50923)},22182,7156,13264,75768,65402,57476,34893,82303,72416,38953,64748,21947,39144,45205,26834,58291,64287,45506,9895,37844,e=>{"use strict";e.s(["PageSignatureError",()=>t,"RemovedPageError",()=>r,"RemovedUAError",()=>n],22182);class t extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class r extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class n extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}e.s(["fromNodeOutgoingHttpHeaders",()=>u,"normalizeNextQueryParam",()=>f,"splitCookiesString",()=>d,"toNodeOutgoingHttpHeaders",()=>p,"validateURL",()=>h],13264),e.s(["NEXT_CACHE_IMPLICIT_TAG_ID",()=>c,"NEXT_INTERCEPTION_MARKER_PREFIX",()=>i,"NEXT_QUERY_PARAM_PREFIX",()=>a,"PRERENDER_REVALIDATE_HEADER",()=>o,"PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER",()=>s],7156);let a="nxtP",i="nxtI",o="x-prerender-revalidate",s="x-prerender-revalidate-if-generated",c="_N_T_",l={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function u(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function d(e){var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}function p(e){let t={},r=[];if(e)for(let[n,a]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...d(a)),t[n]=1===r.length?r[0]:r):t[n]=a;return t}function h(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function f(e){for(let t of[a,i])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}function g(e){return e.replace(/\/$/,"")||"/"}function y(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function m(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:a}=y(e);return""+t+r+n+a}function b(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:a}=y(e);return""+r+t+n+a}function w(e,t){if("string"!=typeof e)return!1;let{pathname:r}=y(e);return r===t||r.startsWith(t+"/")}l.reactServerComponents,l.actionBrowser,l.reactServerComponents,l.actionBrowser,l.instrument,l.middleware,l.apiNode,l.apiEdge,l.serverSideRendering,l.appPagesBrowser,l.reactServerComponents,l.actionBrowser,l.serverSideRendering,l.appPagesBrowser,l.shared,l.instrument,l.middleware,l.reactServerComponents,l.serverSideRendering,l.appPagesBrowser,l.actionBrowser,e.s(["NextRequest",()=>T],57476),e.s(["NextURL",()=>A],75768);let v=new WeakMap;function _(e,t){let r;if(!t)return{pathname:e};let n=v.get(t);n||(n=t.map(e=>e.toLowerCase()),v.set(t,n));let a=e.split("/",2);if(!a[1])return{pathname:e};let i=a[1].toLowerCase(),o=n.indexOf(i);return o<0?{pathname:e}:(r=t[o],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let E=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function S(e,t){return new URL(String(e).replace(E,"localhost"),t&&String(t).replace(E,"localhost"))}let x=Symbol("NextURLInternal");class A{constructor(e,t,r){let n,a;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,a=r||{}):a=r||t||{},this[x]={url:S(e,n??a.base),options:a,basePath:""},this.analyze()}analyze(){var e,t,r,n,a;let i=function(e,t){var r,n;let{basePath:a,i18n:i,trailingSlash:o}=null!=(r=t.nextConfig)?r:{},s={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):o};a&&w(s.pathname,a)&&(s.pathname=function(e,t){if(!w(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(s.pathname,a),s.basePath=a);let c=s.pathname;if(s.pathname.startsWith("/_next/data/")&&s.pathname.endsWith(".json")){let e=s.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");s.buildId=e[0],c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(s.pathname=c)}if(i){let e=t.i18nProvider?t.i18nProvider.analyze(s.pathname):_(s.pathname,i.locales);s.locale=e.detectedLocale,s.pathname=null!=(n=e.pathname)?n:s.pathname,!e.detectedLocale&&s.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):_(c,i.locales)).detectedLocale&&(s.locale=e.detectedLocale)}return s}(this[x].url.pathname,{nextConfig:this[x].options.nextConfig,parseData:!0,i18nProvider:this[x].options.i18nProvider}),o=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[x].url,this[x].options.headers);this[x].domainLocale=this[x].options.i18nProvider?this[x].options.i18nProvider.detectDomainLocale(o):function(e,t,r){if(e)for(let i of(r&&(r=r.toLowerCase()),e)){var n,a;if(t===(null==(n=i.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===i.defaultLocale.toLowerCase()||(null==(a=i.locales)?void 0:a.some(e=>e.toLowerCase()===r)))return i}}(null==(t=this[x].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,o);let s=(null==(r=this[x].domainLocale)?void 0:r.defaultLocale)||(null==(a=this[x].options.nextConfig)||null==(n=a.i18n)?void 0:n.defaultLocale);this[x].url.pathname=i.pathname,this[x].defaultLocale=s,this[x].basePath=i.basePath??"",this[x].buildId=i.buildId,this[x].locale=i.locale??s,this[x].trailingSlash=i.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let a=e.toLowerCase();return!n&&(w(a,"/api")||w(a,"/"+t.toLowerCase()))?e:m(e,"/"+t)}((e={basePath:this[x].basePath,buildId:this[x].buildId,defaultLocale:this[x].options.forceLocale?void 0:this[x].defaultLocale,locale:this[x].locale,pathname:this[x].url.pathname,trailingSlash:this[x].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=g(t)),e.buildId&&(t=b(m(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=m(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:b(t,"/"):g(t)}formatSearch(){return this[x].url.search}get buildId(){return this[x].buildId}set buildId(e){this[x].buildId=e}get locale(){return this[x].locale??""}set locale(e){var t,r;if(!this[x].locale||!(null==(r=this[x].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[x].locale=e}get defaultLocale(){return this[x].defaultLocale}get domainLocale(){return this[x].domainLocale}get searchParams(){return this[x].url.searchParams}get host(){return this[x].url.host}set host(e){this[x].url.host=e}get hostname(){return this[x].url.hostname}set hostname(e){this[x].url.hostname=e}get port(){return this[x].url.port}set port(e){this[x].url.port=e}get protocol(){return this[x].url.protocol}set protocol(e){this[x].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[x].url=S(e),this.analyze()}get origin(){return this[x].url.origin}get pathname(){return this[x].url.pathname}set pathname(e){this[x].url.pathname=e}get hash(){return this[x].url.hash}set hash(e){this[x].url.hash=e}get search(){return this[x].url.search}set search(e){this[x].url.search=e}get password(){return this[x].url.password}set password(e){this[x].url.password=e}get username(){return this[x].url.username}set username(e){this[x].url.username=e}get basePath(){return this[x].basePath}set basePath(e){this[x].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new A(String(this),this[x].options)}}e.s([],65402);var R,C=e.i(1923);let P=Symbol("internal request");class T extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);h(r),e instanceof Request?super(e,t):super(r,t);let n=new A(r,{headers:p(this.headers),nextConfig:t.nextConfig});this[P]={cookies:new C.RequestCookies(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[P].cookies}get nextUrl(){return this[P].nextUrl}get page(){throw new r}get ua(){throw new n}get url(){return this[P].url}}e.s(["NextResponse",()=>H],82303),e.s(["ReflectAdapter",()=>O],34893);class O{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let k=Symbol("internal response"),N=new Set([301,302,303,307,308]);function I(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,a]of e.request.headers)t.set("x-middleware-request-"+n,a),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class H extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new C.ResponseCookies(r),{get(e,n,a){switch(n){case"delete":case"set":return(...a)=>{let i=Reflect.apply(e[n],e,a),o=new Headers(r);return i instanceof C.ResponseCookies&&r.set("x-middleware-set-cookie",i.getAll().map(e=>(0,C.stringifyCookie)(e)).join(",")),I(t,o),i};default:return O.get(e,n,a)}}});this[k]={cookies:n,url:t.url?new A(t.url,{headers:p(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[k].cookies}static json(e,t){let r=Response.json(e,t);return new H(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!N.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},a=new Headers(null==n?void 0:n.headers);return a.set("Location",h(e)),new H(null,{...n,headers:a,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",h(e)),I(t,r),new H(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),I(e,t),new H(null,{...e,headers:t})}}e.s(["FLIGHT_HEADERS",()=>U,"NEXT_HMR_REFRESH_HASH_COOKIE",()=>j,"NEXT_REWRITTEN_PATH_HEADER",()=>L,"NEXT_REWRITTEN_QUERY_HEADER",()=>K,"NEXT_ROUTER_PREFETCH_HEADER",()=>D,"NEXT_RSC_UNION_QUERY",()=>W,"RSC_HEADER",()=>M],72416);let M="rsc",D="next-router-prefetch",j="__next_hmr_refresh_hash__",U=[M,"next-router-state-tree",D,"next-hmr-refresh","next-router-segment-prefetch"],W="_rsc",L="x-nextjs-rewritten-path",K="x-nextjs-rewritten-query";e.s([],64748),e.s(["bindSnapshot",()=>V,"createAsyncLocalStorage",()=>q,"createSnapshot",()=>G],38953);let $=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class J{disable(){throw $}getStore(){}run(){throw $}exit(){throw $}enterWith(){throw $}static bind(e){return e}}let B="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function q(){return B?new B:new J}function V(e){return B?B.bind(e):J.bind(e)}function G(){return B?B.snapshot():function(e,...t){return e(...t)}}let z=q();e.s(["workAsyncStorage",()=>z],21947),e.s(["getRuntimeStagePromise",()=>Q,"throwForMissingRequestStore",()=>Y],45205);let F=q();e.s(["InvariantError",()=>X],39144);class X extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}function Y(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}function Q(e){switch(e.type){case"prerender-runtime":case"private-cache":return e.runtimeStagePromise;case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":case"request":case"cache":case"unstable-cache":return null;default:return e}}e.s(["workUnitAsyncStorage",()=>F],26834),e.s(["LRUCache",()=>et],58291);class Z{constructor(e,t,r){this.prev=null,this.next=null,this.key=e,this.data=t,this.size=r}}class ee{constructor(){this.prev=null,this.next=null}}class et{constructor(e,t){this.cache=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t,this.head=new ee,this.tail=new ee,this.head.next=this.tail,this.tail.prev=this.head}addToHead(e){e.prev=this.head,e.next=this.head.next,this.head.next.prev=e,this.head.next=e}removeNode(e){e.prev.next=e.next,e.next.prev=e.prev}moveToHead(e){this.removeNode(e),this.addToHead(e)}removeTail(){let e=this.tail.prev;return this.removeNode(e),e}set(e,t){let r=(null==this.calculateSize?void 0:this.calculateSize.call(this,t))??1;if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");let n=this.cache.get(e);if(n)n.data=t,this.totalSize=this.totalSize-n.size+r,n.size=r,this.moveToHead(n);else{let n=new Z(e,t,r);this.cache.set(e,n),this.addToHead(n),this.totalSize+=r}for(;this.totalSize>this.maxSize&&this.cache.size>0;){let e=this.removeTail();this.cache.delete(e.key),this.totalSize-=e.size}}has(e){return this.cache.has(e)}get(e){let t=this.cache.get(e);if(t)return this.moveToHead(t),t.data}*[Symbol.iterator](){let e=this.head.next;for(;e&&e!==this.tail;){let t=e;yield[t.key,t.data],e=e.next}}remove(e){let t=this.cache.get(e);t&&(this.removeNode(t),this.cache.delete(e),this.totalSize-=t.size)}get size(){return this.cache.size}get currentSize(){return this.totalSize}}e.s([],64287);let er=q();function en(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}e.s(["afterTaskAsyncStorage",()=>er],45506),e.s([],9895),e.s([],67151);var ea=e.i(62265);function ei(e){return{...(0,ea.default)(e),isBot:void 0!==e&&/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}}function eo({headers:e}){return ei(e.get("user-agent")||void 0)}let es="undefined"==typeof URLPattern?void 0:URLPattern;function ec(e){let t=z.getStore();if(!t)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:r}=t;return r.after(e)}var el=e.i(45210);class eu extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest="DYNAMIC_SERVER_USAGE"}}class ed extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}class ep extends Error{constructor(e,t){super(`During prerendering, ${t} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${t} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route "${e}".`),this.route=e,this.expression=t,this.digest="HANGING_PROMISE_REJECTION"}}let eh=new WeakMap;function ef(e,t,r){if(e.aborted)return Promise.reject(new ep(t,r));{let n=new Promise((n,a)=>{let i=a.bind(null,new ep(t,r)),o=eh.get(e);if(o)o.push(i);else{let t=[i];eh.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return n.catch(eg),n}}function eg(){}let ey="function"==typeof el.default.unstable_postpone;function em(e,t,r){let n=Object.defineProperty(new eu(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function eb(e,t,r){(function(){if(!ey)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),el.default.unstable_postpone(ew(e,t))}function ew(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(ew("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function ev(){let e=z.getStore(),t=F.getStore();if(e){if(t&&"after"===t.phase&&!function(){let e=er.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}())throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(e.forceStatic)return Promise.resolve(void 0);if(e.dynamicShouldError)throw Object.defineProperty(new ed(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(t)switch(t.type){case"cache":{let t=Object.defineProperty(Error(`Route ${e.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual request, but caches must be able to be produced before a request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E752",enumerable:!1,configurable:!0});throw Error.captureStackTrace(t,ev),e.invalidDynamicUsageError??=t,t}case"private-cache":{let t=Object.defineProperty(Error(`Route ${e.route} used "connection" inside "use cache: private". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual navigation request, but caches must be able to be produced before a navigation request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E753",enumerable:!1,configurable:!0});throw Error.captureStackTrace(t,ev),e.invalidDynamicUsageError??=t,t}case"unstable-cache":throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-runtime":return ef(t.renderSignal,e.route,"`connection()`");case"prerender-ppr":return eb(e.route,"connection",t.dynamicTracking);case"prerender-legacy":return em("connection",e,t);case"request":return!function(e){switch(e.type){case"cache":case"unstable-cache":case"private-cache":return}}(t),Promise.resolve(void 0)}}Y("connection")}RegExp(`\\n\\s+at Suspense \\(<anonymous>\\)(?:(?!\\n\\s+at (?:body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6) \\(<anonymous>\\))[\\s\\S])*?\\n\\s+at __next_root_layout_boundary__ \\([^\\n]*\\)`),RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`);let e_=/^[A-Za-z_$][A-Za-z0-9_$]*$/,eE=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"]);q();let{env:eS,stdout:ex}=(null==(R=globalThis)?void 0:R.process)??{},eA=eS&&!eS.NO_COLOR&&(eS.FORCE_COLOR||(null==ex?void 0:ex.isTTY)&&!eS.CI&&"dumb"!==eS.TERM),eR=(e,t,r,n)=>{let a=e.substring(0,n)+r,i=e.substring(n+t.length),o=i.indexOf(t);return~o?a+eR(i,t,r,o):a+i},eC=(e,t,r=e)=>eA?n=>{let a=""+n,i=a.indexOf(t,e.length);return~i?e+eR(a,t,r,i)+t:e+a+t}:String,eP=eC("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m");eC("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),eC("\x1b[3m","\x1b[23m"),eC("\x1b[4m","\x1b[24m"),eC("\x1b[7m","\x1b[27m"),eC("\x1b[8m","\x1b[28m"),eC("\x1b[9m","\x1b[29m"),eC("\x1b[30m","\x1b[39m");let eT=eC("\x1b[31m","\x1b[39m"),eO=eC("\x1b[32m","\x1b[39m"),ek=eC("\x1b[33m","\x1b[39m");eC("\x1b[34m","\x1b[39m");let eN=eC("\x1b[35m","\x1b[39m");eC("\x1b[38;2;173;127;168m","\x1b[39m"),eC("\x1b[36m","\x1b[39m");let eI=eC("\x1b[37m","\x1b[39m");eC("\x1b[90m","\x1b[39m"),eC("\x1b[40m","\x1b[49m"),eC("\x1b[41m","\x1b[49m"),eC("\x1b[42m","\x1b[49m"),eC("\x1b[43m","\x1b[49m"),eC("\x1b[44m","\x1b[49m"),eC("\x1b[45m","\x1b[49m"),eC("\x1b[46m","\x1b[49m"),eC("\x1b[47m","\x1b[49m");let eH={wait:eI(eP("○")),error:eT(eP("⨯")),warn:ek(eP("⚠")),ready:"▲",info:eI(eP(" ")),event:eO(eP("✓")),trace:eN(eP("»"))},eM={log:"log",warn:"warn",error:"error"},eD=new et(1e4,e=>e.length),ej=new WeakMap;async function eU(){!function(...e){let t=e.join(" ");eD.has(t)||(eD.set(t,t),function(...e){!function(e,...t){(""===t[0]||void 0===t[0])&&1===t.length&&t.shift();let r=e in eM?eM[e]:"log",n=eH[e];0===t.length?console[r](""):1===t.length&&"string"==typeof t[0]?console[r](" "+n+" "+t[0]):console[r](" "+n,...t)}("warn",...e)}(...e))}("`unstable_rootParams()` is deprecated and will be removed in an upcoming major release. Import specific root params from `next/root-params` instead.");let e=z.getStore();if(!e)throw Object.defineProperty(new X("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let t=F.getStore();if(!t)throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(t.type){case"cache":case"unstable-cache":throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return function(e,t,r){switch(r.type){case"prerender-client":{let e="`unstable_rootParams`";throw Object.defineProperty(new X(`${e} must not be used within a client component. Next.js should be preventing ${e} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0})}case"prerender":{let n=r.fallbackRouteParams;if(n){for(let a in e)if(n.has(a)){let n=ej.get(e);if(n)return n;let a=ef(r.renderSignal,t.route,"`unstable_rootParams`");return ej.set(e,a),a}}break}case"prerender-ppr":{let n=r.fallbackRouteParams;if(n){for(let a in e)if(n.has(a))return function(e,t,r,n){let a=ej.get(e);if(a)return a;let i={...e},o=Promise.resolve(i);return ej.set(e,o),Object.keys(e).forEach(a=>{eE.has(a)||(t.has(a)?Object.defineProperty(i,a,{get(){var e;let t=(e="unstable_rootParams",e_.test(a)?"`"+e+"."+a+"`":"`"+e+"["+JSON.stringify(a)+"]`");"prerender-ppr"===n.type?eb(r.route,t,n.dynamicTracking):em(t,r,n)},enumerable:!0}):o[a]=e[a])}),o}(e,n,t,r)}}}return Promise.resolve(e)}(t.rootParams,e,t);case"private-cache":case"prerender-runtime":case"request":return Promise.resolve(t.rootParams);default:return t}}e.s(["ImageResponse",()=>en,"NextRequest",()=>T,"NextResponse",()=>H,"URLPattern",()=>es,"after",()=>ec,"connection",()=>ev,"unstable_rootParams",()=>eU,"userAgent",()=>eo,"userAgentFromString",()=>ei],37844),e.i(67151)},70754,e=>{"use strict";e.s(["ImageResponse",()=>t.ImageResponse,"NextRequest",()=>t.NextRequest,"NextResponse",()=>t.NextResponse,"URLPattern",()=>t.URLPattern,"after",()=>t.after,"connection",()=>t.connection,"unstable_rootParams",()=>t.unstable_rootParams,"userAgent",()=>t.userAgent,"userAgentFromString",()=>t.userAgentFromString]),e.i(9895);var t=e.i(37844)},51600,e=>{"use strict";e.s(["CompactEncrypt",()=>tr,"CompactSign",()=>ti,"EmbeddedJWK",()=>tf,"EncryptJWT",()=>tu,"FlattenedEncrypt",()=>eX,"FlattenedSign",()=>ta,"GeneralEncrypt",()=>eQ,"GeneralSign",()=>ts,"SignJWT",()=>tl,"UnsecuredJWT",()=>tS,"base64url",()=>tN,"calculateJwkThumbprint",()=>tp,"calculateJwkThumbprintUri",()=>th,"compactDecrypt",()=>e$,"compactVerify",()=>e5,"createLocalJWKSet",()=>tw,"createRemoteJWKSet",()=>tE,"cryptoRuntime",()=>tI,"decodeJwt",()=>tA,"decodeProtectedHeader",()=>tx,"errors",()=>tR,"exportJWK",()=>eG,"exportPKCS8",()=>eV,"exportSPKI",()=>eq,"flattenedDecrypt",()=>eK,"flattenedVerify",()=>e2,"generalDecrypt",()=>eJ,"generalVerify",()=>e4,"generateKeyPair",()=>tO,"generateSecret",()=>tk,"importJWK",()=>ek,"importPKCS8",()=>eO,"importSPKI",()=>eP,"importX509",()=>eT,"jwtDecrypt",()=>tt,"jwtVerify",()=>te],51600),e.s([],76842),e.s(["JOSEAlgNotAllowed",()=>a,"JOSEError",()=>t,"JOSENotSupported",()=>i,"JWEDecompressionFailed",()=>s,"JWEDecryptionFailed",()=>o,"JWEInvalid",()=>c,"JWKInvalid",()=>d,"JWKSInvalid",()=>p,"JWKSMultipleMatchingKeys",()=>f,"JWKSNoMatchingKey",()=>h,"JWKSTimeout",()=>g,"JWSInvalid",()=>l,"JWSSignatureVerificationFailed",()=>y,"JWTClaimValidationFailed",()=>r,"JWTExpired",()=>n,"JWTInvalid",()=>u],4383);class t extends Error{static get code(){return"ERR_JOSE_GENERIC"}constructor(e){var t;super(e),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,null==(t=Error.captureStackTrace)||t.call(Error,this,this.constructor)}}class r extends t{static get code(){return"ERR_JWT_CLAIM_VALIDATION_FAILED"}constructor(e,t="unspecified",r="unspecified"){super(e),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=t,this.reason=r}}class n extends t{static get code(){return"ERR_JWT_EXPIRED"}constructor(e,t="unspecified",r="unspecified"){super(e),this.code="ERR_JWT_EXPIRED",this.claim=t,this.reason=r}}class a extends t{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}static get code(){return"ERR_JOSE_ALG_NOT_ALLOWED"}}class i extends t{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}static get code(){return"ERR_JOSE_NOT_SUPPORTED"}}class o extends t{constructor(){super(...arguments),this.code="ERR_JWE_DECRYPTION_FAILED",this.message="decryption operation failed"}static get code(){return"ERR_JWE_DECRYPTION_FAILED"}}class s extends t{constructor(){super(...arguments),this.code="ERR_JWE_DECOMPRESSION_FAILED",this.message="decompression operation failed"}static get code(){return"ERR_JWE_DECOMPRESSION_FAILED"}}class c extends t{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}static get code(){return"ERR_JWE_INVALID"}}class l extends t{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}static get code(){return"ERR_JWS_INVALID"}}class u extends t{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}static get code(){return"ERR_JWT_INVALID"}}class d extends t{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}static get code(){return"ERR_JWK_INVALID"}}class p extends t{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}static get code(){return"ERR_JWKS_INVALID"}}class h extends t{constructor(){super(...arguments),this.code="ERR_JWKS_NO_MATCHING_KEY",this.message="no applicable key found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_NO_MATCHING_KEY"}}class f extends t{constructor(){super(...arguments),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS",this.message="multiple matching keys found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}Symbol.asyncIterator;class g extends t{constructor(){super(...arguments),this.code="ERR_JWKS_TIMEOUT",this.message="request timed out"}static get code(){return"ERR_JWKS_TIMEOUT"}}class y extends t{constructor(){super(...arguments),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED",this.message="signature verification failed"}static get code(){return"ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}}var m=e.i(4383);e.s(["decode",()=>I,"encode",()=>N],23719);let b=crypto,w=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await b.subtle.digest(r,t))},v=new TextEncoder,_=new TextDecoder;function E(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}function S(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function x(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return S(r,t,0),S(r,e%0x100000000,4),r}function A(e){let t=new Uint8Array(4);return S(t,e),t}function R(e){return E(A(e.length),e)}async function C(e,t,r){let n=Math.ceil((t>>3)/32),a=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(A(t+1)),n.set(e,4),n.set(r,4+e.length),a.set(await w("sha256",n),32*t)}return a.slice(0,t>>3)}let P=e=>{let t=e;"string"==typeof t&&(t=v.encode(t));let r=[];for(let e=0;e<t.length;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join(""))},T=e=>P(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),O=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},k=e=>{let t=e;t instanceof Uint8Array&&(t=_.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return O(t)}catch(e){throw TypeError("The input to be decoded is not correctly encoded.")}},N=T,I=k;var H=e.i(23719);e.i(76842);let M=b.getRandomValues.bind(b);function D(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new i(`Unsupported JWE Algorithm: ${e}`)}}let j=e=>M(new Uint8Array(D(e)>>3)),U=(e,t)=>{if(t.length<<3!==D(e))throw new c("Invalid Initialization Vector length")},W=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new c(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function L(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function K(e,t){return e.name===t}function $(e){return parseInt(e.name.slice(4),10)}function J(e,t){if(t.length&&!t.some(t=>e.usages.includes(t))){let e="CryptoKey does not support this operation, its usages must include ";if(t.length>2){let r=t.pop();e+=`one of ${t.join(", ")}, or ${r}.`}else 2===t.length?e+=`one of ${t[0]} or ${t[1]}.`:e+=`${t[0]}.`;throw TypeError(e)}}function B(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!K(e.algorithm,"AES-GCM"))throw L("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw L(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!K(e.algorithm,"AES-KW"))throw L("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw L(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw L("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!K(e.algorithm,"PBKDF2"))throw L("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!K(e.algorithm,"RSA-OAEP"))throw L("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if($(e.algorithm.hash)!==r)throw L(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}J(e,r)}function q(e,t,...r){if(r.length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor&&t.constructor.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let V=(e,...t)=>q("Key must be ",e,...t);function G(e,t,...r){return q(`Key for the ${e} algorithm must be `,t,...r)}let z=["CryptoKey"];async function F(e,t,r,n,a,i){let s,c;if(!(t instanceof Uint8Array))throw TypeError(V(t,"Uint8Array"));let l=parseInt(e.slice(1,4),10),u=await b.subtle.importKey("raw",t.subarray(l>>3),"AES-CBC",!1,["decrypt"]),d=await b.subtle.importKey("raw",t.subarray(0,l>>3),{hash:`SHA-${l<<1}`,name:"HMAC"},!1,["sign"]),p=E(i,n,r,x(i.length<<3)),h=new Uint8Array((await b.subtle.sign("HMAC",d,p)).slice(0,l>>3));try{s=((e,t)=>{if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");if(e.length!==t.length)throw TypeError("Input buffers must have the same length");let r=e.length,n=0,a=-1;for(;++a<r;)n|=e[a]^t[a];return 0===n})(a,h)}catch(e){}if(!s)throw new o;try{c=new Uint8Array(await b.subtle.decrypt({iv:n,name:"AES-CBC"},u,r))}catch(e){}if(!c)throw new o;return c}async function X(e,t,r,n,a,i){let s;t instanceof Uint8Array?s=await b.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(B(t,e,"decrypt"),s=t);try{return new Uint8Array(await b.subtle.decrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},s,E(r,a)))}catch(e){throw new o}}let Y=async(e,t,r,n,a,o)=>{if(!(t instanceof CryptoKey)&&!(t instanceof Uint8Array))throw TypeError(V(t,...z,"Uint8Array"));switch(U(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&W(t,parseInt(e.slice(-3),10)),F(e,t,r,n,a,o);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&W(t,parseInt(e.slice(1,4),10)),X(e,t,r,n,a,o);default:throw new i("Unsupported JWE Content Encryption Algorithm")}},Q=async()=>{throw new i('JWE "zip" (Compression Algorithm) Header Parameter is not supported by your javascript runtime. You need to use the `inflateRaw` decrypt option to provide Inflate Raw implementation.')},Z=async()=>{throw new i('JWE "zip" (Compression Algorithm) Header Parameter is not supported by your javascript runtime. You need to use the `deflateRaw` encrypt option to provide Deflate Raw implementation.')},ee=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0};function et(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}let er=[{hash:"SHA-256",name:"HMAC"},!0,["sign"]];function en(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function ea(e,t,r){if(e instanceof CryptoKey)return B(e,t,r),e;if(e instanceof Uint8Array)return b.subtle.importKey("raw",e,"AES-KW",!0,[r]);throw TypeError(V(e,...z,"Uint8Array"))}let ei=async(e,t,r)=>{let n=await ea(t,e,"wrapKey");en(n,e);let a=await b.subtle.importKey("raw",r,...er);return new Uint8Array(await b.subtle.wrapKey("raw",a,n,"AES-KW"))},eo=async(e,t,r)=>{let n=await ea(t,e,"unwrapKey");en(n,e);let a=await b.subtle.unwrapKey("raw",r,n,"AES-KW",...er);return new Uint8Array(await b.subtle.exportKey("raw",a))};async function es(e,t,r,n,a=new Uint8Array(0),i=new Uint8Array(0)){let o;if(!(e instanceof CryptoKey))throw TypeError(V(e,...z));if(B(e,"ECDH"),!(t instanceof CryptoKey))throw TypeError(V(t,...z));B(t,"ECDH","deriveBits");let s=E(R(v.encode(r)),R(a),R(i),A(n));return o="X25519"===e.algorithm.name?256:"X448"===e.algorithm.name?448:Math.ceil(parseInt(e.algorithm.namedCurve.substr(-3),10)/8)<<3,C(new Uint8Array(await b.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}async function ec(e){if(!(e instanceof CryptoKey))throw TypeError(V(e,...z));return b.subtle.generateKey(e.algorithm,!0,["deriveBits"])}function el(e){if(!(e instanceof CryptoKey))throw TypeError(V(e,...z));return["P-256","P-384","P-521"].includes(e.algorithm.namedCurve)||"X25519"===e.algorithm.name||"X448"===e.algorithm.name}async function eu(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new c("PBES2 Salt Input must be 8 or more octets");let a=E(v.encode(t),new Uint8Array([0]),e),i=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:a},s=await function(e,t){if(e instanceof Uint8Array)return b.subtle.importKey("raw",e,"PBKDF2",!1,["deriveBits"]);if(e instanceof CryptoKey)return B(e,t,"deriveBits","deriveKey"),e;throw TypeError(V(e,...z,"Uint8Array"))}(n,t);if(s.usages.includes("deriveBits"))return new Uint8Array(await b.subtle.deriveBits(o,s,i));if(s.usages.includes("deriveKey"))return b.subtle.deriveKey(o,s,{length:i,name:"AES-KW"},!1,["wrapKey","unwrapKey"]);throw TypeError('PBKDF2 key "usages" must include "deriveBits" or "deriveKey"')}let ed=async(e,t,r,n=2048,a=M(new Uint8Array(16)))=>{let i=await eu(a,e,n,t);return{encryptedKey:await ei(e.slice(-6),i,r),p2c:n,p2s:T(a)}},ep=async(e,t,r,n,a)=>{let i=await eu(a,e,n,t);return eo(e.slice(-6),i,r)};function eh(e){switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new i(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}let ef=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},eg=async(e,t,r)=>{if(!(t instanceof CryptoKey))throw TypeError(V(t,...z));if(B(t,e,"encrypt","wrapKey"),ef(e,t),t.usages.includes("encrypt"))return new Uint8Array(await b.subtle.encrypt(eh(e),t,r));if(t.usages.includes("wrapKey")){let n=await b.subtle.importKey("raw",r,...er);return new Uint8Array(await b.subtle.wrapKey("raw",n,t,eh(e)))}throw TypeError('RSA-OAEP key "usages" must include "encrypt" or "wrapKey" for this operation')},ey=async(e,t,r)=>{if(!(t instanceof CryptoKey))throw TypeError(V(t,...z));if(B(t,e,"decrypt","unwrapKey"),ef(e,t),t.usages.includes("decrypt"))return new Uint8Array(await b.subtle.decrypt(eh(e),t,r));if(t.usages.includes("unwrapKey")){let n=await b.subtle.unwrapKey("raw",r,t,eh(e),...er);return new Uint8Array(await b.subtle.exportKey("raw",n))}throw TypeError('RSA-OAEP key "usages" must include "decrypt" or "unwrapKey" for this operation')};function em(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new i(`Unsupported JWE Algorithm: ${e}`)}}let eb=e=>M(new Uint8Array(em(e)>>3)),ew=(e,t)=>{let r=(e.match(/.{1,64}/g)||[]).join("\n");return`-----BEGIN ${t}-----
${r}
-----END ${t}-----`},ev=async(e,t,r)=>{if(!(r instanceof CryptoKey))throw TypeError(V(r,...z));if(!r.extractable)throw TypeError("CryptoKey is not extractable");if(r.type!==e)throw TypeError(`key is not a ${e} key`);return ew(P(new Uint8Array(await b.subtle.exportKey(t,r))),`${e.toUpperCase()} KEY`)},e_=(e,t,r=0)=>{0===r&&(t.unshift(t.length),t.unshift(6));let n=e.indexOf(t[0],r);if(-1===n)return!1;let a=e.subarray(n,n+t.length);return a.length===t.length&&(a.every((e,r)=>e===t[r])||e_(e,t,n+1))},eE=e=>{switch(!0){case e_(e,[42,134,72,206,61,3,1,7]):return"P-256";case e_(e,[43,129,4,0,34]):return"P-384";case e_(e,[43,129,4,0,35]):return"P-521";case e_(e,[43,101,110]):return"X25519";case e_(e,[43,101,111]):return"X448";case e_(e,[43,101,112]):return"Ed25519";case e_(e,[43,101,113]):return"Ed448";default:throw new i("Invalid or unsupported EC Key Curve or OKP Key Sub Type")}},eS=async(e,t,r,n,a)=>{var o;let s,c,l=new Uint8Array(atob(r.replace(e,"")).split("").map(e=>e.charCodeAt(0))),u="spki"===t;switch(n){case"PS256":case"PS384":case"PS512":s={name:"RSA-PSS",hash:`SHA-${n.slice(-3)}`},c=u?["verify"]:["sign"];break;case"RS256":case"RS384":case"RS512":s={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${n.slice(-3)}`},c=u?["verify"]:["sign"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s={name:"RSA-OAEP",hash:`SHA-${parseInt(n.slice(-3),10)||1}`},c=u?["encrypt","wrapKey"]:["decrypt","unwrapKey"];break;case"ES256":s={name:"ECDSA",namedCurve:"P-256"},c=u?["verify"]:["sign"];break;case"ES384":s={name:"ECDSA",namedCurve:"P-384"},c=u?["verify"]:["sign"];break;case"ES512":s={name:"ECDSA",namedCurve:"P-521"},c=u?["verify"]:["sign"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let e=eE(l);s=e.startsWith("P-")?{name:"ECDH",namedCurve:e}:{name:e},c=u?[]:["deriveBits"];break}case"EdDSA":s={name:eE(l)},c=u?["verify"]:["sign"];break;default:throw new i('Invalid or unsupported "alg" (Algorithm) value')}return b.subtle.importKey(t,l,s,null!=(o=null==a?void 0:a.extractable)&&o,c)},ex=(e,t,r)=>eS(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\s)/g,"spki",e,t,r);function eA(e){let t=[],r=0;for(;r<e.length;){let n=eR(e.subarray(r));t.push(n),r+=n.byteLength}return t}function eR(e){let t=0,r=31&e[0];if(t++,31===r){for(r=0;e[t]>=128;)r=128*r+e[t]-128,t++;r=128*r+e[t]-128,t++}let n=0;if(e[t]<128)n=e[t],t++;else if(128===n){for(n=0;0!==e[t+n]||0!==e[t+n+1];){if(n>e.byteLength)throw TypeError("invalid indefinite form length");n++}let r=t+n+2;return{byteLength:r,contents:e.subarray(t,t+n),raw:e.subarray(0,r)}}else{let r=127&e[t];t++,n=0;for(let a=0;a<r;a++)n=256*n+e[t],t++}let a=t+n;return{byteLength:a,contents:e.subarray(t,a),raw:e.subarray(0,a)}}let eC=async e=>{var t,r;if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:n,keyUsages:a}=function(e){let t,r;switch(e.kty){case"oct":switch(e.alg){case"HS256":case"HS384":case"HS512":t={name:"HMAC",hash:`SHA-${e.alg.slice(-3)}`},r=["sign","verify"];break;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":throw new i(`${e.alg} keys cannot be imported as CryptoKey instances`);case"A128GCM":case"A192GCM":case"A256GCM":case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":t={name:"AES-GCM"},r=["encrypt","decrypt"];break;case"A128KW":case"A192KW":case"A256KW":t={name:"AES-KW"},r=["wrapKey","unwrapKey"];break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":t={name:"PBKDF2"},r=["deriveBits"];break;default:throw new i('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new i('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new i('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"EdDSA":t={name:e.crv},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new i('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new i('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),o=[n,null!=(t=e.ext)&&t,null!=(r=e.key_ops)?r:a];if("PBKDF2"===n.name)return b.subtle.importKey("raw",k(e.k),...o);let s={...e};return delete s.alg,delete s.use,b.subtle.importKey("jwk",s,...o)};async function eP(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN PUBLIC KEY-----"))throw TypeError('"spki" must be SPKI formatted string');return ex(e,t,r)}async function eT(e,t,r){let n;if("string"!=typeof e||0!==e.indexOf("-----BEGIN CERTIFICATE-----"))throw TypeError('"x509" must be X.509 formatted string');try{n=ew(function(e){let t=eA(eA(eR(e).contents)[0].contents);return P(t[160===t[0].raw[0]?6:5].raw)}(O(e.replace(/(?:-----(?:BEGIN|END) CERTIFICATE-----|\s)/g,""))),"PUBLIC KEY")}catch(e){throw TypeError("Failed to parse the X.509 certificate",{cause:e})}return ex(n,t,r)}async function eO(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN PRIVATE KEY-----"))throw TypeError('"pkcs8" must be PKCS#8 formatted string');return eS(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\s)/g,"pkcs8",e,t,r)}async function ek(e,t,r){var n;if(!et(e))throw TypeError("JWK must be an object");switch(t||(t=e.alg),e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');if(null!=r||(r=!0!==e.ext),r)return eC({...e,alg:t,ext:null!=(n=e.ext)&&n});return k(e.k);case"RSA":if(void 0!==e.oth)throw new i('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return eC({...e,alg:t});default:throw new i('Unsupported "kty" (Key Type) Parameter value')}}let eN=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(e)?((e,t)=>{if(!(t instanceof Uint8Array)){if(!(t instanceof CryptoKey))throw TypeError(G(e,t,...z,"Uint8Array"));if("secret"!==t.type)throw TypeError(`${z.join(" or ")} instances for symmetric algorithms must be of type "secret"`)}})(e,t):((e,t,r)=>{if(!(t instanceof CryptoKey))throw TypeError(G(e,t,...z));if("secret"===t.type)throw TypeError(`${z.join(" or ")} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${z.join(" or ")} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${z.join(" or ")} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${z.join(" or ")} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${z.join(" or ")} instances for asymmetric algorithm encryption must be of type "public"`)})(e,t,r)};async function eI(e,t,r,n,a){if(!(r instanceof Uint8Array))throw TypeError(V(r,"Uint8Array"));let i=parseInt(e.slice(1,4),10),o=await b.subtle.importKey("raw",r.subarray(i>>3),"AES-CBC",!1,["encrypt"]),s=await b.subtle.importKey("raw",r.subarray(0,i>>3),{hash:`SHA-${i<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await b.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),l=E(a,n,c,x(a.length<<3));return{ciphertext:c,tag:new Uint8Array((await b.subtle.sign("HMAC",s,l)).slice(0,i>>3))}}async function eH(e,t,r,n,a){let i;r instanceof Uint8Array?i=await b.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(B(r,e,"encrypt"),i=r);let o=new Uint8Array(await b.subtle.encrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},i,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s}}let eM=async(e,t,r,n,a)=>{if(!(r instanceof CryptoKey)&&!(r instanceof Uint8Array))throw TypeError(V(r,...z,"Uint8Array"));switch(U(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&W(r,parseInt(e.slice(-3),10)),eI(e,t,r,n,a);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&W(r,parseInt(e.slice(1,4),10)),eH(e,t,r,n,a);default:throw new i("Unsupported JWE Content Encryption Algorithm")}};async function eD(e,t,r,n){let a=e.slice(0,7);n||(n=j(a));let{ciphertext:i,tag:o}=await eM(a,r,t,n,new Uint8Array(0));return{encryptedKey:i,iv:T(n),tag:T(o)}}async function ej(e,t,r,n,a){return Y(e.slice(0,7),t,r,n,a,new Uint8Array(0))}async function eU(e,t,r,n,a){switch(eN(e,t,"decrypt"),e){case"dir":if(void 0!==r)throw new c("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new c("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a,o;if(!et(n.epk))throw new c('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!el(t))throw new i("ECDH with the provided key is not allowed or not supported by your javascript runtime");let s=await ek(n.epk,e);if(void 0!==n.apu){if("string"!=typeof n.apu)throw new c('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{a=k(n.apu)}catch(e){throw new c("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new c('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{o=k(n.apv)}catch(e){throw new c("Failed to base64url decode the apv")}}let l=await es(s,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?em(n.enc):parseInt(e.slice(-5,-2),10),a,o);if("ECDH-ES"===e)return l;if(void 0===r)throw new c("JWE Encrypted Key missing");return eo(e.slice(-6),l,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new c("JWE Encrypted Key missing");return ey(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let i;if(void 0===r)throw new c("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new c('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=(null==a?void 0:a.maxPBES2Count)||1e4;if(n.p2c>o)throw new c('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new c('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{i=k(n.p2s)}catch(e){throw new c("Failed to base64url decode the p2s")}return ep(e,t,r,n.p2c,i)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new c("JWE Encrypted Key missing");return eo(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let a,i;if(void 0===r)throw new c("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new c('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new c('JOSE Header "tag" (Authentication Tag) missing or invalid');try{a=k(n.iv)}catch(e){throw new c("Failed to base64url decode the iv")}try{i=k(n.tag)}catch(e){throw new c("Failed to base64url decode the tag")}return ej(e,t,r,a,i)}default:throw new i('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let eW=function(e,t,r,n,a){let o;if(void 0!==a.crit&&void 0===n.crit)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let s of(o=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!o.has(s))throw new i(`Extension Header Parameter "${s}" is not recognized`);if(void 0===a[s])throw new e(`Extension Header Parameter "${s}" is missing`);if(o.get(s)&&void 0===n[s])throw new e(`Extension Header Parameter "${s}" MUST be integrity protected`)}return new Set(n.crit)},eL=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function eK(e,t,r){var n;let o,s,l,u,d,p,h;if(!et(e))throw new c("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new c("JOSE Header missing");if("string"!=typeof e.iv)throw new c("JWE Initialization Vector missing or incorrect type");if("string"!=typeof e.ciphertext)throw new c("JWE Ciphertext missing or incorrect type");if("string"!=typeof e.tag)throw new c("JWE Authentication Tag missing or incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new c("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new c("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new c("JWE AAD incorrect type");if(void 0!==e.header&&!et(e.header))throw new c("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!et(e.unprotected))throw new c("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=k(e.protected);o=JSON.parse(_.decode(t))}catch(e){throw new c("JWE Protected Header is invalid")}if(!ee(o,e.header,e.unprotected))throw new c("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let f={...o,...e.header,...e.unprotected};if(eW(c,new Map,null==r?void 0:r.crit,o,f),void 0!==f.zip){if(!o||!o.zip)throw new c('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==f.zip)throw new i('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:g,enc:y}=f;if("string"!=typeof g||!g)throw new c("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof y||!y)throw new c("missing JWE Encryption Algorithm (enc) in JWE Header");let m=r&&eL("keyManagementAlgorithms",r.keyManagementAlgorithms),b=r&&eL("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(m&&!m.has(g))throw new a('"alg" (Algorithm) Header Parameter not allowed');if(b&&!b.has(y))throw new a('"enc" (Encryption Algorithm) Header Parameter not allowed');if(void 0!==e.encrypted_key)try{s=k(e.encrypted_key)}catch(e){throw new c("Failed to base64url decode the encrypted_key")}let w=!1;"function"==typeof t&&(t=await t(o,e),w=!0);try{l=await eU(g,t,s,f,r)}catch(e){if(e instanceof TypeError||e instanceof c||e instanceof i)throw e;l=eb(y)}try{u=k(e.iv)}catch(e){throw new c("Failed to base64url decode the iv")}try{d=k(e.tag)}catch(e){throw new c("Failed to base64url decode the tag")}let S=v.encode(null!=(n=e.protected)?n:"");p=void 0!==e.aad?E(S,v.encode("."),v.encode(e.aad)):S;try{h=k(e.ciphertext)}catch(e){throw new c("Failed to base64url decode the ciphertext")}let x=await Y(y,l,h,u,d,p);"DEF"===f.zip&&(x=await ((null==r?void 0:r.inflateRaw)||Q)(x));let A={plaintext:x};if(void 0!==e.protected&&(A.protectedHeader=o),void 0!==e.aad)try{A.additionalAuthenticatedData=k(e.aad)}catch(e){throw new c("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(A.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(A.unprotectedHeader=e.header),w)?{...A,key:t}:A}async function e$(e,t,r){if(e instanceof Uint8Array&&(e=_.decode(e)),"string"!=typeof e)throw new c("Compact JWE must be a string or Uint8Array");let{0:n,1:a,2:i,3:o,4:s,length:l}=e.split(".");if(5!==l)throw new c("Invalid Compact JWE");let u=await eK({ciphertext:o,iv:i||void 0,protected:n||void 0,tag:s||void 0,encrypted_key:a||void 0},t,r),d={plaintext:u.plaintext,protectedHeader:u.protectedHeader};return"function"==typeof t?{...d,key:u.key}:d}async function eJ(e,t,r){if(!et(e))throw new c("General JWE must be an object");if(!Array.isArray(e.recipients)||!e.recipients.every(et))throw new c("JWE Recipients missing or incorrect type");if(!e.recipients.length)throw new c("JWE Recipients has no members");for(let n of e.recipients)try{return await eK({aad:e.aad,ciphertext:e.ciphertext,encrypted_key:n.encrypted_key,header:n.header,iv:e.iv,protected:e.protected,tag:e.tag,unprotected:e.unprotected},t,r)}catch(e){}throw new o}let eB=async e=>{if(e instanceof Uint8Array)return{kty:"oct",k:T(e)};if(!(e instanceof CryptoKey))throw TypeError(V(e,...z,"Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:a,...i}=await b.subtle.exportKey("jwk",e);return i};async function eq(e){return ev("public","spki",e)}async function eV(e){return ev("private","pkcs8",e)}async function eG(e){return eB(e)}async function ez(e,t,r,n,a={}){let o,s,c;switch(eN(e,r,"encrypt"),e){case"dir":c=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!el(r))throw new i("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:u}=a,{epk:d}=a;d||(d=(await ec(r)).privateKey);let{x:p,y:h,crv:f,kty:g}=await eG(d),y=await es(r,d,"ECDH-ES"===e?t:e,"ECDH-ES"===e?em(t):parseInt(e.slice(-5,-2),10),l,u);if(s={epk:{x:p,crv:f,kty:g}},"EC"===g&&(s.epk.y=h),l&&(s.apu=T(l)),u&&(s.apv=T(u)),"ECDH-ES"===e){c=y;break}c=n||eb(t);let m=e.slice(-6);o=await ei(m,y,c);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":c=n||eb(t),o=await eg(e,r,c);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{c=n||eb(t);let{p2c:i,p2s:l}=a;({encryptedKey:o,...s}=await ed(e,r,c,i,l));break}case"A128KW":case"A192KW":case"A256KW":c=n||eb(t),o=await ei(e,r,c);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{c=n||eb(t);let{iv:i}=a;({encryptedKey:o,...s}=await eD(e,r,c,i));break}default:throw new i('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:c,encryptedKey:o,parameters:s}}let eF=Symbol();class eX{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,t){let r,n,a,o,s,l,u;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new c("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!ee(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new c("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let d={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(eW(c,new Map,null==t?void 0:t.crit,this._protectedHeader,d),void 0!==d.zip){if(!this._protectedHeader||!this._protectedHeader.zip)throw new c('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==d.zip)throw new i('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:p,enc:h}=d;if("string"!=typeof p||!p)throw new c('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof h||!h)throw new c('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if("dir"===p){if(this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Encryption")}else if("ECDH-ES"===p&&this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Key Agreement");{let a;({cek:n,encryptedKey:r,parameters:a}=await ez(p,h,e,this._cek,this._keyManagementParameters)),a&&(t&&eF in t?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...a}:this.setUnprotectedHeader(a):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...a}:this.setProtectedHeader(a))}if(this._iv||(this._iv=j(h)),o=this._protectedHeader?v.encode(T(JSON.stringify(this._protectedHeader))):v.encode(""),this._aad?(s=T(this._aad),a=E(o,v.encode("."),v.encode(s))):a=o,"DEF"===d.zip){let e=await ((null==t?void 0:t.deflateRaw)||Z)(this._plaintext);({ciphertext:l,tag:u}=await eM(h,e,n,this._iv,a))}else({ciphertext:l,tag:u}=await eM(h,this._plaintext,n,this._iv,a));let f={ciphertext:T(l),iv:T(this._iv),tag:T(u)};return r&&(f.encrypted_key=T(r)),s&&(f.aad=s),this._protectedHeader&&(f.protected=_.decode(o)),this._sharedUnprotectedHeader&&(f.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(f.header=this._unprotectedHeader),f}}class eY{constructor(e,t,r){this.parent=e,this.key=t,this.options=r}setUnprotectedHeader(e){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=e,this}addRecipient(...e){return this.parent.addRecipient(...e)}encrypt(...e){return this.parent.encrypt(...e)}done(){return this.parent}}class eQ{constructor(e){this._recipients=[],this._plaintext=e}addRecipient(e,t){let r=new eY(this,e,{crit:null==t?void 0:t.crit});return this._recipients.push(r),r}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}async encrypt(e){var t,r,n;let a;if(!this._recipients.length)throw new c("at least one recipient must be added");if(e={deflateRaw:null==e?void 0:e.deflateRaw},1===this._recipients.length){let[t]=this._recipients,r=await new eX(this._plaintext).setAdditionalAuthenticatedData(this._aad).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(t.unprotectedHeader).encrypt(t.key,{...t.options,...e}),n={ciphertext:r.ciphertext,iv:r.iv,recipients:[{}],tag:r.tag};return r.aad&&(n.aad=r.aad),r.protected&&(n.protected=r.protected),r.unprotected&&(n.unprotected=r.unprotected),r.encrypted_key&&(n.recipients[0].encrypted_key=r.encrypted_key),r.header&&(n.recipients[0].header=r.header),n}for(let e=0;e<this._recipients.length;e++){let t=this._recipients[e];if(!ee(this._protectedHeader,this._unprotectedHeader,t.unprotectedHeader))throw new c("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let r={...this._protectedHeader,...this._unprotectedHeader,...t.unprotectedHeader},{alg:n}=r;if("string"!=typeof n||!n)throw new c('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("dir"===n||"ECDH-ES"===n)throw new c('"dir" and "ECDH-ES" alg may only be used with a single recipient');if("string"!=typeof r.enc||!r.enc)throw new c('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(a){if(a!==r.enc)throw new c('JWE "enc" (Encryption Algorithm) Header Parameter must be the same for all recipients')}else a=r.enc;if(eW(c,new Map,t.options.crit,this._protectedHeader,r),void 0!==r.zip&&(!this._protectedHeader||!this._protectedHeader.zip))throw new c('JWE "zip" (Compression Algorithm) Header MUST be integrity protected')}let i=eb(a),o={ciphertext:"",iv:"",recipients:[],tag:""};for(let s=0;s<this._recipients.length;s++){let c=this._recipients[s],l={};o.recipients.push(l);let u=({...this._protectedHeader,...this._unprotectedHeader,...c.unprotectedHeader}).alg.startsWith("PBES2")?2048+s:void 0;if(0===s){let t=await new eX(this._plaintext).setAdditionalAuthenticatedData(this._aad).setContentEncryptionKey(i).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(c.unprotectedHeader).setKeyManagementParameters({p2c:u}).encrypt(c.key,{...c.options,...e,[eF]:!0});o.ciphertext=t.ciphertext,o.iv=t.iv,o.tag=t.tag,t.aad&&(o.aad=t.aad),t.protected&&(o.protected=t.protected),t.unprotected&&(o.unprotected=t.unprotected),l.encrypted_key=t.encrypted_key,t.header&&(l.header=t.header);continue}let{encryptedKey:d,parameters:p}=await ez((null==(t=c.unprotectedHeader)?void 0:t.alg)||(null==(r=this._protectedHeader)?void 0:r.alg)||(null==(n=this._unprotectedHeader)?void 0:n.alg),a,c.key,i,{p2c:u});l.encrypted_key=T(d),(c.unprotectedHeader||p)&&(l.header={...c.unprotectedHeader,...p})}return o}}function eZ(e,t){let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:e.slice(-3)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"EdDSA":return{name:t.name};default:throw new i(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}function e0(e,t,r){if(t instanceof CryptoKey)return!function(e,t,...r){switch(t){case"HS256":case"HS384":case"HS512":{if(!K(e.algorithm,"HMAC"))throw L("HMAC");let r=parseInt(t.slice(2),10);if($(e.algorithm.hash)!==r)throw L(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!K(e.algorithm,"RSASSA-PKCS1-v1_5"))throw L("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if($(e.algorithm.hash)!==r)throw L(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!K(e.algorithm,"RSA-PSS"))throw L("RSA-PSS");let r=parseInt(t.slice(2),10);if($(e.algorithm.hash)!==r)throw L(`SHA-${r}`,"algorithm.hash");break}case"EdDSA":if("Ed25519"!==e.algorithm.name&&"Ed448"!==e.algorithm.name)throw L("Ed25519 or Ed448");break;case"ES256":case"ES384":case"ES512":{if(!K(e.algorithm,"ECDSA"))throw L("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw L(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}J(e,r)}(t,e,r),t;if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(V(t,...z));return b.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}throw TypeError(V(t,...z,"Uint8Array"))}let e1=async(e,t,r,n)=>{let a=await e0(e,t,"verify");ef(e,a);let i=eZ(e,a.algorithm);try{return await b.subtle.verify(i,a,r,n)}catch(e){return!1}};async function e2(e,t,r){var n;let i,o;if(!et(e))throw new l("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new l('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new l("JWS Protected Header incorrect type");if(void 0===e.payload)throw new l("JWS Payload missing");if("string"!=typeof e.signature)throw new l("JWS Signature missing or incorrect type");if(void 0!==e.header&&!et(e.header))throw new l("JWS Unprotected Header incorrect type");let s={};if(e.protected)try{let t=k(e.protected);s=JSON.parse(_.decode(t))}catch(e){throw new l("JWS Protected Header is invalid")}if(!ee(s,e.header))throw new l("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let c={...s,...e.header},u=eW(l,new Map([["b64",!0]]),null==r?void 0:r.crit,s,c),d=!0;if(u.has("b64")&&"boolean"!=typeof(d=s.b64))throw new l('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:p}=c;if("string"!=typeof p||!p)throw new l('JWS "alg" (Algorithm) Header Parameter missing or invalid');let h=r&&eL("algorithms",r.algorithms);if(h&&!h.has(p))throw new a('"alg" (Algorithm) Header Parameter not allowed');if(d){if("string"!=typeof e.payload)throw new l("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new l("JWS Payload must be a string or an Uint8Array instance");let f=!1;"function"==typeof t&&(t=await t(s,e),f=!0),eN(p,t,"verify");let g=E(v.encode(null!=(n=e.protected)?n:""),v.encode("."),"string"==typeof e.payload?v.encode(e.payload):e.payload);try{i=k(e.signature)}catch(e){throw new l("Failed to base64url decode the signature")}if(!await e1(p,t,i,g))throw new y;if(d)try{o=k(e.payload)}catch(e){throw new l("Failed to base64url decode the payload")}else o="string"==typeof e.payload?v.encode(e.payload):e.payload;let m={payload:o};return(void 0!==e.protected&&(m.protectedHeader=s),void 0!==e.header&&(m.unprotectedHeader=e.header),f)?{...m,key:t}:m}async function e5(e,t,r){if(e instanceof Uint8Array&&(e=_.decode(e)),"string"!=typeof e)throw new l("Compact JWS must be a string or Uint8Array");let{0:n,1:a,2:i,length:o}=e.split(".");if(3!==o)throw new l("Invalid Compact JWS");let s=await e2({payload:a,protected:n,signature:i},t,r),c={payload:s.payload,protectedHeader:s.protectedHeader};return"function"==typeof t?{...c,key:s.key}:c}async function e4(e,t,r){if(!et(e))throw new l("General JWS must be an object");if(!Array.isArray(e.signatures)||!e.signatures.every(et))throw new l("JWS Signatures missing or incorrect type");for(let n of e.signatures)try{return await e2({header:n.header,payload:e.payload,protected:n.protected,signature:n.signature},t,r)}catch(e){}throw new y}let e6=e=>Math.floor(e.getTime()/1e3),e3=/^(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)$/i,e8=e=>{let t=e3.exec(e);if(!t)throw TypeError("Invalid time period format");let r=parseFloat(t[1]);switch(t[2].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":return Math.round(r);case"minute":case"minutes":case"min":case"mins":case"m":return Math.round(60*r);case"hour":case"hours":case"hr":case"hrs":case"h":return Math.round(3600*r);case"day":case"days":case"d":return Math.round(86400*r);case"week":case"weeks":case"w":return Math.round(604800*r);default:return Math.round(0x1e187e0*r)}},e9=e=>e.toLowerCase().replace(/^application\//,""),e7=(e,t,a={})=>{let i,o,{typ:s}=a;if(s&&("string"!=typeof e.typ||e9(e.typ)!==e9(s)))throw new r('unexpected "typ" JWT header value',"typ","check_failed");try{i=JSON.parse(_.decode(t))}catch(e){}if(!et(i))throw new u("JWT Claims Set must be a top-level JSON object");let{requiredClaims:c=[],issuer:l,subject:d,audience:p,maxTokenAge:h}=a;for(let e of(void 0!==h&&c.push("iat"),void 0!==p&&c.push("aud"),void 0!==d&&c.push("sub"),void 0!==l&&c.push("iss"),new Set(c.reverse())))if(!(e in i))throw new r(`missing required "${e}" claim`,e,"missing");if(l&&!(Array.isArray(l)?l:[l]).includes(i.iss))throw new r('unexpected "iss" claim value',"iss","check_failed");if(d&&i.sub!==d)throw new r('unexpected "sub" claim value',"sub","check_failed");if(p&&!((e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))))(i.aud,"string"==typeof p?[p]:p))throw new r('unexpected "aud" claim value',"aud","check_failed");switch(typeof a.clockTolerance){case"string":o=e8(a.clockTolerance);break;case"number":o=a.clockTolerance;break;case"undefined":o=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=a,g=e6(f||new Date);if((void 0!==i.iat||h)&&"number"!=typeof i.iat)throw new r('"iat" claim must be a number',"iat","invalid");if(void 0!==i.nbf){if("number"!=typeof i.nbf)throw new r('"nbf" claim must be a number',"nbf","invalid");if(i.nbf>g+o)throw new r('"nbf" claim timestamp check failed',"nbf","check_failed")}if(void 0!==i.exp){if("number"!=typeof i.exp)throw new r('"exp" claim must be a number',"exp","invalid");if(i.exp<=g-o)throw new n('"exp" claim timestamp check failed',"exp","check_failed")}if(h){let e=g-i.iat;if(e-o>("number"==typeof h?h:e8(h)))throw new n('"iat" claim timestamp check failed (too far in the past)',"iat","check_failed");if(e<0-o)throw new r('"iat" claim timestamp check failed (it should be in the past)',"iat","check_failed")}return i};async function te(e,t,r){var n;let a=await e5(e,t,r);if((null==(n=a.protectedHeader.crit)?void 0:n.includes("b64"))&&!1===a.protectedHeader.b64)throw new u("JWTs MUST NOT use unencoded payload");let i={payload:e7(a.protectedHeader,a.payload,r),protectedHeader:a.protectedHeader};return"function"==typeof t?{...i,key:a.key}:i}async function tt(e,t,n){let a=await e$(e,t,n),i=e7(a.protectedHeader,a.plaintext,n),{protectedHeader:o}=a;if(void 0!==o.iss&&o.iss!==i.iss)throw new r('replicated "iss" claim header parameter mismatch',"iss","mismatch");if(void 0!==o.sub&&o.sub!==i.sub)throw new r('replicated "sub" claim header parameter mismatch',"sub","mismatch");if(void 0!==o.aud&&JSON.stringify(o.aud)!==JSON.stringify(i.aud))throw new r('replicated "aud" claim header parameter mismatch',"aud","mismatch");let s={payload:i,protectedHeader:o};return"function"==typeof t?{...s,key:a.key}:s}class tr{constructor(e){this._flattened=new eX(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tn=async(e,t,r)=>{let n=await e0(e,t,"sign");return ef(e,n),new Uint8Array(await b.subtle.sign(eZ(e,n.algorithm),n,r))};class ta{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,t){let r;if(!this._protectedHeader&&!this._unprotectedHeader)throw new l("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!ee(this._protectedHeader,this._unprotectedHeader))throw new l("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let n={...this._protectedHeader,...this._unprotectedHeader},a=eW(l,new Map([["b64",!0]]),null==t?void 0:t.crit,this._protectedHeader,n),i=!0;if(a.has("b64")&&"boolean"!=typeof(i=this._protectedHeader.b64))throw new l('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:o}=n;if("string"!=typeof o||!o)throw new l('JWS "alg" (Algorithm) Header Parameter missing or invalid');eN(o,e,"sign");let s=this._payload;i&&(s=v.encode(T(s)));let c=E(r=this._protectedHeader?v.encode(T(JSON.stringify(this._protectedHeader))):v.encode(""),v.encode("."),s),u={signature:T(await tn(o,e,c)),payload:""};return i&&(u.payload=_.decode(s)),this._unprotectedHeader&&(u.header=this._unprotectedHeader),this._protectedHeader&&(u.protected=_.decode(r)),u}}class ti{constructor(e){this._flattened=new ta(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,t){let r=await this._flattened.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}class to{constructor(e,t,r){this.parent=e,this.key=t,this.options=r}setProtectedHeader(e){if(this.protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this.protectedHeader=e,this}setUnprotectedHeader(e){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=e,this}addSignature(...e){return this.parent.addSignature(...e)}sign(...e){return this.parent.sign(...e)}done(){return this.parent}}class ts{constructor(e){this._signatures=[],this._payload=e}addSignature(e,t){let r=new to(this,e,t);return this._signatures.push(r),r}async sign(){if(!this._signatures.length)throw new l("at least one signature must be added");let e={signatures:[],payload:""};for(let t=0;t<this._signatures.length;t++){let r=this._signatures[t],n=new ta(this._payload);n.setProtectedHeader(r.protectedHeader),n.setUnprotectedHeader(r.unprotectedHeader);let{payload:a,...i}=await n.sign(r.key,r.options);if(0===t)e.payload=a;else if(e.payload!==a)throw new l("inconsistent use of JWS Unencoded Payload (RFC7797)");e.signatures.push(i)}return e}}class tc{constructor(e){if(!et(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:e}:this._payload={...this._payload,nbf:e6(new Date)+e8(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:e}:this._payload={...this._payload,exp:e6(new Date)+e8(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:e6(new Date)}:this._payload={...this._payload,iat:e},this}}class tl extends tc{setProtectedHeader(e){return this._protectedHeader=e,this}async sign(e,t){var r;let n=new ti(v.encode(JSON.stringify(this._payload)));if(n.setProtectedHeader(this._protectedHeader),Array.isArray(null==(r=this._protectedHeader)?void 0:r.crit)&&this._protectedHeader.crit.includes("b64")&&!1===this._protectedHeader.b64)throw new u("JWTs MUST NOT use unencoded payload");return n.sign(e,t)}}class tu extends tc{setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new tr(v.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}let td=(e,t)=>{if("string"!=typeof e||!e)throw new d(`${t} missing or invalid`)};async function tp(e,t){let r;if(!et(e))throw TypeError("JWK must be an object");if(null!=t||(t="sha256"),"sha256"!==t&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":td(e.crv,'"crv" (Curve) Parameter'),td(e.x,'"x" (X Coordinate) Parameter'),td(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":td(e.crv,'"crv" (Subtype of Key Pair) Parameter'),td(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":td(e.e,'"e" (Exponent) Parameter'),td(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":td(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new i('"kty" (Key Type) Parameter missing or unsupported')}let n=v.encode(JSON.stringify(r));return T(await w(t,n))}async function th(e,t){null!=t||(t="sha256");let r=await tp(e,t);return`urn:ietf:params:oauth:jwk-thumbprint:sha-${t.slice(-3)}:${r}`}async function tf(e,t){let r={...e,...null==t?void 0:t.header};if(!et(r.jwk))throw new l('"jwk" (JSON Web Key) Header Parameter must be a JSON object');let n=await ek({...r.jwk,ext:!0},r.alg,!0);if(n instanceof Uint8Array||"public"!==n.type)throw new l('"jwk" (JSON Web Key) Header Parameter must be a public key');return n}function tg(e){return e&&"object"==typeof e&&Array.isArray(e.keys)&&e.keys.every(ty)}function ty(e){return et(e)}class tm{constructor(e){if(this._cached=new WeakMap,!tg(e))throw new p("JSON Web Key Set malformed");this._jwks=function(e){return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}(e)}async getKey(e,t){let{alg:r,kid:n}={...e,...null==t?void 0:t.header},a=function(e){switch("string"==typeof e&&e.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:throw new i('Unsupported "alg" value for a JSON Web Key Set')}}(r),o=this._jwks.keys.filter(e=>{let t=a===e.kty;if(t&&"string"==typeof n&&(t=n===e.kid),t&&"string"==typeof e.alg&&(t=r===e.alg),t&&"string"==typeof e.use&&(t="sig"===e.use),t&&Array.isArray(e.key_ops)&&(t=e.key_ops.includes("verify")),t&&"EdDSA"===r&&(t="Ed25519"===e.crv||"Ed448"===e.crv),t)switch(r){case"ES256":t="P-256"===e.crv;break;case"ES256K":t="secp256k1"===e.crv;break;case"ES384":t="P-384"===e.crv;break;case"ES512":t="P-521"===e.crv}return t}),{0:s,length:c}=o;if(0===c)throw new h;if(1!==c){let e=new f,{_cached:t}=this;throw e[Symbol.asyncIterator]=async function*(){for(let e of o)try{yield await tb(t,e,r)}catch(e){continue}},e}return tb(this._cached,s,r)}}async function tb(e,t,r){let n=e.get(t)||e.set(t,{}).get(t);if(void 0===n[r]){let e=await ek({...t,ext:!0},r);if(e instanceof Uint8Array||"public"!==e.type)throw new p("JSON Web Key Set members must be public keys");n[r]=e}return n[r]}function tw(e){let t=new tm(e);return async function(e,r){return t.getKey(e,r)}}let tv=async(e,r,n)=>{let a,i,o=!1;"function"==typeof AbortController&&(a=new AbortController,i=setTimeout(()=>{o=!0,a.abort()},r));let s=await fetch(e.href,{signal:a?a.signal:void 0,redirect:"manual",headers:n.headers}).catch(e=>{if(o)throw new g;throw e});if(void 0!==i&&clearTimeout(i),200!==s.status)throw new t("Expected 200 OK from the JSON Web Key Set HTTP response");try{return await s.json()}catch(e){throw new t("Failed to parse the JSON Web Key Set HTTP response as JSON")}};class t_ extends tm{constructor(e,t){if(super({keys:[]}),this._jwks=void 0,!(e instanceof URL))throw TypeError("url must be an instance of URL");this._url=new URL(e.href),this._options={agent:null==t?void 0:t.agent,headers:null==t?void 0:t.headers},this._timeoutDuration="number"==typeof(null==t?void 0:t.timeoutDuration)?null==t?void 0:t.timeoutDuration:5e3,this._cooldownDuration="number"==typeof(null==t?void 0:t.cooldownDuration)?null==t?void 0:t.cooldownDuration:3e4,this._cacheMaxAge="number"==typeof(null==t?void 0:t.cacheMaxAge)?null==t?void 0:t.cacheMaxAge:6e5}coolingDown(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cooldownDuration}fresh(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cacheMaxAge}async getKey(e,t){this._jwks&&this.fresh()||await this.reload();try{return await super.getKey(e,t)}catch(r){if(r instanceof h&&!1===this.coolingDown())return await this.reload(),super.getKey(e,t);throw r}}async reload(){this._pendingFetch&&("undefined"!=typeof WebSocketPair||"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent)&&(this._pendingFetch=void 0),this._pendingFetch||(this._pendingFetch=tv(this._url,this._timeoutDuration,this._options).then(e=>{if(!tg(e))throw new p("JSON Web Key Set malformed");this._jwks={keys:e.keys},this._jwksTimestamp=Date.now(),this._pendingFetch=void 0}).catch(e=>{throw this._pendingFetch=void 0,e})),await this._pendingFetch}}function tE(e,t){let r=new t_(e,t);return async function(e,t){return r.getKey(e,t)}}class tS extends tc{encode(){let e=T(JSON.stringify({alg:"none"})),t=T(JSON.stringify(this._payload));return`${e}.${t}.`}static decode(e,t){let r;if("string"!=typeof e)throw new u("Unsecured JWT must be a string");let{0:n,1:a,2:i,length:o}=e.split(".");if(3!==o||""!==i)throw new u("Invalid Unsecured JWT");try{if(r=JSON.parse(_.decode(k(n))),"none"!==r.alg)throw Error()}catch(e){throw new u("Invalid Unsecured JWT")}return{payload:e7(r,k(a),t),header:r}}}function tx(e){let t;if("string"==typeof e){let r=e.split(".");(3===r.length||5===r.length)&&([t]=r)}else if("object"==typeof e&&e)if("protected"in e)t=e.protected;else throw TypeError("Token does not contain a Protected Header");try{if("string"!=typeof t||!t)throw Error();let e=JSON.parse(_.decode(I(t)));if(!et(e))throw Error();return e}catch(e){throw TypeError("Invalid Token or Protected Header formatting")}}function tA(e){let t,r;if("string"!=typeof e)throw new u("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:a}=e.split(".");if(5===a)throw new u("Only JWTs using Compact JWS serialization can be decoded");if(3!==a)throw new u("Invalid JWT");if(!n)throw new u("JWTs must contain a payload");try{t=I(n)}catch(e){throw new u("Failed to base64url decode the payload")}try{r=JSON.parse(_.decode(t))}catch(e){throw new u("Failed to parse the decoded payload as JSON")}if(!et(r))throw new u("Invalid JWT Claims Set");return r}var tR=m;async function tC(e,t){var r;let n,a,o;switch(e){case"HS256":case"HS384":case"HS512":n=parseInt(e.slice(-3),10),a={name:"HMAC",hash:`SHA-${n}`,length:n},o=["sign","verify"];break;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return M(new Uint8Array((n=parseInt(e.slice(-3),10))>>3));case"A128KW":case"A192KW":case"A256KW":a={name:"AES-KW",length:n=parseInt(e.slice(1,4),10)},o=["wrapKey","unwrapKey"];break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":case"A128GCM":case"A192GCM":case"A256GCM":a={name:"AES-GCM",length:n=parseInt(e.slice(1,4),10)},o=["encrypt","decrypt"];break;default:throw new i('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return b.subtle.generateKey(a,null!=(r=null==t?void 0:t.extractable)&&r,o)}function tP(e){var t;let r=null!=(t=null==e?void 0:e.modulusLength)?t:2048;if("number"!=typeof r||r<2048)throw new i("Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used");return r}async function tT(e,t){var r,n,a;let o,s;switch(e){case"PS256":case"PS384":case"PS512":o={name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:tP(t)},s=["sign","verify"];break;case"RS256":case"RS384":case"RS512":o={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:tP(t)},s=["sign","verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":o={name:"RSA-OAEP",hash:`SHA-${parseInt(e.slice(-3),10)||1}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:tP(t)},s=["decrypt","unwrapKey","encrypt","wrapKey"];break;case"ES256":o={name:"ECDSA",namedCurve:"P-256"},s=["sign","verify"];break;case"ES384":o={name:"ECDSA",namedCurve:"P-384"},s=["sign","verify"];break;case"ES512":o={name:"ECDSA",namedCurve:"P-521"},s=["sign","verify"];break;case"EdDSA":s=["sign","verify"];let c=null!=(r=null==t?void 0:t.crv)?r:"Ed25519";switch(c){case"Ed25519":case"Ed448":o={name:c};break;default:throw new i("Invalid or unsupported crv option provided")}break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{s=["deriveKey","deriveBits"];let e=null!=(n=null==t?void 0:t.crv)?n:"P-256";switch(e){case"P-256":case"P-384":case"P-521":o={name:"ECDH",namedCurve:e};break;case"X25519":case"X448":o={name:e};break;default:throw new i("Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, X25519, and X448")}break}default:throw new i('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return b.subtle.generateKey(o,null!=(a=null==t?void 0:t.extractable)&&a,s)}async function tO(e,t){return tT(e,t)}async function tk(e,t){return tC(e,t)}var tN=H;let tI="WebCryptoAPI"},61041,e=>{"use strict";e.s(["default",()=>n,"hkdf",()=>n],61041);let t=async(e,t,r,n,a)=>{let{crypto:{subtle:i}}=(()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;throw Error("unable to locate global object")})();return new Uint8Array(await i.deriveBits({name:"HKDF",hash:`SHA-${e.substr(3)}`,salt:r,info:n},await i.importKey("raw",t,"HKDF",!1,["deriveBits"]),a<<3))};function r(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function n(e,n,a,i,o){return t(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=r(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(n),r(a,"salt"),function(e){let t=r(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(i),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(o,e))}},86899,e=>{"use strict";e.s(["NIL",()=>C,"parse",()=>f,"stringify",()=>u,"v1",()=>h,"v3",()=>S,"v4",()=>x,"v5",()=>R,"validate",()=>s,"version",()=>P],86899),e.s([],7564),e.i(7564);var t,r,n,a=new Uint8Array(16);function i(){if(!t&&!(t="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return t(a)}let o=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,s=function(e){return"string"==typeof e&&o.test(e)};for(var c=[],l=0;l<256;++l)c.push((l+256).toString(16).substr(1));let u=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=(c[e[t+0]]+c[e[t+1]]+c[e[t+2]]+c[e[t+3]]+"-"+c[e[t+4]]+c[e[t+5]]+"-"+c[e[t+6]]+c[e[t+7]]+"-"+c[e[t+8]]+c[e[t+9]]+"-"+c[e[t+10]]+c[e[t+11]]+c[e[t+12]]+c[e[t+13]]+c[e[t+14]]+c[e[t+15]]).toLowerCase();if(!s(r))throw TypeError("Stringified UUID is invalid");return r};var d=0,p=0;let h=function(e,t,a){var o=t&&a||0,s=t||Array(16),c=(e=e||{}).node||r,l=void 0!==e.clockseq?e.clockseq:n;if(null==c||null==l){var h=e.random||(e.rng||i)();null==c&&(c=r=[1|h[0],h[1],h[2],h[3],h[4],h[5]]),null==l&&(l=n=(h[6]<<8|h[7])&16383)}var f=void 0!==e.msecs?e.msecs:Date.now(),g=void 0!==e.nsecs?e.nsecs:p+1,y=f-d+(g-p)/1e4;if(y<0&&void 0===e.clockseq&&(l=l+1&16383),(y<0||f>d)&&void 0===e.nsecs&&(g=0),g>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");d=f,p=g,n=l;var m=((0xfffffff&(f+=122192928e5))*1e4+g)%0x100000000;s[o++]=m>>>24&255,s[o++]=m>>>16&255,s[o++]=m>>>8&255,s[o++]=255&m;var b=f/0x100000000*1e4&0xfffffff;s[o++]=b>>>8&255,s[o++]=255&b,s[o++]=b>>>24&15|16,s[o++]=b>>>16&255,s[o++]=l>>>8|128,s[o++]=255&l;for(var w=0;w<6;++w)s[o+w]=c[w];return t||u(s)},f=function(e){if(!s(e))throw TypeError("Invalid UUID");var t,r=new Uint8Array(16);return r[0]=(t=parseInt(e.slice(0,8),16))>>>24,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r[4]=(t=parseInt(e.slice(9,13),16))>>>8,r[5]=255&t,r[6]=(t=parseInt(e.slice(14,18),16))>>>8,r[7]=255&t,r[8]=(t=parseInt(e.slice(19,23),16))>>>8,r[9]=255&t,r[10]=(t=parseInt(e.slice(24,36),16))/0x10000000000&255,r[11]=t/0x100000000&255,r[12]=t>>>24&255,r[13]=t>>>16&255,r[14]=t>>>8&255,r[15]=255&t,r};function g(e,t,r){function n(e,n,a,i){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}(e)),"string"==typeof n&&(n=f(n)),16!==n.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var o=new Uint8Array(16+e.length);if(o.set(n),o.set(e,n.length),(o=r(o))[6]=15&o[6]|t,o[8]=63&o[8]|128,a){i=i||0;for(var s=0;s<16;++s)a[i+s]=o[s];return a}return u(o)}try{n.name=e}catch(e){}return n.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",n.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",n}function y(e){return(e+64>>>9<<4)+14+1}function m(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function b(e,t,r,n,a,i){var o;return m((o=m(m(t,e),m(n,i)))<<a|o>>>32-a,r)}function w(e,t,r,n,a,i,o){return b(t&r|~t&n,e,t,a,i,o)}function v(e,t,r,n,a,i,o){return b(t&n|r&~n,e,t,a,i,o)}function _(e,t,r,n,a,i,o){return b(t^r^n,e,t,a,i,o)}function E(e,t,r,n,a,i,o){return b(r^(t|~n),e,t,a,i,o)}let S=g("v3",48,function(e){if("string"==typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var r=0;r<t.length;++r)e[r]=t.charCodeAt(r)}return function(e){for(var t=[],r=32*e.length,n="0123456789abcdef",a=0;a<r;a+=8){var i=e[a>>5]>>>a%32&255,o=parseInt(n.charAt(i>>>4&15)+n.charAt(15&i),16);t.push(o)}return t}(function(e,t){e[t>>5]|=128<<t%32,e[y(t)-1]=t;for(var r=0x67452301,n=-0x10325477,a=-0x67452302,i=0x10325476,o=0;o<e.length;o+=16){var s=r,c=n,l=a,u=i;r=w(r,n,a,i,e[o],7,-0x28955b88),i=w(i,r,n,a,e[o+1],12,-0x173848aa),a=w(a,i,r,n,e[o+2],17,0x242070db),n=w(n,a,i,r,e[o+3],22,-0x3e423112),r=w(r,n,a,i,e[o+4],7,-0xa83f051),i=w(i,r,n,a,e[o+5],12,0x4787c62a),a=w(a,i,r,n,e[o+6],17,-0x57cfb9ed),n=w(n,a,i,r,e[o+7],22,-0x2b96aff),r=w(r,n,a,i,e[o+8],7,0x698098d8),i=w(i,r,n,a,e[o+9],12,-0x74bb0851),a=w(a,i,r,n,e[o+10],17,-42063),n=w(n,a,i,r,e[o+11],22,-0x76a32842),r=w(r,n,a,i,e[o+12],7,0x6b901122),i=w(i,r,n,a,e[o+13],12,-0x2678e6d),a=w(a,i,r,n,e[o+14],17,-0x5986bc72),n=w(n,a,i,r,e[o+15],22,0x49b40821),r=v(r,n,a,i,e[o+1],5,-0x9e1da9e),i=v(i,r,n,a,e[o+6],9,-0x3fbf4cc0),a=v(a,i,r,n,e[o+11],14,0x265e5a51),n=v(n,a,i,r,e[o],20,-0x16493856),r=v(r,n,a,i,e[o+5],5,-0x29d0efa3),i=v(i,r,n,a,e[o+10],9,0x2441453),a=v(a,i,r,n,e[o+15],14,-0x275e197f),n=v(n,a,i,r,e[o+4],20,-0x182c0438),r=v(r,n,a,i,e[o+9],5,0x21e1cde6),i=v(i,r,n,a,e[o+14],9,-0x3cc8f82a),a=v(a,i,r,n,e[o+3],14,-0xb2af279),n=v(n,a,i,r,e[o+8],20,0x455a14ed),r=v(r,n,a,i,e[o+13],5,-0x561c16fb),i=v(i,r,n,a,e[o+2],9,-0x3105c08),a=v(a,i,r,n,e[o+7],14,0x676f02d9),n=v(n,a,i,r,e[o+12],20,-0x72d5b376),r=_(r,n,a,i,e[o+5],4,-378558),i=_(i,r,n,a,e[o+8],11,-0x788e097f),a=_(a,i,r,n,e[o+11],16,0x6d9d6122),n=_(n,a,i,r,e[o+14],23,-0x21ac7f4),r=_(r,n,a,i,e[o+1],4,-0x5b4115bc),i=_(i,r,n,a,e[o+4],11,0x4bdecfa9),a=_(a,i,r,n,e[o+7],16,-0x944b4a0),n=_(n,a,i,r,e[o+10],23,-0x41404390),r=_(r,n,a,i,e[o+13],4,0x289b7ec6),i=_(i,r,n,a,e[o],11,-0x155ed806),a=_(a,i,r,n,e[o+3],16,-0x2b10cf7b),n=_(n,a,i,r,e[o+6],23,0x4881d05),r=_(r,n,a,i,e[o+9],4,-0x262b2fc7),i=_(i,r,n,a,e[o+12],11,-0x1924661b),a=_(a,i,r,n,e[o+15],16,0x1fa27cf8),n=_(n,a,i,r,e[o+2],23,-0x3b53a99b),r=E(r,n,a,i,e[o],6,-0xbd6ddbc),i=E(i,r,n,a,e[o+7],10,0x432aff97),a=E(a,i,r,n,e[o+14],15,-0x546bdc59),n=E(n,a,i,r,e[o+5],21,-0x36c5fc7),r=E(r,n,a,i,e[o+12],6,0x655b59c3),i=E(i,r,n,a,e[o+3],10,-0x70f3336e),a=E(a,i,r,n,e[o+10],15,-1051523),n=E(n,a,i,r,e[o+1],21,-0x7a7ba22f),r=E(r,n,a,i,e[o+8],6,0x6fa87e4f),i=E(i,r,n,a,e[o+15],10,-0x1d31920),a=E(a,i,r,n,e[o+6],15,-0x5cfebcec),n=E(n,a,i,r,e[o+13],21,0x4e0811a1),r=E(r,n,a,i,e[o+4],6,-0x8ac817e),i=E(i,r,n,a,e[o+11],10,-0x42c50dcb),a=E(a,i,r,n,e[o+2],15,0x2ad7d2bb),n=E(n,a,i,r,e[o+9],21,-0x14792c6f),r=m(r,s),n=m(n,c),a=m(a,l),i=m(i,u)}return[r,n,a,i]}(function(e){if(0===e.length)return[];for(var t=8*e.length,r=new Uint32Array(y(t)),n=0;n<t;n+=8)r[n>>5]|=(255&e[n/8])<<n%32;return r}(e),8*e.length))}),x=function(e,t,r){var n=(e=e||{}).random||(e.rng||i)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(var a=0;a<16;++a)t[r+a]=n[a];return t}return u(n)};function A(e,t){return e<<t|e>>>32-t}let R=g("v5",80,function(e){var t=[0x5a827999,0x6ed9eba1,0x8f1bbcdc,0xca62c1d6],r=[0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0];if("string"==typeof e){var n=unescape(encodeURIComponent(e));e=[];for(var a=0;a<n.length;++a)e.push(n.charCodeAt(a))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var i=Math.ceil((e.length/4+2)/16),o=Array(i),s=0;s<i;++s){for(var c=new Uint32Array(16),l=0;l<16;++l)c[l]=e[64*s+4*l]<<24|e[64*s+4*l+1]<<16|e[64*s+4*l+2]<<8|e[64*s+4*l+3];o[s]=c}o[i-1][14]=(e.length-1)*8/0x100000000,o[i-1][14]=Math.floor(o[i-1][14]),o[i-1][15]=(e.length-1)*8|0;for(var u=0;u<i;++u){for(var d=new Uint32Array(80),p=0;p<16;++p)d[p]=o[u][p];for(var h=16;h<80;++h)d[h]=A(d[h-3]^d[h-8]^d[h-14]^d[h-16],1);for(var f=r[0],g=r[1],y=r[2],m=r[3],b=r[4],w=0;w<80;++w){var v=Math.floor(w/20),_=A(f,5)+function(e,t,r,n){switch(e){case 0:return t&r^~t&n;case 1:case 3:return t^r^n;case 2:return t&r^t&n^r&n}}(v,g,y,m)+b+t[v]+d[w]>>>0;b=m,m=y,y=A(g,30)>>>0,g=f,f=_}r[0]=r[0]+f>>>0,r[1]=r[1]+g>>>0,r[2]=r[2]+y>>>0,r[3]=r[3]+m>>>0,r[4]=r[4]+b>>>0}return[r[0]>>24&255,r[0]>>16&255,r[0]>>8&255,255&r[0],r[1]>>24&255,r[1]>>16&255,r[1]>>8&255,255&r[1],r[2]>>24&255,r[2]>>16&255,r[2]>>8&255,255&r[2],r[3]>>24&255,r[3]>>16&255,r[3]>>8&255,255&r[3],r[4]>>24&255,r[4]>>16&255,r[4]>>8&255,255&r[4]]}),C="00000000-0000-0000-0000-000000000000",P=function(e){if(!s(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}},99765,(e,t,r)=>{"use strict";function n(e,t,r){a(e,t),t.set(e,r)}function a(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function i(e,t){return e.get(s(e,t))}function o(e,t,r){return e.set(s(e,t),r),r}function s(e,t,r){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:r;throw TypeError("Private element is not present on this object")}Object.defineProperty(r,"__esModule",{value:!0}),r.SessionStore=void 0,r.defaultCookies=function(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}next-auth.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}next-auth.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}next-auth.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}next-auth.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}next-auth.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}next-auth.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}}}};var c=new WeakMap,l=new WeakMap,u=new WeakMap,d=new WeakSet;function p(e){let t=Math.ceil(e.value.length/3933);if(1===t)return i(c,this)[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,a=e.value.substr(3933*n,3933);r.push({...e,name:t,value:a}),i(c,this)[t]=a}return i(u,this).debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:163,valueSize:e.value.length,chunks:r.map(e=>e.value.length+163)}),r}function h(){let e={};for(let r in i(c,this)){var t;null==(t=i(c,this))||delete t[r],e[r]={name:r,value:"",options:{...i(l,this).options,maxAge:0}}}return e}r.SessionStore=class{constructor(e,t,r){!function(e,t){a(e,t),t.add(e)}(this,d),n(this,c,{}),n(this,l,void 0),n(this,u,void 0),o(u,this,r),o(l,this,e);let{cookies:s}=t,{name:p}=e;if("function"==typeof(null==s?void 0:s.getAll))for(let{name:e,value:t}of s.getAll())e.startsWith(p)&&(i(c,this)[e]=t);else if(s instanceof Map)for(let e of s.keys())e.startsWith(p)&&(i(c,this)[e]=s.get(e));else for(let e in s)e.startsWith(p)&&(i(c,this)[e]=s[e])}get value(){return Object.keys(i(c,this)).sort((e,t)=>{var r,n;return parseInt(null!=(r=e.split(".").pop())?r:"0")-parseInt(null!=(n=t.split(".").pop())?n:"0")}).map(e=>i(c,this)[e]).join("")}chunk(e,t){let r=s(d,this,h).call(this);for(let n of s(d,this,p).call(this,{name:i(l,this).name,value:e,options:{...i(l,this).options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(s(d,this,h).call(this))}}},84775,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},56099,(e,t,r)=>{"use strict";var n=e.r(18862);Object.defineProperty(r,"__esModule",{value:!0});var a={encode:!0,decode:!0,getToken:!0};r.decode=d,r.encode=u,r.getToken=p;var i=e.r(51600),o=n(e.r(61041)),s=e.r(86899),c=e.r(99765),l=e.r(84775);async function u(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:a=""}=e,o=await h(r,a);return await new i.EncryptJWT(t).setProtectedHeader({alg:"dir",enc:"A256GCM"}).setIssuedAt().setExpirationTime((Date.now()/1e3|0)+n).setJti((0,s.v4)()).encrypt(o)}async function d(e){let{token:t,secret:r,salt:n=""}=e;if(!t)return null;let a=await h(r,n),{payload:o}=await (0,i.jwtDecrypt)(t,a,{clockTolerance:15});return o}async function p(e){var t,r,n,a;let{req:i,secureCookie:o=null!=(t=null==(r=process.env.NEXTAUTH_URL)?void 0:r.startsWith("https://"))?t:!!process.env.VERCEL,cookieName:s=o?"__Secure-next-auth.session-token":"next-auth.session-token",raw:l,decode:u=d,logger:p=console,secret:h=null!=(n=process.env.NEXTAUTH_SECRET)?n:process.env.AUTH_SECRET}=e;if(!i)throw Error("Must pass `req` to JWT getToken()");let f=new c.SessionStore({name:s,options:{secure:o}},{cookies:i.cookies,headers:i.headers},p).value,g=i.headers instanceof Headers?i.headers.get("authorization"):null==(a=i.headers)?void 0:a.authorization;if(f||(null==g?void 0:g.split(" ")[0])!=="Bearer"||(f=decodeURIComponent(g.split(" ")[1])),!f)return null;if(l)return f;try{return await u({token:f,secret:h})}catch(e){return null}}async function h(e,t){return await (0,o.default)("sha256",e,t,`NextAuth.js Generated Encryption Key${t?` (${t})`:""}`,32)}Object.keys(l).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===l[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return l[e]}}))})},43021,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){var t;let r=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let n=new URL(null!=(t=e)?t:r),a=("/"===n.pathname?r.pathname:n.pathname).replace(/\/$/,""),i=`${n.origin}${a}`;return{origin:n.origin,host:n.host,path:a,base:i,toString:()=>i}}},64544,(e,t,r)=>{"use strict";var n=e.r(18862);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,r.withAuth=c;var a=e.r(70754),i=e.r(56099),o=n(e.r(43021));async function s(e,t,r){var n,s,c,l,u,d,p,h,f,g,y;let{pathname:m,search:b,origin:w,basePath:v}=e.nextUrl,_=null!=(n=null==t||null==(s=t.pages)?void 0:s.signIn)?n:"/api/auth/signin",E=null!=(c=null==t||null==(l=t.pages)?void 0:l.error)?c:"/api/auth/error",S=(0,o.default)(process.env.NEXTAUTH_URL).path;if(`${v}${m}`.startsWith(S)||[_,E].includes(m)||["/_next","/favicon.ico"].some(e=>m.startsWith(e)))return;let x=null!=(u=null!=(d=null==t?void 0:t.secret)?d:process.env.NEXTAUTH_SECRET)?u:process.env.AUTH_SECRET;if(!x){console.error("[next-auth][error][NO_SECRET]",`
https://next-auth.js.org/errors#no_secret`);let e=new URL(`${v}${E}`,w);return e.searchParams.append("error","Configuration"),a.NextResponse.redirect(e)}let A=await (0,i.getToken)({req:e,decode:null==t||null==(p=t.jwt)?void 0:p.decode,cookieName:null==t||null==(h=t.cookies)||null==(h=h.sessionToken)?void 0:h.name,secret:x});if(null!=(f=await (null==t||null==(g=t.callbacks)||null==(y=g.authorized)?void 0:y.call(g,{req:e,token:A})))?f:!!A)return await (null==r?void 0:r(A));let R=new URL(`${v}${_}`,w);return R.searchParams.append("callbackUrl",`${v}${m}${b}`),a.NextResponse.redirect(R)}function c(...e){if(!e.length||e[0]instanceof Request)return s(...e);if("function"==typeof e[0]){let t=e[0],r=e[1];return async(...e)=>await s(e[0],r,async r=>(e[0].nextauth={token:r},await t(...e)))}let t=e[0];return async(...e)=>await s(e[0],t)}r.default=c},45908,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var s=a?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(e.r(64544));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})},11129,e=>{"use strict";let t;async function r(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}e.s(["default",()=>eJ],11129);let n=null;async function a(){if("phase-production-build"===process.env.NEXT_PHASE)return;n||(n=r());let e=await n;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function i(...e){let t=await r();try{var n;await (null==t||null==(n=t.onRequestError)?void 0:n.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let o=null;function s(){return o||(o=a()),o}function c(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==e.g.process&&(process.env=e.g.process.env,e.g.process=process);try{Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(c(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(c(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,a){if("function"==typeof a[0])return a[0](t);throw Object.defineProperty(Error(c(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1})}catch{}s();var l=e.i(22182),u=e.i(13264);let d=Symbol("response"),p=Symbol("passThrough"),h=Symbol("waitUntil");class f{constructor(e,t){this[p]=!1,this[h]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[d]||(this[d]=Promise.resolve(e))}passThroughOnException(){this[p]=!0}waitUntil(e){if("external"===this[h].kind)return(0,this[h].function)(e);this[h].promises.push(e)}}class g extends f{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new l.PageSignatureError({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new l.PageSignatureError({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}var y=e.i(57476),m=e.i(82303);function b(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),a=n.origin===r.origin;return{url:a?n.toString().slice(r.origin.length):n.toString(),isRelative:a}}var w=e.i(75768),v=e.i(72416);v.NEXT_RSC_UNION_QUERY;var _=e.i(34893);class E extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new E}}class S extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return _.ReflectAdapter.get(t,r,n);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==i)return _.ReflectAdapter.get(t,i,n)},set(t,r,n,a){if("symbol"==typeof r)return _.ReflectAdapter.set(t,r,n,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return _.ReflectAdapter.set(t,o??r,n,a)},has(t,r){if("symbol"==typeof r)return _.ReflectAdapter.has(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==a&&_.ReflectAdapter.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return _.ReflectAdapter.deleteProperty(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===a||_.ReflectAdapter.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return E.callable;default:return _.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new S(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}e.i(65402);var x=e.i(1923);e.i(64748);var A=e.i(21947);class R extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new R}}class C{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return R.callable;default:return _.ReflectAdapter.get(e,t,r)}}})}}let P=Symbol.for("next.mutated.cookies");class T{static wrap(e,t){let r=new x.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],a=new Set,i=()=>{let e=A.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of n){let r=new x.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},o=new Proxy(r,{get(e,t,r){switch(t){case P:return n;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),o}finally{i()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),o}finally{i()}};default:return _.ReflectAdapter.get(e,t,r)}}});return o}}function O(e,t){if("action"!==e.phase)throw new R}var k=e.i(7156),N=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(N||{}),I=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(I||{}),H=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(H||{}),M=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(M||{}),D=function(e){return e.startServer="startServer.startServer",e}(D||{}),j=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(j||{}),U=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(U||{}),W=function(e){return e.executeRoute="Router.executeRoute",e}(W||{}),L=function(e){return e.runHandler="Node.runHandler",e}(L||{}),K=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(K||{}),$=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}($||{}),J=function(e){return e.execute="Middleware.execute",e}(J||{});let B=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],q=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function V(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:G,propagation:z,trace:F,SpanStatusCode:X,SpanKind:Y,ROOT_CONTEXT:Q}=t=e.r(12431);class Z extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let ee=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof Z})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&(e.recordException(t),e.setAttribute("error.type",t.name)),e.setStatus({code:X.ERROR,message:null==t?void 0:t.message})),e.end()},et=new Map,er=t.createContextKey("next.rootSpanId"),en=0,ea={set(e,t,r){e.push({key:t,value:r})}};class ei{getTracerInstance(){return F.getTracer("next.js","0.0.1")}getContext(){return G}getTracePropagationData(){let e=G.active(),t=[];return z.inject(e,t,ea),t}getActiveScopeSpan(){return F.getSpan(null==G?void 0:G.active())}withPropagatedContext(e,t,r){let n=G.active();if(F.getSpanContext(n))return t();let a=z.extract(n,e,r);return G.with(a,t)}trace(...e){var t;let[r,n,a]=e,{fn:i,options:o}="function"==typeof n?{fn:n,options:{}}:{fn:a,options:{...n}},s=o.spanName??r;if(!B.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||o.hideSpan)return i();let c=this.getSpanContext((null==o?void 0:o.parentSpan)??this.getActiveScopeSpan()),l=!1;c?(null==(t=F.getSpanContext(c))?void 0:t.isRemote)&&(l=!0):(c=(null==G?void 0:G.active())??Q,l=!0);let u=en++;return o.attributes={"next.span_name":s,"next.span_type":r,...o.attributes},G.with(c.setValue(er,u),()=>this.getTracerInstance().startActiveSpan(s,o,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{et.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&q.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};l&&et.set(u,new Map(Object.entries(o.attributes??{})));try{if(i.length>1)return i(e,t=>ee(e,t));let t=i(e);if(V(t))return t.then(t=>(e.end(),t)).catch(t=>{throw ee(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw ee(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,a]=3===e.length?e:[e[0],{},e[1]];return B.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof a&&(e=e.apply(this,arguments));let i=arguments.length-1,o=arguments[i];if("function"!=typeof o)return t.trace(r,e,()=>a.apply(this,arguments));{let n=t.getContext().bind(G.active(),o);return t.trace(r,e,(e,t)=>(arguments[i]=function(e){return null==t||t(e),n.apply(this,arguments)},a.apply(this,arguments)))}}:a}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?F.setSpan(G.active(),e):void 0}getRootSpanAttributes(){let e=G.active().getValue(er);return et.get(e)}setRootSpanAttribute(e,t){let r=G.active().getValue(er),n=et.get(r);n&&n.set(e,t)}}let eo=(()=>{let e=new ei;return()=>e})(),es="__prerender_bypass";Symbol("__next_preview_data"),Symbol(es);class ec{constructor(e,t,r,n){var a;let i=e&&function(e,t){let r=S.from(e.headers);return{isOnDemandRevalidate:r.get(k.PRERENDER_REVALIDATE_HEADER)===t.previewModeId,revalidateOnlyGenerated:r.has(k.PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER)}}(t,e).isOnDemandRevalidate,o=null==(a=r.get(es))?void 0:a.value;this._isEnabled=!!(!i&&o&&e&&o===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:es,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:es,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function el(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of(0,u.splitCookiesString)(r))n.append("set-cookie",e);for(let e of new x.ResponseCookies(n).getAll())t.set(e)}}e.i(45205);var eu=e.i(26834),ed=e.i(63567),ep=e.i(39144);e.i(51615),new(e.i(58291)).LRUCache(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE&&((e,...t)=>{console.log(`use-cache: ${e}`,...t)}),Symbol.for("@next/cache-handlers");let eh=Symbol.for("@next/cache-handlers-map"),ef=Symbol.for("@next/cache-handlers-set"),eg=globalThis;function ey(){if(eg[eh])return eg[eh].entries()}async function em(e,t){if(!e)return t();let r=eb(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eb(e));await ev(e,t)}}function eb(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function ew(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eg[ef])return eg[ef].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function ev(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},a=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([ew(r,e.incrementalCache),...Object.values(n),...a])}var e_=e.i(38953);e.i(64287);var eE=e.i(45506);class eS{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new ed.default,this.callbackQueue.pause()}after(e){if(V(e))this.waitUntil||ex(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){this.waitUntil||ex();let t=eu.workUnitAsyncStorage.getStore();t&&this.workUnitStores.add(t);let r=eE.afterTaskAsyncStorage.getStore(),n=r?r.rootTaskSpawnPhase:null==t?void 0:t.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let a=(0,e_.bindSnapshot)(async()=>{try{await eE.afterTaskAsyncStorage.run({rootTaskSpawnPhase:n},()=>e())}catch(e){this.reportTaskError("function",e)}});this.callbackQueue.add(a)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=A.workAsyncStorage.getStore();if(!e)throw Object.defineProperty(new ep.InvariantError("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return em(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new ep.InvariantError("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function ex(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function eA(e){let t,r={then:(n,a)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,a))};return r}class eR{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function eC(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID||"",previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let eP=Symbol.for("@next/request-context");async function eT(e,t,r){let n=[],a=r&&r.size>0;for(let t of(e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t})(e))t=`${k.NEXT_CACHE_IMPLICIT_TAG_ID}${t}`,n.push(t);if(t.pathname&&!a){let e=`${k.NEXT_CACHE_IMPLICIT_TAG_ID}${t.pathname}`;n.push(e)}return{tags:n,expirationsByCacheKind:function(e){let t=new Map,r=ey();if(r)for(let[n,a]of r)"getExpiration"in a&&t.set(n,eA(async()=>a.getExpiration(...e)));return t}(n)}}class eO extends y.NextRequest{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new l.PageSignatureError({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new l.PageSignatureError({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new l.PageSignatureError({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let ek={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},eN=(e,t)=>eo().withPropagatedContext(e.headers,t,ek),eI=!1;async function eH(t){var r;let n,a;if(!eI&&(eI=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:t,wrapRequestHandler:r}=e.r(92137);t(),eN=r(eN)}await s();let i=void 0!==globalThis.__BUILD_MANIFEST;t.request.url=t.request.url.replace(/\.rsc($|\?)/,"$1");let o=t.bypassNextUrl?new URL(t.request.url):new w.NextURL(t.request.url,{headers:t.request.headers,nextConfig:t.request.nextConfig});for(let e of[...o.searchParams.keys()]){let t=o.searchParams.getAll(e),r=(0,u.normalizeNextQueryParam)(e);if(r){for(let e of(o.searchParams.delete(r),t))o.searchParams.append(r,e);o.searchParams.delete(e)}}let c=process.env.__NEXT_BUILD_ID||"";"buildId"in o&&(c=o.buildId||"",o.buildId="");let l=(0,u.fromNodeOutgoingHttpHeaders)(t.request.headers),d=l.has("x-nextjs-data"),p="1"===l.get(v.RSC_HEADER);d&&"/index"===o.pathname&&(o.pathname="/");let f=new Map;if(!i)for(let e of v.FLIGHT_HEADERS){let t=l.get(e);null!==t&&(f.set(e,t),l.delete(e))}let y=o.searchParams.get(v.NEXT_RSC_UNION_QUERY),E=new eO({page:t.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(v.NEXT_RSC_UNION_QUERY),t?r.toString():r})(o).toString(),init:{body:t.request.body,headers:l,method:t.request.method,nextConfig:t.request.nextConfig,signal:t.request.signal}});d&&Object.defineProperty(E,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCacheShared&&t.IncrementalCache&&(globalThis.__incrementalCache=new t.IncrementalCache({CurCacheHandler:t.incrementalCacheHandler,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:t.request.headers,getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:eC()})}));let R=t.request.waitUntil??(null==(r=function(){let e=globalThis[eP];return null==e?void 0:e.get()}())?void 0:r.waitUntil),P=new g({request:E,page:t.page,context:R?{waitUntil:R}:void 0});if((n=await eN(E,()=>{if("/middleware"===t.page||"/src/middleware"===t.page){let e=P.waitUntil.bind(P),r=new eR;return eo().trace(J.execute,{spanName:`middleware ${E.method} ${E.nextUrl.pathname}`,attributes:{"http.target":E.nextUrl.pathname,"http.method":E.method}},async()=>{try{var n,i,o,s,l,u;let d=eC(),p=await eT("/",E.nextUrl,null),h=(l=E.nextUrl,u=e=>{a=e},function(e,t,r,n,a,i,o,s,c,l,u,d){function p(e){r&&r.setHeader("Set-Cookie",e)}let h={};return{type:"request",phase:e,implicitTags:i,url:{pathname:n.pathname,search:n.search??""},rootParams:a,get headers(){return h.headers||(h.headers=function(e){let t=S.from(e);for(let e of v.FLIGHT_HEADERS)t.delete(e);return S.seal(t)}(t.headers)),h.headers},get cookies(){if(!h.cookies){let e=new x.RequestCookies(S.from(t.headers));el(t,e),h.cookies=C.seal(e)}return h.cookies},set cookies(value){h.cookies=value},get mutableCookies(){if(!h.mutableCookies){let e=function(e,t){let r=new x.RequestCookies(S.from(e));return T.wrap(r,t)}(t.headers,o||(r?p:void 0));el(t,e),h.mutableCookies=e}return h.mutableCookies},get userspaceMutableCookies(){return h.userspaceMutableCookies||(h.userspaceMutableCookies=function(e){let t=new Proxy(e.mutableCookies,{get(r,n,a){switch(n){case"delete":return function(...n){return O(e,"cookies().delete"),r.delete(...n),t};case"set":return function(...n){return O(e,"cookies().set"),r.set(...n),t};default:return _.ReflectAdapter.get(r,n,a)}}});return t}(this)),h.userspaceMutableCookies},get draftMode(){return h.draftMode||(h.draftMode=new ec(c,t,this.cookies,this.mutableCookies)),h.draftMode},renderResumeDataCache:s??null,isHmrRefresh:l,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache,devFallbackParams:null}}("action",E,void 0,l,{},p,u,void 0,d,!1,void 0,null)),f=function({page:e,renderOpts:t,isPrefetchRequest:r,buildId:n,previouslyRevalidatedTags:a}){var i;let o=!t.shouldWaitOnAllReady&&!t.supportsDynamicResponse&&!t.isDraftMode&&!t.isPossibleServerAction,s=t.dev??!1,c=s||o&&(!!process.env.NEXT_DEBUG_BUILD||"1"===process.env.NEXT_SSG_FETCH_METRICS),l={isStaticGeneration:o,page:e,route:(i=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?i:"/"+i,incrementalCache:t.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:t.cacheLifeProfiles,isRevalidate:t.isRevalidate,isBuildTimePrerendering:t.nextExport,hasReadableErrorStacks:t.hasReadableErrorStacks,fetchCache:t.fetchCache,isOnDemandRevalidate:t.isOnDemandRevalidate,isDraftMode:t.isDraftMode,isPrefetchRequest:r,buildId:n,reactLoadableManifest:(null==t?void 0:t.reactLoadableManifest)||{},assetPrefix:(null==t?void 0:t.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new eS({waitUntil:t,onClose:r,onTaskError:n})}(t),cacheComponentsEnabled:t.experimental.cacheComponents,dev:s,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=ey();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,eA(async()=>n.refreshTags()));return e}(),runInCleanSnapshot:(0,e_.createSnapshot)(),shouldTrackFetchMetrics:c};return t.store=l,l}({page:"/",renderOpts:{cacheLifeProfiles:null==(i=t.request.nextConfig)||null==(n=i.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,cacheComponents:!1,authInterrupts:!!(null==(s=t.request.nextConfig)||null==(o=s.experimental)?void 0:o.authInterrupts)},supportsDynamicResponse:!0,waitUntil:e,onClose:r.onClose.bind(r),onAfterTaskError:void 0},isPrefetchRequest:"1"===E.headers.get(v.NEXT_ROUTER_PREFETCH_HEADER),buildId:c??"",previouslyRevalidatedTags:[]});return await A.workAsyncStorage.run(f,()=>eu.workUnitAsyncStorage.run(h,t.handler,E,P))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return t.handler(E,P)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&a&&n.headers.set("set-cookie",a);let k=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&k&&(p||!i)){let e=new w.NextURL(k,{forceLocale:!0,headers:t.request.headers,nextConfig:t.request.nextConfig});i||e.host!==E.nextUrl.host||(e.buildId=c||e.buildId,n.headers.set("x-middleware-rewrite",String(e)));let{url:r,isRelative:a}=b(e.toString(),o.toString());!i&&d&&n.headers.set("x-nextjs-rewrite",r),p&&a&&(o.pathname!==e.pathname&&n.headers.set(v.NEXT_REWRITTEN_PATH_HEADER,e.pathname),o.search!==e.search&&n.headers.set(v.NEXT_REWRITTEN_QUERY_HEADER,e.search.slice(1)))}if(n&&k&&p&&y){let e=new URL(k);e.searchParams.has(v.NEXT_RSC_UNION_QUERY)||(e.searchParams.set(v.NEXT_RSC_UNION_QUERY,y),n.headers.set("x-middleware-rewrite",e.toString()))}let N=null==n?void 0:n.headers.get("Location");if(n&&N&&!i){let e=new w.NextURL(N,{forceLocale:!1,headers:t.request.headers,nextConfig:t.request.nextConfig});n=new Response(n.body,n),e.host===o.host&&(e.buildId=c||e.buildId,n.headers.set("Location",e.toString())),d&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",b(e.toString(),o.toString()).url))}let I=n||m.NextResponse.next(),H=I.headers.get("x-middleware-override-headers"),M=[];if(H){for(let[e,t]of f)I.headers.set(`x-middleware-request-${e}`,t),M.push(e);M.length>0&&I.headers.set("x-middleware-override-headers",H+","+M.join(","))}return{response:I,waitUntil:("internal"===P[h].kind?Promise.all(P[h].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:E.fetchMetrics}}e.s(["config",()=>eU,"default",()=>ej],73415);var eM=e.i(45908);e.i(9895);var eD=e.i(37844);let ej=(0,eM.withAuth)(function(e){let t=e.nextauth.token,{pathname:r}=e.nextUrl;if(!t)return eD.NextResponse.redirect(new URL("/login",e.url));let n=t.role;return r.startsWith("/admin")&&"ADMIN"!==n||r.startsWith("/teacher")&&"TEACHER"!==n&&"ADMIN"!==n||r.startsWith("/student")&&"STUDENT"!==n&&"ADMIN"!==n?eD.NextResponse.redirect(new URL("/unauthorized",e.url)):r.startsWith("/api/admin")&&"ADMIN"!==n||r.startsWith("/api/teacher")&&"TEACHER"!==n&&"ADMIN"!==n||r.startsWith("/api/student")&&"STUDENT"!==n&&"ADMIN"!==n?eD.NextResponse.json({error:"Unauthorized"},{status:401}):eD.NextResponse.next()},{callbacks:{authorized:({token:e})=>!!e}}),eU={matcher:["/admin/:path*","/teacher/:path*","/student/:path*","/api/admin/:path*","/api/teacher/:path*","/api/student/:path*"]};var eW=e.i(73415);Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401});let eL={...eW},eK=eL.middleware||eL.default,e$="/middleware";if("function"!=typeof eK)throw Object.defineProperty(Error(`The Middleware "${e$}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function eJ(e){return eH({...e,page:e$,handler:async(...e)=>{try{return await eK(...e)}catch(a){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await i(a,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),a}}})}}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__b388f26c._.js.map