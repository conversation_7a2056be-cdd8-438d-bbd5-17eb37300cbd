module.exports=[45176,a=>{"use strict";a.s(["default",()=>q]);var b=a.i(41825),c=a.i(54159),d=a.i(4082),e=a.i(2331),f=a.i(11090),g=a.i(62821),h=a.i(78402),i=a.i(14401),j=a.i(3815),k=a.i(91662),l=a.i(32541),m=a.i(66710),n=a.i(14023),o=a.i(73211),p=a.i(95788);function q(){let[a,q]=(0,c.useState)([]),[r,s]=(0,c.useState)("all"),[t,u]=(0,c.useState)("all"),[v,w]=(0,c.useState)("all");(0,c.useEffect)(()=>{q([{id:"1",studentName:"<PERSON>",admissionNo:"STU001",className:"Grade 8",sectionName:"A",termName:"Term 1",academicYear:"2024-2025",totalMarks:500,obtainedMarks:425,percentage:85,grade:"A",rank:3,generatedAt:"2024-12-15",status:"GENERATED"},{id:"2",studentName:"Jane Smith",admissionNo:"STU002",className:"Grade 8",sectionName:"A",termName:"Term 1",academicYear:"2024-2025",totalMarks:500,obtainedMarks:380,percentage:76,grade:"B+",rank:8,generatedAt:"2024-12-15",status:"PUBLISHED"},{id:"3",studentName:"Mike Johnson",admissionNo:"STU003",className:"Grade 8",sectionName:"A",termName:"Term 1",academicYear:"2024-2025",totalMarks:500,obtainedMarks:450,percentage:90,grade:"A+",rank:1,generatedAt:"2024-12-15",status:"GENERATED"}])},[]);let x=a=>{switch(a){case"GENERATED":return"bg-blue-100 text-blue-800";case"PUBLISHED":return"bg-green-100 text-green-800";case"PENDING":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},y=a=>{switch(a){case"A+":case"A":return"bg-green-100 text-green-800";case"B+":case"B":return"bg-blue-100 text-blue-800";case"C+":case"C":return"bg-yellow-100 text-yellow-800";case"D":return"bg-orange-100 text-orange-800";case"F":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},z={total:a.length,generated:a.filter(a=>"GENERATED"===a.status).length,published:a.filter(a=>"PUBLISHED"===a.status).length,pending:a.filter(a=>"PENDING"===a.status).length,averagePercentage:a.length>0?Math.round(a.reduce((a,b)=>a+b.percentage,0)/a.length):0};return(0,b.jsx)(g.default,{title:"Reports Management",navigation:p.adminNavigation,children:(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"Reports Management"}),(0,b.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Generate and manage student report cards and academic reports"})]}),(0,b.jsxs)("div",{className:"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2",children:[(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full sm:w-auto",children:[(0,b.jsx)(j.BarChart3,{className:"w-4 h-4 mr-2"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Analytics"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Analytics Report"})]}),(0,b.jsxs)(e.Button,{className:"w-full sm:w-auto",children:[(0,b.jsx)(h.FileText,{className:"w-4 h-4 mr-2"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Generate"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Generate Reports"})]})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Total Reports"}),(0,b.jsx)(h.FileText,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:z.total})})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Generated"}),(0,b.jsx)(h.FileText,{className:"h-4 w-4 text-blue-600"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:z.generated})})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Published"}),(0,b.jsx)(h.FileText,{className:"h-4 w-4 text-green-600"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:z.published})})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Pending"}),(0,b.jsx)(h.FileText,{className:"h-4 w-4 text-yellow-600"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:z.pending})})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Avg Score"}),(0,b.jsx)(m.TrendingUp,{className:"h-4 w-4 text-purple-600"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[z.averagePercentage,"%"]})})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,b.jsx)(h.FileText,{className:"w-5 h-5 mr-2"}),"Generate Report Cards"]}),(0,b.jsx)(d.CardDescription,{children:"Create report cards for all students in a class"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"term-select",children:"Select Term"}),(0,b.jsxs)("select",{id:"term-select",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"",children:"Choose a term"}),(0,b.jsx)("option",{value:"term1",children:"Term 1"}),(0,b.jsx)("option",{value:"term2",children:"Term 2"}),(0,b.jsx)("option",{value:"term3",children:"Term 3"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"class-select",children:"Select Class"}),(0,b.jsxs)("select",{id:"class-select",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"",children:"Choose a class"}),(0,b.jsx)("option",{value:"grade8",children:"Grade 8"}),(0,b.jsx)("option",{value:"grade9",children:"Grade 9"}),(0,b.jsx)("option",{value:"grade10",children:"Grade 10"})]})]}),(0,b.jsxs)(e.Button,{className:"w-full",children:[(0,b.jsx)(h.FileText,{className:"w-4 h-4 mr-2"}),"Generate Reports"]})]})})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,b.jsx)(j.BarChart3,{className:"w-5 h-5 mr-2"}),"Performance Analytics"]}),(0,b.jsx)(d.CardDescription,{children:"View detailed performance analytics"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full",children:[(0,b.jsx)(m.TrendingUp,{className:"w-4 h-4 mr-2"}),"Class Performance"]}),(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full",children:[(0,b.jsx)(l.Award,{className:"w-4 h-4 mr-2"}),"Subject Analysis"]}),(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full",children:[(0,b.jsx)(k.Users,{className:"w-4 h-4 mr-2"}),"Student Rankings"]})]})})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,b.jsx)(i.Download,{className:"w-5 h-5 mr-2"}),"Export Reports"]}),(0,b.jsx)(d.CardDescription,{children:"Export reports in various formats"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full",children:[(0,b.jsx)(i.Download,{className:"w-4 h-4 mr-2"}),"Export to Excel"]}),(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full",children:[(0,b.jsx)(h.FileText,{className:"w-4 h-4 mr-2"}),"Export to PDF"]}),(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full",children:[(0,b.jsx)(n.Printer,{className:"w-4 h-4 mr-2"}),"Print Reports"]})]})})]})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsx)(d.CardHeader,{children:(0,b.jsx)(d.CardTitle,{children:"Filters"})}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"term",children:"Term"}),(0,b.jsxs)("select",{id:"term",value:r,onChange:a=>s(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:"All Terms"}),(0,b.jsx)("option",{value:"term1",children:"Term 1"}),(0,b.jsx)("option",{value:"term2",children:"Term 2"}),(0,b.jsx)("option",{value:"term3",children:"Term 3"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"class",children:"Class"}),(0,b.jsxs)("select",{id:"class",value:t,onChange:a=>u(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:"All Classes"}),(0,b.jsx)("option",{value:"grade8",children:"Grade 8"}),(0,b.jsx)("option",{value:"grade9",children:"Grade 9"}),(0,b.jsx)("option",{value:"grade10",children:"Grade 10"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"status",children:"Status"}),(0,b.jsxs)("select",{id:"status",value:v,onChange:a=>w(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:"All Status"}),(0,b.jsx)("option",{value:"generated",children:"Generated"}),(0,b.jsx)("option",{value:"published",children:"Published"}),(0,b.jsx)("option",{value:"pending",children:"Pending"})]})]})]})})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsx)(d.CardTitle,{children:"Report Cards"}),(0,b.jsx)(d.CardDescription,{children:"Generated report cards for students"})]}),(0,b.jsxs)(d.CardContent,{children:[(0,b.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,b.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,b.jsx)("thead",{className:"bg-gray-50",children:(0,b.jsxs)("tr",{children:[(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Class"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Term"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Marks"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentage"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Grade"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,b.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map(a=>(0,b.jsxs)("tr",{children:[(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,b.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,b.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a.studentName.split(" ").map(a=>a[0]).join("")})})}),(0,b.jsxs)("div",{className:"ml-4",children:[(0,b.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.studentName}),(0,b.jsx)("div",{className:"text-sm text-gray-500",children:a.admissionNo})]})]})}),(0,b.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.className," - ",a.sectionName]}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.termName}),(0,b.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.obtainedMarks,"/",a.totalMarks]}),(0,b.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.percentage,"%"]}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,b.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${y(a.grade)}`,children:a.grade})}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.rank}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,b.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${x(a.status)}`,children:a.status})}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,b.jsxs)("div",{className:"flex space-x-2",children:[(0,b.jsx)(e.Button,{variant:"outline",size:"sm",children:(0,b.jsx)(o.Eye,{className:"w-4 h-4"})}),(0,b.jsx)(e.Button,{variant:"outline",size:"sm",children:(0,b.jsx)(i.Download,{className:"w-4 h-4"})}),(0,b.jsx)(e.Button,{variant:"outline",size:"sm",children:(0,b.jsx)(n.Printer,{className:"w-4 h-4"})})]})})]},a.id))})]})}),(0,b.jsx)("div",{className:"lg:hidden space-y-4",children:a.map(a=>(0,b.jsx)(d.Card,{className:"p-4",children:(0,b.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-start justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0",children:(0,b.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a.studentName.split(" ").map(a=>a[0]).join("")})}),(0,b.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 truncate",children:a.studentName}),(0,b.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[a.admissionNo," • ",a.className," - ",a.sectionName]})]})]}),(0,b.jsx)("div",{className:"flex items-center space-x-2",children:(0,b.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${x(a.status)}`,children:a.status})})]}),(0,b.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Term:"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:a.termName})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Marks:"}),(0,b.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[a.obtainedMarks,"/",a.totalMarks]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Percentage:"}),(0,b.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[a.percentage,"%"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Grade:"}),(0,b.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${y(a.grade)}`,children:a.grade})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Rank:"}),(0,b.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:["#",a.rank]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Generated:"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:a.generatedAt})]})]}),(0,b.jsxs)("div",{className:"flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700",children:[(0,b.jsxs)(e.Button,{variant:"outline",size:"sm",className:"flex-1",children:[(0,b.jsx)(o.Eye,{className:"w-4 h-4 mr-1"}),"View"]}),(0,b.jsxs)(e.Button,{variant:"outline",size:"sm",className:"flex-1",children:[(0,b.jsx)(i.Download,{className:"w-4 h-4 mr-1"}),"Download"]}),(0,b.jsxs)(e.Button,{variant:"outline",size:"sm",className:"flex-1",children:[(0,b.jsx)(n.Printer,{className:"w-4 h-4 mr-1"}),"Print"]})]})]})},a.id))})]})]})]})})}}];

//# sourceMappingURL=school-management-system_src_app_%28dash%29_admin_reports_page_tsx_ebcb6537._.js.map