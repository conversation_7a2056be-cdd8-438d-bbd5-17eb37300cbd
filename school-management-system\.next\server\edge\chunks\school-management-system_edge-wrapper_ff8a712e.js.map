{"version": 3, "sources": ["turbopack:///[project]/school-management-system/edge-wrapper.js"], "sourcesContent": ["self._ENTRIES ||= {};\nconst modProm = import('MODULE');\nmodProm.catch(() => {});\nself._ENTRIES[\"middleware_middleware\"] = new Proxy(modProm, {\n    get(modProm, name) {\n        if (name === \"then\") {\n            return (res, rej) => modProm.then(res, rej);\n        }\n        let result = (...args) => modProm.then((mod) => (0, mod[name])(...args));\n        result.then = (res, rej) => modProm.then((mod) => mod[name]).then(res, rej);\n        return result;\n    },\n});\n"], "names": [], "mappings": "mIAAA,KAAK,QAAQ,GAAK,CAAC,EACnB,IAAM,EAAA,QAAA,OAAA,GAAA,IAAA,CAAA,IAAA,EAAA,CAAA,CAAA,QACN,EAAQ,KAAK,CAAC,KAAO,GACrB,KAAK,QAAQ,CAAC,qBAAwB,CAAG,IAAI,MAAM,EAAS,CACxD,IAAI,CAAO,CAAE,CAAI,EACb,GAAI,AAAS,QAAQ,GACjB,MAAO,CAAC,EAAK,IAAQ,EAAQ,IAAI,CAAC,EAAK,GAE3C,IAAI,EAAS,CAAC,GAAG,IAAS,EAAQ,IAAI,CAAC,AAAC,GAAQ,CAAC,EAAG,CAAG,CAAC,EAAA,AAAK,KAAK,IAElE,OADA,EAAO,IAAI,CAAG,CAAC,EAAK,IAAQ,EAAQ,IAAI,CAAC,AAAC,GAAQ,CAAG,CAAC,EAAK,EAAE,IAAI,CAAC,EAAK,GAChE,CACX,CACJ"}