module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61146,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},34057,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],34057);var b,c=a.i(45535);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(20463),a.i(741),a.i(98468),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(26974),h=a.i(60358);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(62360);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},91795,a=>{"use strict";a.s(["normalizeAppPath",()=>c],91795);var b=a.i(2105);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},24478,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(20463);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},3727,a=>{a.n(a.i(14188))},44113,(a,b,c)=>{},23109,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(58730).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/school-management-system/src/app/(dash)/admin/marks/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/school-management-system/src/app/(dash)/admin/marks/page.tsx <module evaluation>","default")},73739,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(58730).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/school-management-system/src/app/(dash)/admin/marks/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/school-management-system/src/app/(dash)/admin/marks/page.tsx","default")},11514,a=>{"use strict";a.i(23109);var b=a.i(73739);a.n(b)}];

//# sourceMappingURL=%5Broot-of-the-server%5D__9d28f14d._.js.map