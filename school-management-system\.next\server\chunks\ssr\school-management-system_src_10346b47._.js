module.exports=[4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},5492,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},39036,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(41825),c=a.i(54159),d=a.i(25384),e=a.i(2331),f=a.i(5492),g=a.i(4082);function h(){let{data:a,status:h}=(0,d.useSession)(),[i,j]=(0,c.useState)({email:"<EMAIL>",password:"Admin@12345"}),[k,l]=(0,c.useState)(!1),[m,n]=(0,c.useState)(null),o=async()=>{l(!0),n(null);try{let a=await (0,d.signIn)("credentials",{email:i.email,password:i.password,redirect:!1});n(a)}catch(a){n({error:a.message})}finally{l(!1)}};return(0,b.jsx)("div",{className:"min-h-screen p-8 bg-gray-50",children:(0,b.jsx)("div",{className:"max-w-4xl mx-auto space-y-6",children:(0,b.jsxs)(g.Card,{children:[(0,b.jsx)(g.CardHeader,{children:(0,b.jsx)(g.CardTitle,{children:"Login Test Page"})}),(0,b.jsxs)(g.CardContent,{className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-semibold mb-2",children:"Current Session Status:"}),(0,b.jsxs)("pre",{className:"bg-gray-100 p-3 rounded text-sm",children:["Status: ",h,a&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("br",{}),"User: ",JSON.stringify(a.user,null,2)]})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-semibold mb-2",children:"Test Direct SignIn:"}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)(f.Input,{placeholder:"Email",value:i.email,onChange:a=>j(b=>({...b,email:a.target.value}))}),(0,b.jsx)(f.Input,{type:"password",placeholder:"Password",value:i.password,onChange:a=>j(b=>({...b,password:a.target.value}))}),(0,b.jsx)(e.Button,{onClick:o,disabled:k,children:k?"Testing...":"Test SignIn"})]}),m&&(0,b.jsxs)("div",{className:"mt-4",children:[(0,b.jsx)("h4",{className:"font-semibold",children:"SignIn Result:"}),(0,b.jsx)("pre",{className:"bg-gray-100 p-3 rounded text-sm",children:JSON.stringify(m,null,2)})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-semibold mb-2",children:"Quick Test Buttons:"}),(0,b.jsx)("div",{className:"space-y-2",children:[{email:"<EMAIL>",password:"Admin@12345",role:"Admin"},{email:"<EMAIL>",password:"Teacher@12345",role:"Teacher"},{email:"<EMAIL>",password:"Student@12345",role:"Student"}].map((a,c)=>(0,b.jsxs)(e.Button,{variant:"outline",onClick:()=>{j({email:a.email,password:a.password}),setTimeout(()=>o(),100)},disabled:k,children:["Test ",a.role," Login"]},c))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-semibold mb-2",children:"Navigation Links:"}),(0,b.jsxs)("div",{className:"space-x-2",children:[(0,b.jsx)(e.Button,{variant:"outline",onClick:()=>window.location.href="/login",children:"Go to Login Page"}),(0,b.jsx)(e.Button,{variant:"outline",onClick:()=>window.location.href="/admin",children:"Go to Admin Dashboard"}),(0,b.jsx)(e.Button,{variant:"outline",onClick:()=>window.location.href="/teacher",children:"Go to Teacher Dashboard"}),(0,b.jsx)(e.Button,{variant:"outline",onClick:()=>window.location.href="/student",children:"Go to Student Dashboard"})]})]})]})]})})})}}];

//# sourceMappingURL=school-management-system_src_10346b47._.js.map