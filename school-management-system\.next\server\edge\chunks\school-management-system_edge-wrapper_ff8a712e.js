(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["chunks/school-management-system_edge-wrapper_ff8a712e.js",67919,(e,t,h)=>{self._ENTRIES||={};let n=Promise.resolve().then(()=>e.i(11129));n.catch(()=>{}),self._ENTRIES.middleware_middleware=new Proxy(n,{get(e,t){if("then"===t)return(t,h)=>e.then(t,h);let h=(...h)=>e.then(e=>(0,e[t])(...h));return h.then=(h,n)=>e.then(e=>e[t]).then(h,n),h}})}]);

//# sourceMappingURL=school-management-system_edge-wrapper_ff8a712e.js.map