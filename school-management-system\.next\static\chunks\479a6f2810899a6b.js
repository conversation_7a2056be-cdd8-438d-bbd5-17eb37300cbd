(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,t,a)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>l,"CardHeader",()=>n,"CardTitle",()=>i]);var t=e.i(53379),a=e.i(46686),r=e.i(36946);let s=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...n})});s.displayName="Card";let n=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...n})});n.displayName="CardHeader";let i=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("h3",{ref:a,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});i.displayName="CardTitle";let l=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("p",{ref:a,className:(0,r.cn)("text-sm text-gray-600 dark:text-gray-400",s),...n})});l.displayName="CardDescription";let d=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("p-6 pt-0",s),...n})});d.displayName="CardContent",a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>u],30151);var t=e.i(53379),a=e.i(46686);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return e=>{let a=!1,s=t.map(t=>{let s=r(t,e);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let e=0;e<s.length;e++){let a=s[e];"function"==typeof a?a():r(t[e],null)}}}}function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.useCallback(s(...t),t)}function i(e){let r=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){var i,l,d;let e,c,o=(c=(e=null==(l=Object.getOwnPropertyDescriptor((i=r).props,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(c=(e=null==(d=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,x=function(e,t){let a={...t};for(let r in t){let s=e[r],n=t[r];/^on[A-Z]/.test(r)?s&&n?a[r]=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];let r=n(...t);return s(...t),r}:s&&(a[r]=s):"style"===r?a[r]={...s,...n}:"className"===r&&(a[r]=[s,n].filter(Boolean).join(" "))}return{...e,...a}}(n,r.props);return r.type!==a.Fragment&&(x.ref=t?s(t,o):o),a.cloneElement(r,x)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=a.forwardRef((e,s)=>{let{children:n,...i}=e,l=a.Children.toArray(n),d=l.find(c);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,t.jsx)(r,{...i,ref:s,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,t.jsx)(r,{...i,ref:s,children:n})});return n.displayName="".concat(e,".Slot"),n}e.s(["Slot",()=>l,"createSlot",()=>i],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>n],35952);var l=i("Slot"),d=Symbol("radix.slottable");function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var o=e.i(94323),x=e.i(36946);let m=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef((e,a)=>{let{className:r,variant:s,size:n,asChild:i=!1,...d}=e;return(0,t.jsx)(i?l:"button",{className:(0,x.cn)(m({variant:s,size:n,className:r})),ref:a,...d})});u.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(46686),a=e.i(50321),r=e.i(88338),s=e.i(53379),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,a)=>{let n=(0,r.createSlot)("Primitive.".concat(a)),i=t.forwardRef((e,t)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(r?n:a,{...i,ref:t})});return i.displayName="Primitive.".concat(a),{...e,[a]:i}},{});function i(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4741,e=>{"use strict";e.s(["default",()=>i],4741);var t=e.i(46686);let a=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},r=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,a)=>{let{color:n="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:x,...m}=e;return(0,t.createElement)("svg",{ref:a,...s,width:i,height:i,stroke:n,strokeWidth:d?24*Number(l)/Number(i):l,className:r("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(m)&&{"aria-hidden":"true"},...m},[...x.map(e=>{let[a,r]=e;return(0,t.createElement)(a,r)}),...Array.isArray(o)?o:[o]])}),i=(e,s)=>{let i=(0,t.forwardRef)((i,l)=>{let{className:d,...c}=i;return(0,t.createElement)(n,{ref:l,iconNode:s,className:r("lucide-".concat(a(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...c})});return i.displayName=a(e),i}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>s,"studentNavigation",()=>r,"teacherNavigation",()=>a]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"FileText"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],a=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],r=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function s(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},92521,e=>{"use strict";e.s(["Label",()=>d],92521);var t=e.i(53379),a=e.i(46686),r=e.i(62521),s=a.forwardRef((e,a)=>(0,t.jsx)(r.Primitive.label,{...e,ref:a,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var n=e.i(94323),i=e.i(36946);let l=(0,n.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(s,{ref:a,className:(0,i.cn)(l(),r),...n})});d.displayName=s.displayName},21244,e=>{"use strict";e.s(["TrendingUp",()=>t],21244);let t=(0,e.i(4741).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4534,e=>{"use strict";e.s(["CheckCircle",()=>t],4534);let t=(0,e.i(4741).default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},43008,e=>{"use strict";e.s(["XCircle",()=>t],43008);let t=(0,e.i(4741).default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},35229,e=>{"use strict";e.s(["Filter",()=>t],35229);let t=(0,e.i(4741).default)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},4270,e=>{"use strict";e.s(["default",()=>p]);var t=e.i(53379),a=e.i(46686),r=e.i(32668),s=e.i(30151),n=e.i(92521),i=e.i(89559),l=e.i(10535),d=e.i(21244),c=e.i(96274),o=e.i(23178),x=e.i(35229),m=e.i(35255),u=e.i(4534),h=e.i(43008),f=e.i(84633);function p(){let[e,p]=(0,a.useState)([]),[g,y]=(0,a.useState)("all"),[j,b]=(0,a.useState)("all"),[v,N]=(0,a.useState)("all");(0,a.useEffect)(()=>{p([{id:"1",studentName:"John Doe",admissionNo:"STU001",examName:"Unit Test 1",subjectName:"Mathematics",className:"Grade 8",sectionName:"A",obtainedMarks:45,maxMarks:50,percentage:90,grade:"A+",gradedBy:"Mrs. Smith",date:"2024-10-15"},{id:"2",studentName:"Jane Smith",admissionNo:"STU002",examName:"Unit Test 1",subjectName:"Mathematics",className:"Grade 8",sectionName:"A",obtainedMarks:38,maxMarks:50,percentage:76,grade:"B+",gradedBy:"Mrs. Smith",date:"2024-10-15"},{id:"3",studentName:"Mike Johnson",admissionNo:"STU003",examName:"Mid Term Exam",subjectName:"English",className:"Grade 8",sectionName:"A",obtainedMarks:85,maxMarks:100,percentage:85,grade:"A",gradedBy:"Mr. Brown",date:"2024-11-20"}])},[]);let w={total:e.length,average:e.length>0?Math.round(e.reduce((e,t)=>e+t.percentage,0)/e.length):0,highest:Math.max(...e.map(e=>e.percentage)),lowest:Math.min(...e.map(e=>e.percentage)),passed:e.filter(e=>e.percentage>=40).length,failed:e.filter(e=>e.percentage<40).length};return(0,t.jsx)(i.default,{title:"Marks Management",navigation:f.adminNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Marks Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Monitor and manage student examination marks"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(s.Button,{variant:"outline",children:[(0,t.jsx)(o.Download,{className:"w-4 h-4 mr-2"}),"Export Marks"]}),(0,t.jsxs)(s.Button,{children:[(0,t.jsx)(m.Plus,{className:"w-4 h-4 mr-2"}),"Add Marks"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6",children:[(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Total Records"}),(0,t.jsx)(c.FileText,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:w.total})})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Average Score"}),(0,t.jsx)(d.TrendingUp,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[w.average,"%"]})})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Highest Score"}),(0,t.jsx)(l.Award,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[w.highest,"%"]})})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Lowest Score"}),(0,t.jsx)(d.TrendingUp,{className:"h-4 w-4 text-red-600"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[w.lowest,"%"]})})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Passed"}),(0,t.jsx)(u.CheckCircle,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:w.passed})})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Failed"}),(0,t.jsx)(h.XCircle,{className:"h-4 w-4 text-red-600"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:w.failed})})]})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsx)(r.CardHeader,{children:(0,t.jsxs)(r.CardTitle,{className:"flex items-center",children:[(0,t.jsx)(x.Filter,{className:"w-5 h-5 mr-2"}),"Filters"]})}),(0,t.jsx)(r.CardContent,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"exam",children:"Exam"}),(0,t.jsxs)("select",{id:"exam",value:g,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Exams"}),(0,t.jsx)("option",{value:"unit1",children:"Unit Test 1"}),(0,t.jsx)("option",{value:"midterm",children:"Mid Term Exam"}),(0,t.jsx)("option",{value:"final",children:"Final Exam"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"subject",children:"Subject"}),(0,t.jsxs)("select",{id:"subject",value:j,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Subjects"}),(0,t.jsx)("option",{value:"math",children:"Mathematics"}),(0,t.jsx)("option",{value:"english",children:"English"}),(0,t.jsx)("option",{value:"science",children:"Science"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"class",children:"Class"}),(0,t.jsxs)("select",{id:"class",value:v,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Classes"}),(0,t.jsx)("option",{value:"grade8",children:"Grade 8"}),(0,t.jsx)("option",{value:"grade9",children:"Grade 9"}),(0,t.jsx)("option",{value:"grade10",children:"Grade 10"})]})]})]})})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{children:[(0,t.jsx)(r.CardTitle,{children:"Marks Records"}),(0,t.jsx)(r.CardDescription,{children:"Examination marks and grades for all students"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Exam"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subject"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Class"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Marks"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentage"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Grade"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Graded By"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.studentName.split(" ").map(e=>e[0]).join("")})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.studentName}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.admissionNo})]})]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.examName}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.subjectName}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.className," - ",e.sectionName]}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.obtainedMarks,"/",e.maxMarks]}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.percentage,"%"]}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((e=>{switch(e){case"A+":case"A":return"bg-green-100 text-green-800";case"B+":case"B":return"bg-blue-100 text-blue-800";case"C+":case"C":return"bg-yellow-100 text-yellow-800";case"D":return"bg-orange-100 text-orange-800";case"F":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.grade)),children:e.grade})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.gradedBy}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:"Edit"}),(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:"View"})]})})]},e.id))})]})})})]})]})})}}]);