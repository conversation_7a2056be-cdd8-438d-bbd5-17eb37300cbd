module.exports=[11090,a=>{"use strict";a.s(["Label",()=>i],11090);var b=a.i(41825),c=a.i(54159),d=a.i(36870),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(24311),g=a.i(18688);let h=(0,f.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e,{ref:d,className:(0,g.cn)(h(),a),...c}));i.displayName=e.displayName},75422,a=>{"use strict";a.s(["Alert",()=>g,"AlertDescription",()=>h]);var b=a.i(41825),c=a.i(54159),d=a.i(24311),e=a.i(18688);let f=(0,d.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),g=c.forwardRef(({className:a,variant:c,...d},g)=>(0,b.jsx)("div",{ref:g,role:"alert",className:(0,e.cn)(f({variant:c}),a),...d}));g.displayName="Alert",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("h5",{ref:d,className:(0,e.cn)("mb-1 font-medium leading-none tracking-tight",a),...c})).displayName="AlertTitle";let h=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:(0,e.cn)("text-sm [&_p]:leading-relaxed",a),...c}));h.displayName="AlertDescription"},36870,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(54159),c=a.i(81453),d=a.i(54472),e=a.i(41825),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},81453,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactDOM},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},5492,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},15416,a=>{"use strict";a.s(["default",()=>j]);var b=a.i(41825),c=a.i(54159),d=a.i(52963),e=a.i(2331),f=a.i(5492),g=a.i(11090),h=a.i(4082),i=a.i(75422);function j({classData:a,date:j=new Date().toISOString().split("T")[0]}){let k=(0,d.useRouter)(),[l,m]=(0,c.useState)(!1),[n,o]=(0,c.useState)(""),[p,q]=(0,c.useState)(""),[r,s]=(0,c.useState)(j),[t,u]=(0,c.useState)(a||null),[v,w]=(0,c.useState)([]),[x,y]=(0,c.useState)({});(0,c.useEffect)(()=>{(async()=>{try{let a=await fetch("/api/admin/classes");if(a.ok){let b=await a.json();w(b.classes)}}catch(a){console.error("Error fetching classes:",a)}})()},[]),(0,c.useEffect)(()=>{if(t&&t.students){let a={};t.students.forEach(b=>{a[b.id]="PRESENT"}),y(a)}},[t]);let z=async a=>{if(a.preventDefault(),!t)return void o("Please select a class");m(!0),o(""),q("");try{let a=Object.entries(x).map(([a,b])=>({studentId:parseInt(a),status:b})),b={classId:t.id,date:r,attendanceRecords:a},c=await fetch("/api/teacher/attendance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)}),d=await c.json();if(!c.ok)throw Error(d.error||"Failed to mark attendance");q(d.message),setTimeout(()=>{k.push("/teacher/attendance")},2e3)}catch(a){o(a.message)}finally{m(!1)}};return(0,b.jsxs)(h.Card,{className:"w-full max-w-4xl mx-auto",children:[(0,b.jsx)(h.CardHeader,{children:(0,b.jsx)(h.CardTitle,{children:"Mark Attendance"})}),(0,b.jsx)(h.CardContent,{children:(0,b.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[n&&(0,b.jsx)(i.Alert,{variant:"destructive",children:(0,b.jsx)(i.AlertDescription,{children:n})}),p&&(0,b.jsx)(i.Alert,{children:(0,b.jsx)(i.AlertDescription,{children:p})}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"class",children:"Class *"}),(0,b.jsxs)("select",{id:"class",value:t?.id||"",onChange:a=>{let b=parseInt(a.target.value);u(v.find(a=>a.id===b)||null)},required:!0,disabled:l,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,b.jsx)("option",{value:"",children:"Select Class"}),v.map(a=>(0,b.jsxs)("option",{value:a.id,children:[a.name," ",a.section.name]},a.id))]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"date",children:"Date *"}),(0,b.jsx)(f.Input,{id:"date",type:"date",value:r,onChange:a=>s(a.target.value),required:!0,disabled:l})]})]}),t&&(0,b.jsxs)("div",{children:[(0,b.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Students in ",t.name," ",t.section.name]}),(0,b.jsx)("div",{className:"space-y-3",children:t.students.map(a=>(0,b.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)("div",{className:"font-medium",children:[a.firstName," ",a.lastName]}),(0,b.jsxs)("div",{className:"text-sm text-gray-500",children:["Roll No: ",a.rollNumber]})]}),(0,b.jsx)("div",{className:"flex gap-2",children:["PRESENT","ABSENT","LATE","HALF_DAY"].map(c=>(0,b.jsx)("button",{type:"button",onClick:()=>{var b;return b=a.id,void y(a=>({...a,[b]:c}))},className:`px-3 py-1 rounded text-sm font-medium transition-colors ${x[a.id]===c?(a=>{switch(a){case"PRESENT":return"bg-green-100 text-green-800";case"ABSENT":return"bg-red-100 text-red-800";case"LATE":return"bg-yellow-100 text-yellow-800";case"HALF_DAY":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}})(c):"bg-gray-100 text-gray-600 hover:bg-gray-200"}`,children:c},c))})]},a.id))}),(0,b.jsxs)("div",{className:"mt-6 flex justify-between items-center",children:[(0,b.jsxs)("div",{className:"text-sm text-gray-600",children:["Total Students: ",t.students?.length||0]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(e.Button,{type:"button",variant:"outline",onClick:()=>{let a={};t.students?.forEach(b=>{a[b.id]="PRESENT"}),y(a)},disabled:l,children:"Mark All Present"}),(0,b.jsx)(e.Button,{type:"button",variant:"outline",onClick:()=>{let a={};t.students?.forEach(b=>{a[b.id]="ABSENT"}),y(a)},disabled:l,children:"Mark All Absent"})]})]})]}),(0,b.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,b.jsx)(e.Button,{type:"button",variant:"outline",onClick:()=>k.back(),disabled:l,children:"Cancel"}),(0,b.jsx)(e.Button,{type:"submit",disabled:l||!t,children:l?"Saving...":"Mark Attendance"})]})]})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__2ae0c0b0._.js.map