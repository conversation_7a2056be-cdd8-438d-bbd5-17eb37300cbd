module.exports=[4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},75422,a=>{"use strict";a.s(["Alert",()=>g,"AlertDescription",()=>h]);var b=a.i(41825),c=a.i(54159),d=a.i(24311),e=a.i(18688);let f=(0,d.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),g=c.forwardRef(({className:a,variant:c,...d},g)=>(0,b.jsx)("div",{ref:g,role:"alert",className:(0,e.cn)(f({variant:c}),a),...d}));g.displayName="Alert",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("h5",{ref:d,className:(0,e.cn)("mb-1 font-medium leading-none tracking-tight",a),...c})).displayName="AlertTitle";let h=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:(0,e.cn)("text-sm [&_p]:leading-relaxed",a),...c}));h.displayName="AlertDescription"},12336,a=>{"use strict";a.s(["Badge",()=>f]);var b=a.i(41825),c=a.i(24311),d=a.i(18688);let e=(0,c.cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",secondary:"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"}},defaultVariants:{variant:"default"}});function f({className:a,variant:c,...f}){return(0,b.jsx)("div",{className:(0,d.cn)(e({variant:c}),a),...f})}},23757,a=>{"use strict";a.s(["default",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(12336),e=a.i(4082),f=a.i(75422);function g(){let[a,g]=(0,c.useState)([]),[h,i]=(0,c.useState)({total:0,present:0,absent:0,late:0,halfDay:0,percentage:0}),[j,k]=(0,c.useState)(!0),[l,m]=(0,c.useState)("");(0,c.useEffect)(()=>{(async()=>{try{k(!0);let a=await fetch("/api/student/attendance");if(!a.ok)throw Error("Failed to fetch attendance");let b=await a.json();g(b.attendanceRecords),i(b.statistics)}catch(a){m(a.message)}finally{k(!1)}})()},[]);let n=a=>{switch(a){case"PRESENT":return"bg-green-100 text-green-800";case"ABSENT":return"bg-red-100 text-red-800";case"LATE":return"bg-yellow-100 text-yellow-800";case"HALF_DAY":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},o=a=>new Date(a).toLocaleDateString();return j?(0,b.jsx)("div",{className:"flex justify-center p-8",children:(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-xl sm:text-2xl lg:text-3xl font-bold",children:"My Attendance"}),(0,b.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"View your attendance records and statistics."})]}),l&&(0,b.jsx)(f.Alert,{variant:"destructive",children:(0,b.jsx)(f.AlertDescription,{children:l})}),(0,b.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4",children:[(0,b.jsxs)(e.Card,{children:[(0,b.jsx)(e.CardHeader,{className:"pb-2",children:(0,b.jsx)(e.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Total Days"})}),(0,b.jsx)(e.CardContent,{children:(0,b.jsx)("div",{className:"text-xl sm:text-2xl font-bold",children:h.total})})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsx)(e.CardHeader,{className:"pb-2",children:(0,b.jsx)(e.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Present"})}),(0,b.jsx)(e.CardContent,{children:(0,b.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-green-600",children:h.present})})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsx)(e.CardHeader,{className:"pb-2",children:(0,b.jsx)(e.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Absent"})}),(0,b.jsx)(e.CardContent,{children:(0,b.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-red-600",children:h.absent})})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsx)(e.CardHeader,{className:"pb-2",children:(0,b.jsx)(e.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Late"})}),(0,b.jsx)(e.CardContent,{children:(0,b.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-yellow-600",children:h.late})})]}),(0,b.jsxs)(e.Card,{className:"col-span-2 sm:col-span-1",children:[(0,b.jsx)(e.CardHeader,{className:"pb-2",children:(0,b.jsx)(e.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Attendance %"})}),(0,b.jsx)(e.CardContent,{children:(0,b.jsxs)("div",{className:"text-xl sm:text-2xl font-bold text-blue-600",children:[h.percentage,"%"]})})]})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsx)(e.CardHeader,{children:(0,b.jsx)(e.CardTitle,{children:"Recent Attendance Records"})}),(0,b.jsx)(e.CardContent,{children:0===a.length?(0,b.jsx)("div",{className:"text-center py-8",children:(0,b.jsx)("p",{className:"text-gray-500",children:"No attendance records found."})}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("div",{className:"hidden md:block overflow-x-auto",children:(0,b.jsxs)("table",{className:"w-full",children:[(0,b.jsx)("thead",{children:(0,b.jsxs)("tr",{className:"border-b",children:[(0,b.jsx)("th",{className:"text-left p-2",children:"Date"}),(0,b.jsx)("th",{className:"text-left p-2",children:"Class"}),(0,b.jsx)("th",{className:"text-left p-2",children:"Status"}),(0,b.jsx)("th",{className:"text-left p-2",children:"Remarks"})]})}),(0,b.jsx)("tbody",{children:a.map(a=>(0,b.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,b.jsx)("td",{className:"p-2",children:(0,b.jsx)("div",{className:"font-medium",children:o(a.date)})}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsxs)(d.Badge,{variant:"secondary",children:[a.class.name," ",a.class.section.name]})}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsx)(d.Badge,{className:n(a.status),children:a.status})}),(0,b.jsx)("td",{className:"p-2",children:a.remarks||"-"})]},a.id))})]})}),(0,b.jsx)("div",{className:"md:hidden space-y-4",children:a.map(a=>(0,b.jsx)(e.Card,{className:"p-4",children:(0,b.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-start justify-between",children:[(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:o(a.date)}),(0,b.jsx)("div",{className:"mt-1",children:(0,b.jsxs)(d.Badge,{variant:"secondary",className:"text-xs",children:[a.class.name," ",a.class.section.name]})})]}),(0,b.jsx)("div",{className:"ml-4",children:(0,b.jsx)(d.Badge,{className:n(a.status),children:a.status})})]}),a.remarks&&(0,b.jsxs)("div",{className:"text-sm",children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Remarks:"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:a.remarks})]})]})},a.id))})]})})]})]})}}];

//# sourceMappingURL=school-management-system_src_65f0929e._.js.map