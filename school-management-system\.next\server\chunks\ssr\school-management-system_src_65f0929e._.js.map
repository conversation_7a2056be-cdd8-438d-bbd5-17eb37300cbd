{"version": 3, "sources": ["turbopack:///[project]/school-management-system/src/components/ui/card.tsx", "turbopack:///[project]/school-management-system/src/components/ui/alert.tsx", "turbopack:///[project]/school-management-system/src/components/ui/badge.tsx", "turbopack:///[project]/school-management-system/src/app/(dash)/student/attendance/page.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n", "import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800\",\r\n        destructive:\r\n          \"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n", "import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        secondary:\r\n          \"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        destructive:\r\n          \"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline: \"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\n\ninterface AttendanceRecord {\n  id: number;\n  date: string;\n  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'HALF_DAY';\n  remarks?: string;\n  class: {\n    id: number;\n    name: string;\n    section: {\n      name: string;\n    };\n  };\n}\n\ninterface AttendanceStats {\n  total: number;\n  present: number;\n  absent: number;\n  late: number;\n  halfDay: number;\n  percentage: number;\n}\n\nexport default function StudentAttendancePage() {\n  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);\n  const [stats, setStats] = useState<AttendanceStats>({\n    total: 0,\n    present: 0,\n    absent: 0,\n    late: 0,\n    halfDay: 0,\n    percentage: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    const fetchAttendance = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('/api/student/attendance');\n        if (!response.ok) {\n          throw new Error('Failed to fetch attendance');\n        }\n\n        const data = await response.json();\n        setAttendanceRecords(data.attendanceRecords);\n        setStats(data.statistics);\n      } catch (err: any) {\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchAttendance();\n  }, []);\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PRESENT': return 'bg-green-100 text-green-800';\n      case 'ABSENT': return 'bg-red-100 text-red-800';\n      case 'LATE': return 'bg-yellow-100 text-yellow-800';\n      case 'HALF_DAY': return 'bg-orange-100 text-orange-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center p-8\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-xl sm:text-2xl lg:text-3xl font-bold\">My Attendance</h1>\n        <p className=\"text-sm sm:text-base text-gray-600\">View your attendance records and statistics.</p>\n      </div>\n\n      {error && (\n        <Alert variant=\"destructive\">\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      {/* Attendance Statistics */}\n      <div className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4\">\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-xs sm:text-sm font-medium\">Total Days</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-xl sm:text-2xl font-bold\">{stats.total}</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-xs sm:text-sm font-medium\">Present</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-xl sm:text-2xl font-bold text-green-600\">{stats.present}</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-xs sm:text-sm font-medium\">Absent</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-xl sm:text-2xl font-bold text-red-600\">{stats.absent}</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-xs sm:text-sm font-medium\">Late</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-xl sm:text-2xl font-bold text-yellow-600\">{stats.late}</div>\n          </CardContent>\n        </Card>\n        <Card className=\"col-span-2 sm:col-span-1\">\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-xs sm:text-sm font-medium\">Attendance %</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-xl sm:text-2xl font-bold text-blue-600\">{stats.percentage}%</div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Attendance Records */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Attendance Records</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {attendanceRecords.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">No attendance records found.</p>\n            </div>\n          ) : (\n            <>\n              {/* Desktop Table */}\n              <div className=\"hidden md:block overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead>\n                    <tr className=\"border-b\">\n                      <th className=\"text-left p-2\">Date</th>\n                      <th className=\"text-left p-2\">Class</th>\n                      <th className=\"text-left p-2\">Status</th>\n                      <th className=\"text-left p-2\">Remarks</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {attendanceRecords.map((record) => (\n                      <tr key={record.id} className=\"border-b hover:bg-gray-50\">\n                        <td className=\"p-2\">\n                          <div className=\"font-medium\">{formatDate(record.date)}</div>\n                        </td>\n                        <td className=\"p-2\">\n                          <Badge variant=\"secondary\">\n                            {record.class.name} {record.class.section.name}\n                          </Badge>\n                        </td>\n                        <td className=\"p-2\">\n                          <Badge className={getStatusColor(record.status)}>\n                            {record.status}\n                          </Badge>\n                        </td>\n                        <td className=\"p-2\">\n                          {record.remarks || '-'}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Mobile Cards */}\n              <div className=\"md:hidden space-y-4\">\n                {attendanceRecords.map((record) => (\n                  <Card key={record.id} className=\"p-4\">\n                    <div className=\"flex flex-col space-y-3\">\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1 min-w-0\">\n                          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n                            {formatDate(record.date)}\n                          </h3>\n                          <div className=\"mt-1\">\n                            <Badge variant=\"secondary\" className=\"text-xs\">\n                              {record.class.name} {record.class.section.name}\n                            </Badge>\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <Badge className={getStatusColor(record.status)}>\n                            {record.status}\n                          </Badge>\n                        </div>\n                      </div>\n                      \n                      {record.remarks && (\n                        <div className=\"text-sm\">\n                          <span className=\"font-medium text-gray-700 dark:text-gray-300\">Remarks:</span>\n                          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">{record.remarks}</p>\n                        </div>\n                      )}\n                    </div>\n                  </Card>\n                ))}\n              </div>\n            </>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": "4JAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAO,EAAA,UAAgB,CAG3B,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,8HACA,GAED,GAAG,CAAK,IAGb,EAAK,WAAW,CAAG,OAEnB,IAAM,EAAa,EAAA,UAAgB,CAGjC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,gCAAiC,GAC9C,GAAG,CAAK,IAGb,EAAW,WAAW,CAAG,aAEzB,IAAM,EAAY,EAAA,UAAgB,CAGhC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAE,AAAF,EACT,qDACA,GAED,GAAG,CAAK,IAGb,EAAU,WAAW,CAAG,YAExB,IAAM,EAAkB,EAAA,UAAgB,CAGtC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,2CAA4C,GACzD,GAAG,CAAK,IAGb,EAAgB,WAAW,CAAG,kBAE9B,IAAM,EAAc,EAAA,UAAgB,CAGlC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,IAAK,EAAK,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,WAAY,GAAa,GAAG,CAAK,IAEhE,EAAY,WAAW,CAAG,cAY1B,AAVmB,EAAA,UAAgB,CAGjC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,6BAA8B,GAC3C,GAAG,CAAK,IAGF,WAAW,CAAG,oGC3EzB,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAgB,CAAA,EAAA,EAAA,GAAG,AAAH,EACpB,qLACA,CACE,SAAU,CACR,QAAS,CACP,QAAS,kGACT,YACE,4IACJ,CACF,EACA,gBAAiB,CACf,QAAS,SACX,CACF,GAGI,EAAQ,EAAA,UAAgB,CAG5B,CAAC,WAAE,CAAS,SAAE,CAAO,CAAE,GAAG,EAAO,CAAE,IACnC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,IAAK,EACL,KAAK,QACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,EAAc,SAAE,CAAQ,GAAI,GACzC,GAAG,CAAK,IAGb,EAAM,WAAW,CAAG,QAYpB,AAVmB,EAAA,UAAgB,CAGjC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAE,AAAF,EAAG,+CAAgD,GAC7D,GAAG,CAAK,IAGF,WAAW,CAAG,aAEzB,IAAM,EAAmB,EAAA,UAAgB,CAGvC,CAAC,CAAE,WAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,gCAAiC,GAC9C,GAAG,CAAK,IAGb,EAAiB,WAAW,CAAG,iFCtD/B,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAgB,CAAA,EAAA,EAAA,GAAA,AAAG,EACvB,6KACA,CACE,SAAU,CACR,QAAS,CACP,QACE,sGACF,UACE,4HACF,YACE,kGACF,QAAS,uEACX,CACF,EACA,gBAAiB,CACf,QAAS,SACX,CACF,GAOF,SAAS,EAAM,WAAE,CAAS,SAAE,CAAO,CAAE,GAAG,EAAmB,EACzD,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,EAAc,SAAE,CAAQ,GAAI,GAAa,GAAG,CAAK,EAExE,kEC/BA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAyBe,SAAS,IACtB,GAAM,CAAC,EAAmB,EAAqB,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAA6B,EAAE,EAC3E,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAkB,CAClD,MAAO,EACP,QAAS,EACT,OAAQ,EACR,KAAM,EACN,QAAS,EACT,WAAY,CACd,GACM,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAEnC,CAAA,EAAA,EAAA,SAAS,AAAT,EAAU,KAmBR,CAlBwB,UACtB,GAAI,CACF,GAAW,GACX,IAAM,EAAW,MAAM,MAAM,2BAC7B,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACV,AAAI,MAAM,8BAGlB,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,EAAqB,EAAK,iBAAiB,EAC3C,EAAS,EAAK,UAAU,CAC1B,CAAE,MAAO,EAAU,CACjB,EAAS,EAAI,OAAO,CACtB,QAAU,CACR,GAAW,EACb,EACF,GAGF,EAAG,EAAE,EAEL,IAAM,EAAiB,AAAC,IACtB,OAAQ,GACN,IAAK,UAAW,MAAO,6BACvB,KAAK,SAAU,MAAO,yBACtB,KAAK,OAAQ,MAAO,+BACpB,KAAK,WAAY,MAAO,+BACxB,SAAS,MAAO,2BAClB,CACF,EAEM,EAAa,AAAC,GACX,IAAI,KAAK,GAAY,kBAAkB,UAGhD,AAAI,EAEA,CAAA,EAAA,EAAA,EAFS,CAET,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mEAMnB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,qDAA4C,kBAC1D,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8CAAqC,oDAGnD,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,uBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,gBAAgB,CAAA,UAAE,MAKvB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,0CAAiC,iBAExD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yCAAiC,EAAM,KAAK,QAG/D,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,0CAAiC,cAExD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wDAAgD,EAAM,OAAO,QAGhF,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,0CAAiC,aAExD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sDAA8C,EAAM,MAAM,QAG7E,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,0CAAiC,WAExD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yDAAiD,EAAM,IAAI,QAG9E,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,qCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,0CAAiC,mBAExD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wDAA+C,EAAM,UAAU,CAAC,eAMrF,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,UACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,gCAEb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACoB,IAA7B,EAAkB,MAAM,CACvB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,mCAG/B,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WAEE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAM,UAAU,mBACf,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,UACC,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,qBACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,SAC9B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,UAC9B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,WAC9B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,iBAGlC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,UACE,EAAkB,GAAG,CAAC,AAAC,GACtB,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAmB,UAAU,sCAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uBAAe,EAAW,EAAO,IAAI,MAEtD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eACZ,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,sBACZ,EAAO,KAAK,CAAC,IAAI,CAAC,IAAE,EAAO,KAAK,CAAC,OAAO,CAAC,IAAI,MAGlD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eACZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAW,EAAe,EAAO,MAAM,WAC3C,EAAO,MAAM,KAGlB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eACX,EAAO,OAAO,EAAI,QAfd,EAAO,EAAE,UAwB1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+BACZ,EAAkB,GAAG,CAAC,AAAC,GACtB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAiB,UAAU,eAC9B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,gEACX,EAAW,EAAO,IAAI,IAEzB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,YAAY,UAAU,oBAClC,EAAO,KAAK,CAAC,IAAI,CAAC,IAAE,EAAO,KAAK,CAAC,OAAO,CAAC,IAAI,SAIpD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAW,EAAe,EAAO,MAAM,WAC3C,EAAO,MAAM,QAKnB,EAAO,OAAO,EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oBACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,wDAA+C,aAC/D,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iDAAyC,EAAO,OAAO,UAvBjE,EAAO,EAAE,eAoCtC"}