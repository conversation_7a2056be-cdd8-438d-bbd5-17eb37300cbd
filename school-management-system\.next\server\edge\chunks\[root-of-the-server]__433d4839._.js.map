{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/school-management-system/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware'\r\nimport { NextResponse } from 'next/server'\r\n\r\nexport default withAuth(\r\n  function middleware(req) {\r\n    const token = req.nextauth.token\r\n    const { pathname } = req.nextUrl\r\n\r\n    // If no token, redirect to login\r\n    if (!token) {\r\n      return NextResponse.redirect(new URL('/login', req.url))\r\n    }\r\n\r\n    const userRole = token.role as string\r\n\r\n    // Role-based route protection\r\n    if (pathname.startsWith('/admin')) {\r\n      if (userRole !== 'ADMIN') {\r\n        return NextResponse.redirect(new URL('/unauthorized', req.url))\r\n      }\r\n    }\r\n\r\n    if (pathname.startsWith('/teacher')) {\r\n      if (userRole !== 'TEACHER' && userRole !== 'ADMIN') {\r\n        return NextResponse.redirect(new URL('/unauthorized', req.url))\r\n      }\r\n    }\r\n\r\n    if (pathname.startsWith('/student')) {\r\n      if (userRole !== 'STUDENT' && userRole !== 'ADMIN') {\r\n        return NextResponse.redirect(new URL('/unauthorized', req.url))\r\n      }\r\n    }\r\n\r\n    // API route protection\r\n    if (pathname.startsWith('/api/admin')) {\r\n      if (userRole !== 'ADMIN') {\r\n        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\r\n      }\r\n    }\r\n\r\n    if (pathname.startsWith('/api/teacher')) {\r\n      if (userRole !== 'TEACHER' && userRole !== 'ADMIN') {\r\n        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\r\n      }\r\n    }\r\n\r\n    if (pathname.startsWith('/api/student')) {\r\n      if (userRole !== 'STUDENT' && userRole !== 'ADMIN') {\r\n        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\r\n      }\r\n    }\r\n\r\n    return NextResponse.next()\r\n  },\r\n  {\r\n    callbacks: {\r\n      authorized: ({ token }) => !!token\r\n    }\r\n  }\r\n)\r\n\r\nexport const config = {\r\n  matcher: [\r\n    '/admin/:path*',\r\n    '/teacher/:path*',\r\n    '/student/:path*',\r\n    '/api/admin/:path*',\r\n    '/api/teacher/:path*',\r\n    '/api/student/:path*'\r\n  ]\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;;;uCAEe,IAAA,4SAAQ,EACrB,SAAS,WAAW,GAAG;IACrB,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAEhC,iCAAiC;IACjC,IAAI,CAAC,OAAO;QACV,OAAO,iVAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;IACxD;IAEA,MAAM,WAAW,MAAM,IAAI;IAE3B,8BAA8B;IAC9B,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,IAAI,aAAa,SAAS;YACxB,OAAO,iVAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,iBAAiB,IAAI,GAAG;QAC/D;IACF;IAEA,IAAI,SAAS,UAAU,CAAC,aAAa;QACnC,IAAI,aAAa,aAAa,aAAa,SAAS;YAClD,OAAO,iVAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,iBAAiB,IAAI,GAAG;QAC/D;IACF;IAEA,IAAI,SAAS,UAAU,CAAC,aAAa;QACnC,IAAI,aAAa,aAAa,aAAa,SAAS;YAClD,OAAO,iVAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,iBAAiB,IAAI,GAAG;QAC/D;IACF;IAEA,uBAAuB;IACvB,IAAI,SAAS,UAAU,CAAC,eAAe;QACrC,IAAI,aAAa,SAAS;YACxB,OAAO,iVAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;IACF;IAEA,IAAI,SAAS,UAAU,CAAC,iBAAiB;QACvC,IAAI,aAAa,aAAa,aAAa,SAAS;YAClD,OAAO,iVAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;IACF;IAEA,IAAI,SAAS,UAAU,CAAC,iBAAiB;QACvC,IAAI,aAAa,aAAa,aAAa,SAAS;YAClD,OAAO,iVAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;IACF;IAEA,OAAO,iVAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC,CAAC;IAC/B;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;KACD;AACH"}}]}