module.exports=[75422,a=>{"use strict";a.s(["Alert",()=>g,"AlertDescription",()=>h]);var b=a.i(41825),c=a.i(54159),d=a.i(24311),e=a.i(18688);let f=(0,d.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),g=c.forwardRef(({className:a,variant:c,...d},g)=>(0,b.jsx)("div",{ref:g,role:"alert",className:(0,e.cn)(f({variant:c}),a),...d}));g.displayName="Alert",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("h5",{ref:d,className:(0,e.cn)("mb-1 font-medium leading-none tracking-tight",a),...c})).displayName="AlertTitle";let h=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:(0,e.cn)("text-sm [&_p]:leading-relaxed",a),...c}));h.displayName="AlertDescription"},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},5492,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},12336,a=>{"use strict";a.s(["Badge",()=>f]);var b=a.i(41825),c=a.i(24311),d=a.i(18688);let e=(0,c.cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",secondary:"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"}},defaultVariants:{variant:"default"}});function f({className:a,variant:c,...f}){return(0,b.jsx)("div",{className:(0,d.cn)(e({variant:c}),a),...f})}},90086,a=>{"use strict";a.s(["default",()=>j]);var b=a.i(41825),c=a.i(54159),d=a.i(52963),e=a.i(2331),f=a.i(5492),g=a.i(12336),h=a.i(4082),i=a.i(75422);function j(){let a=(0,d.useRouter)(),[j,k]=(0,c.useState)([]),[l,m]=(0,c.useState)(!0),[n,o]=(0,c.useState)(""),[p,q]=(0,c.useState)({page:1,limit:10,total:0,totalPages:0}),[r,s]=(0,c.useState)(""),[t,u]=(0,c.useState)(""),v=async()=>{try{m(!0);let a=new URLSearchParams({page:p.page.toString(),limit:p.limit.toString(),...r&&{classId:r},...t&&{date:t}}),b=await fetch(`/api/teacher/attendance?${a}`);if(!b.ok)throw Error("Failed to fetch attendance");let c=await b.json();k(c.attendanceRecords),q(c.pagination)}catch(a){o(a.message)}finally{m(!1)}};(0,c.useEffect)(()=>{v()},[p.page,r,t]);let w=a=>{q(b=>({...b,page:a}))};return l?(0,b.jsx)("div",{className:"flex justify-center p-8",children:(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsx)("h1",{className:"text-3xl font-bold",children:"Attendance Management"}),(0,b.jsx)(e.Button,{onClick:()=>a.push("/teacher/attendance/mark"),children:"Mark Attendance"})]}),n&&(0,b.jsx)(i.Alert,{variant:"destructive",children:(0,b.jsx)(i.AlertDescription,{children:n})}),(0,b.jsxs)(h.Card,{children:[(0,b.jsx)(h.CardHeader,{children:(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,b.jsxs)(h.CardTitle,{children:["Attendance Records (",p.total,")"]}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 w-full sm:w-auto",children:[(0,b.jsx)(f.Input,{type:"date",value:t,onChange:a=>u(a.target.value),className:"w-full sm:w-auto"}),(0,b.jsxs)("select",{value:r,onChange:a=>s(a.target.value),className:"w-full sm:w-auto p-2 border border-gray-300 rounded-md",children:[(0,b.jsx)("option",{value:"",children:"All Classes"}),(0,b.jsx)("option",{value:"1",children:"Grade 8A"}),(0,b.jsx)("option",{value:"2",children:"Grade 8B"})]})]})]})}),(0,b.jsxs)(h.CardContent,{children:[0===j.length?(0,b.jsx)("div",{className:"text-center py-8",children:(0,b.jsx)("p",{className:"text-gray-500",children:"No attendance records found."})}):(0,b.jsx)("div",{className:"overflow-x-auto",children:(0,b.jsxs)("table",{className:"w-full",children:[(0,b.jsx)("thead",{children:(0,b.jsxs)("tr",{className:"border-b",children:[(0,b.jsx)("th",{className:"text-left p-2",children:"Date"}),(0,b.jsx)("th",{className:"text-left p-2",children:"Student"}),(0,b.jsx)("th",{className:"text-left p-2",children:"Class"}),(0,b.jsx)("th",{className:"text-left p-2",children:"Status"}),(0,b.jsx)("th",{className:"text-left p-2",children:"Remarks"})]})}),(0,b.jsx)("tbody",{children:j.map(a=>(0,b.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,b.jsx)("td",{className:"p-2",children:(0,b.jsx)("div",{className:"font-medium",children:new Date(a.date).toLocaleDateString()})}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsxs)("div",{children:[(0,b.jsxs)("div",{className:"font-medium",children:[a.student.firstName," ",a.student.lastName]}),(0,b.jsxs)("div",{className:"text-sm text-gray-500",children:["Roll No: ",a.student.rollNumber]})]})}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsxs)(g.Badge,{variant:"secondary",children:[a.class.name," ",a.class.section.name]})}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsx)(g.Badge,{className:(a=>{switch(a){case"PRESENT":return"bg-green-100 text-green-800";case"ABSENT":return"bg-red-100 text-red-800";case"LATE":return"bg-yellow-100 text-yellow-800";case"HALF_DAY":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}})(a.status),children:a.status})}),(0,b.jsx)("td",{className:"p-2",children:a.remarks||"-"})]},a.id))})]})}),p.totalPages>1&&(0,b.jsxs)("div",{className:"flex justify-between items-center mt-6",children:[(0,b.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(p.page-1)*p.limit+1," to"," ",Math.min(p.page*p.limit,p.total)," of"," ",p.total," records"]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(e.Button,{variant:"outline",size:"sm",onClick:()=>w(p.page-1),disabled:p.page<=1,children:"Previous"}),(0,b.jsxs)("span",{className:"px-3 py-2 text-sm",children:["Page ",p.page," of ",p.totalPages]}),(0,b.jsx)(e.Button,{variant:"outline",size:"sm",onClick:()=>w(p.page+1),disabled:p.page>=p.totalPages,children:"Next"})]})]})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,b.jsxs)(h.Card,{children:[(0,b.jsx)(h.CardHeader,{className:"pb-2",children:(0,b.jsx)(h.CardTitle,{className:"text-sm font-medium",children:"Total Records"})}),(0,b.jsx)(h.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:p.total})})]}),(0,b.jsxs)(h.Card,{children:[(0,b.jsx)(h.CardHeader,{className:"pb-2",children:(0,b.jsx)(h.CardTitle,{className:"text-sm font-medium",children:"Present Today"})}),(0,b.jsx)(h.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:j.filter(a=>"PRESENT"===a.status&&new Date(a.date).toDateString()===new Date().toDateString()).length})})]}),(0,b.jsxs)(h.Card,{children:[(0,b.jsx)(h.CardHeader,{className:"pb-2",children:(0,b.jsx)(h.CardTitle,{className:"text-sm font-medium",children:"Absent Today"})}),(0,b.jsx)(h.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:j.filter(a=>"ABSENT"===a.status&&new Date(a.date).toDateString()===new Date().toDateString()).length})})]}),(0,b.jsxs)(h.Card,{children:[(0,b.jsx)(h.CardHeader,{className:"pb-2",children:(0,b.jsx)(h.CardTitle,{className:"text-sm font-medium",children:"Late Today"})}),(0,b.jsx)(h.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:j.filter(a=>"LATE"===a.status&&new Date(a.date).toDateString()===new Date().toDateString()).length})})]})]})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__0fff92d9._.js.map