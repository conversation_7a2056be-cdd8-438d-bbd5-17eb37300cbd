[{"C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(auth)\\login\\page.tsx": "1", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\attendance\\page.tsx": "2", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\new\\page.tsx": "3", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\page.tsx": "4", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\exams\\page.tsx": "5", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\marks\\page.tsx": "6", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\page.tsx": "7", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\reports\\page.tsx": "8", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\settings\\page.tsx": "9", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\bulk\\page.tsx": "10", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\new\\page.tsx": "11", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\page.tsx": "12", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\edit\\page.tsx": "13", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\page.tsx": "14", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\subjects\\page.tsx": "15", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\new\\page.tsx": "16", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\page.tsx": "17", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\edit\\page.tsx": "18", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\page.tsx": "19", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\attendance\\page.tsx": "20", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\marks\\page.tsx": "21", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\page.tsx": "22", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\reports\\page.tsx": "23", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\mark\\page.tsx": "24", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\page.tsx": "25", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\page.tsx": "26", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\page.tsx": "27", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\view\\page.tsx": "28", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\page.tsx": "29", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\attendance\\route.ts": "30", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\route.ts": "31", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\[id]\\route.ts": "32", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\dashboard\\stats\\route.ts": "33", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\exams\\route.ts": "34", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\marks\\route.ts": "35", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\reports\\route.ts": "36", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\sections\\route.ts": "37", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\settings\\route.ts": "38", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\bulk\\route.ts": "39", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\route.ts": "40", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\[id]\\route.ts": "41", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\subjects\\route.ts": "42", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\route.ts": "43", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\[id]\\route.ts": "44", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "45", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\attendance\\route.ts": "46", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\dashboard\\stats\\route.ts": "47", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\marks\\route.ts": "48", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\reports\\route.ts": "49", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\attendance\\route.ts": "50", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\dashboard\\stats\\route.ts": "51", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\route.ts": "52", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\[examId]\\students\\route.ts": "53", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\marks\\route.ts": "54", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\layout.tsx": "55", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\page.tsx": "56", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\test-login\\page.tsx": "57", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\unauthorized\\page.tsx": "58", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\attendance\\attendance-form.tsx": "59", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\logout-button.tsx": "60", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\protected-route.tsx": "61", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-form.tsx": "62", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-table.tsx": "63", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\layout\\dashboard-layout.tsx": "64", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\grade-calculator.tsx": "65", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-entry-form.tsx": "66", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-table.tsx": "67", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\session-provider.tsx": "68", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\theme-provider.tsx": "69", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\bulk-import.tsx": "70", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-form.tsx": "71", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-table.tsx": "72", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-form.tsx": "73", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-table.tsx": "74", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\alert.tsx": "75", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\badge.tsx": "76", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\button.tsx": "77", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\card.tsx": "78", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\dropdown-menu.tsx": "79", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\input.tsx": "80", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\label.tsx": "81", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\responsive-container.tsx": "82", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\simple-theme-toggle.tsx": "83", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\theme-toggle.tsx": "84", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\hooks\\use-login.ts": "85", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth-utils.ts": "86", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth.ts": "87", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\db.ts": "88", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\grading.ts": "89", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\marks-validation.ts": "90", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\navigation.ts": "91", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\rbac.ts": "92", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\utils.ts": "93", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\middleware.ts": "94", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\types\\next-auth.d.ts": "95"}, {"size": 6234, "mtime": 1756888610598, "results": "96", "hashOfConfig": "97"}, {"size": 13827, "mtime": 1756885850874, "results": "98", "hashOfConfig": "97"}, {"size": 633, "mtime": 1756885899712, "results": "99", "hashOfConfig": "97"}, {"size": 5017, "mtime": 1756892045824, "results": "100", "hashOfConfig": "97"}, {"size": 9702, "mtime": 1756885931424, "results": "101", "hashOfConfig": "97"}, {"size": 14512, "mtime": 1756885966343, "results": "102", "hashOfConfig": "97"}, {"size": 10752, "mtime": 1756897720880, "results": "103", "hashOfConfig": "97"}, {"size": 23334, "mtime": 1756887424139, "results": "104", "hashOfConfig": "97"}, {"size": 28080, "mtime": 1756887359141, "results": "105", "hashOfConfig": "97"}, {"size": 934, "mtime": 1756815886903, "results": "106", "hashOfConfig": "97"}, {"size": 1430, "mtime": 1756885266272, "results": "107", "hashOfConfig": "97"}, {"size": 4690, "mtime": 1756891054176, "results": "108", "hashOfConfig": "97"}, {"size": 2727, "mtime": 1756890831207, "results": "109", "hashOfConfig": "97"}, {"size": 13822, "mtime": 1756890820333, "results": "110", "hashOfConfig": "97"}, {"size": 8774, "mtime": 1756886077773, "results": "111", "hashOfConfig": "97"}, {"size": 1139, "mtime": 1756885273906, "results": "112", "hashOfConfig": "97"}, {"size": 4838, "mtime": 1756884448481, "results": "113", "hashOfConfig": "97"}, {"size": 3239, "mtime": 1756890791506, "results": "114", "hashOfConfig": "97"}, {"size": 9418, "mtime": 1756890885919, "results": "115", "hashOfConfig": "97"}, {"size": 8053, "mtime": 1756887545945, "results": "116", "hashOfConfig": "97"}, {"size": 18371, "mtime": 1756894954105, "results": "117", "hashOfConfig": "97"}, {"size": 13272, "mtime": 1756896519717, "results": "118", "hashOfConfig": "97"}, {"size": 11164, "mtime": 1756894988580, "results": "119", "hashOfConfig": "97"}, {"size": 398, "mtime": 1756798332445, "results": "120", "hashOfConfig": "97"}, {"size": 9863, "mtime": 1756798332445, "results": "121", "hashOfConfig": "97"}, {"size": 9273, "mtime": 1756894860221, "results": "122", "hashOfConfig": "97"}, {"size": 6607, "mtime": 1756894882482, "results": "123", "hashOfConfig": "97"}, {"size": 8895, "mtime": 1756894918199, "results": "124", "hashOfConfig": "97"}, {"size": 11708, "mtime": 1756896370625, "results": "125", "hashOfConfig": "97"}, {"size": 2332, "mtime": 1756886129726, "results": "126", "hashOfConfig": "97"}, {"size": 7806, "mtime": 1756892297872, "results": "127", "hashOfConfig": "97"}, {"size": 4551, "mtime": 1756886222137, "results": "128", "hashOfConfig": "97"}, {"size": 3150, "mtime": 1756896096954, "results": "129", "hashOfConfig": "97"}, {"size": 2536, "mtime": 1756814576844, "results": "130", "hashOfConfig": "97"}, {"size": 2467, "mtime": 1756814471224, "results": "131", "hashOfConfig": "97"}, {"size": 5236, "mtime": 1756814485233, "results": "132", "hashOfConfig": "97"}, {"size": 5515, "mtime": 1756799849243, "results": "133", "hashOfConfig": "97"}, {"size": 8078, "mtime": 1756891737939, "results": "134", "hashOfConfig": "97"}, {"size": 9633, "mtime": 1756812704313, "results": "135", "hashOfConfig": "97"}, {"size": 6863, "mtime": 1756891698086, "results": "136", "hashOfConfig": "97"}, {"size": 8063, "mtime": 1756885638017, "results": "137", "hashOfConfig": "97"}, {"size": 6338, "mtime": 1756816248462, "results": "138", "hashOfConfig": "97"}, {"size": 8295, "mtime": 1756891390912, "results": "139", "hashOfConfig": "97"}, {"size": 5832, "mtime": 1756891014057, "results": "140", "hashOfConfig": "97"}, {"size": 163, "mtime": 1756792184475, "results": "141", "hashOfConfig": "97"}, {"size": 4732, "mtime": 1756816268741, "results": "142", "hashOfConfig": "97"}, {"size": 4085, "mtime": 1756896137803, "results": "143", "hashOfConfig": "97"}, {"size": 1652, "mtime": 1756814576851, "results": "144", "hashOfConfig": "97"}, {"size": 1368, "mtime": 1756814576858, "results": "145", "hashOfConfig": "97"}, {"size": 8314, "mtime": 1756815108834, "results": "146", "hashOfConfig": "97"}, {"size": 4821, "mtime": 1756896118959, "results": "147", "hashOfConfig": "97"}, {"size": 1666, "mtime": 1756893659821, "results": "148", "hashOfConfig": "97"}, {"size": 2211, "mtime": 1756895800633, "results": "149", "hashOfConfig": "97"}, {"size": 7312, "mtime": 1756894402060, "results": "150", "hashOfConfig": "97"}, {"size": 945, "mtime": 1756886916185, "results": "151", "hashOfConfig": "97"}, {"size": 3287, "mtime": 1756886934351, "results": "152", "hashOfConfig": "97"}, {"size": 4644, "mtime": 1756889607556, "results": "153", "hashOfConfig": "97"}, {"size": 1775, "mtime": 1756792184480, "results": "154", "hashOfConfig": "97"}, {"size": 9653, "mtime": 1756890967660, "results": "155", "hashOfConfig": "97"}, {"size": 2569, "mtime": 1756888490440, "results": "156", "hashOfConfig": "97"}, {"size": 4796, "mtime": 1756888515488, "results": "157", "hashOfConfig": "97"}, {"size": 8108, "mtime": 1756798104289, "results": "158", "hashOfConfig": "97"}, {"size": 8908, "mtime": 1756798123208, "results": "159", "hashOfConfig": "97"}, {"size": 6869, "mtime": 1756892563597, "results": "160", "hashOfConfig": "97"}, {"size": 10055, "mtime": 1756894133538, "results": "161", "hashOfConfig": "97"}, {"size": 14550, "mtime": 1756894523296, "results": "162", "hashOfConfig": "97"}, {"size": 13419, "mtime": 1756894096579, "results": "163", "hashOfConfig": "97"}, {"size": 424, "mtime": 1756882431724, "results": "164", "hashOfConfig": "97"}, {"size": 3253, "mtime": 1756881867586, "results": "165", "hashOfConfig": "97"}, {"size": 10619, "mtime": 1756793087826, "results": "166", "hashOfConfig": "97"}, {"size": 12859, "mtime": 1756887183493, "results": "167", "hashOfConfig": "97"}, {"size": 17303, "mtime": 1756886864336, "results": "168", "hashOfConfig": "97"}, {"size": 10412, "mtime": 1756797937081, "results": "169", "hashOfConfig": "97"}, {"size": 14392, "mtime": 1756890917447, "results": "170", "hashOfConfig": "97"}, {"size": 1783, "mtime": 1756880256590, "results": "171", "hashOfConfig": "97"}, {"size": 1300, "mtime": 1756880299321, "results": "172", "hashOfConfig": "97"}, {"size": 2085, "mtime": 1756880229145, "results": "173", "hashOfConfig": "97"}, {"size": 2032, "mtime": 1756880929472, "results": "174", "hashOfConfig": "97"}, {"size": 7302, "mtime": 1756879926941, "results": "175", "hashOfConfig": "97"}, {"size": 920, "mtime": 1756880242756, "results": "176", "hashOfConfig": "97"}, {"size": 732, "mtime": 1756792184477, "results": "177", "hashOfConfig": "97"}, {"size": 2835, "mtime": 1756886907469, "results": "178", "hashOfConfig": "97"}, {"size": 1583, "mtime": 1756882485393, "results": "179", "hashOfConfig": "97"}, {"size": 1695, "mtime": 1756882497465, "results": "180", "hashOfConfig": "97"}, {"size": 5287, "mtime": 1756888536272, "results": "181", "hashOfConfig": "97"}, {"size": 5639, "mtime": 1756889574866, "results": "182", "hashOfConfig": "97"}, {"size": 2144, "mtime": 1756889548014, "results": "183", "hashOfConfig": "97"}, {"size": 288, "mtime": 1756791879185, "results": "184", "hashOfConfig": "97"}, {"size": 4030, "mtime": 1756799139956, "results": "185", "hashOfConfig": "97"}, {"size": 5462, "mtime": 1756895773964, "results": "186", "hashOfConfig": "97"}, {"size": 2316, "mtime": 1756888317388, "results": "187", "hashOfConfig": "97"}, {"size": 3183, "mtime": 1756812578167, "results": "188", "hashOfConfig": "97"}, {"size": 172, "mtime": 1756792184477, "results": "189", "hashOfConfig": "97"}, {"size": 1935, "mtime": 1756891638445, "results": "190", "hashOfConfig": "97"}, {"size": 559, "mtime": 1756798682449, "results": "191", "hashOfConfig": "97"}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13embjg", {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(auth)\\login\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\attendance\\page.tsx", ["477"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\new\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\page.tsx", ["478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\exams\\page.tsx", ["490", "491", "492", "493", "494", "495"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\marks\\page.tsx", ["496", "497", "498", "499"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\page.tsx", ["500", "501", "502", "503"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\reports\\page.tsx", ["504", "505"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\settings\\page.tsx", ["506", "507", "508", "509", "510", "511", "512"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\bulk\\page.tsx", ["513", "514", "515"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\new\\page.tsx", ["516", "517", "518", "519"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\page.tsx", ["520", "521", "522", "523", "524", "525"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\edit\\page.tsx", ["526", "527", "528", "529", "530", "531"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\page.tsx", ["532", "533", "534", "535", "536", "537", "538", "539"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\subjects\\page.tsx", ["540", "541"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\new\\page.tsx", ["542", "543", "544", "545"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\page.tsx", ["546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\edit\\page.tsx", ["558", "559"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\page.tsx", ["560", "561"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\attendance\\page.tsx", ["562"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\marks\\page.tsx", ["563", "564", "565"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\page.tsx", ["566", "567", "568", "569", "570", "571"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\reports\\page.tsx", ["572", "573"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\mark\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\page.tsx", ["574", "575"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\page.tsx", ["576", "577", "578", "579"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\page.tsx", ["580", "581", "582", "583", "584", "585", "586", "587", "588", "589"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\view\\page.tsx", ["590", "591", "592", "593", "594"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\page.tsx", ["595", "596", "597"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\attendance\\route.ts", ["598", "599", "600"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\route.ts", ["601", "602", "603", "604"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\[id]\\route.ts", ["605", "606", "607"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\dashboard\\stats\\route.ts", ["608"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\exams\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\marks\\route.ts", ["609"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\reports\\route.ts", ["610"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\sections\\route.ts", ["611"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\settings\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\bulk\\route.ts", ["612", "613"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\route.ts", ["614", "615", "616"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\[id]\\route.ts", ["617", "618", "619", "620", "621", "622"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\subjects\\route.ts", ["623", "624"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\route.ts", ["625"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\[id]\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\attendance\\route.ts", ["626", "627"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\dashboard\\stats\\route.ts", ["628"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\marks\\route.ts", ["629"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\reports\\route.ts", ["630"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\attendance\\route.ts", ["631", "632"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\dashboard\\stats\\route.ts", ["633"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\route.ts", ["634"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\[examId]\\students\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\marks\\route.ts", ["635"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\layout.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\test-login\\page.tsx", ["636"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\unauthorized\\page.tsx", ["637", "638", "639"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\attendance\\attendance-form.tsx", ["640", "641"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\logout-button.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\protected-route.tsx", ["642", "643", "644"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-form.tsx", ["645", "646"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-table.tsx", ["647"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\layout\\dashboard-layout.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\grade-calculator.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-entry-form.tsx", ["648", "649"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-table.tsx", ["650"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\session-provider.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\theme-provider.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\bulk-import.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-form.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-table.tsx", ["651"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-form.tsx", ["652", "653", "654", "655"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-table.tsx", ["656", "657", "658", "659", "660"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\alert.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\badge.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\button.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\card.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\input.tsx", ["661"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\label.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\responsive-container.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\simple-theme-toggle.tsx", ["662", "663"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\theme-toggle.tsx", ["664"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\hooks\\use-login.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth-utils.ts", ["665"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\db.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\grading.ts", ["666", "667"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\marks-validation.ts", ["668"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\navigation.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\rbac.ts", ["669", "670", "671", "672"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\utils.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\middleware.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\types\\next-auth.d.ts", ["673"], [], {"ruleId": "674", "severity": 1, "message": "675", "line": 10, "column": 3, "nodeType": null, "messageId": "676", "endLine": 10, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "677", "line": 12, "column": 3, "nodeType": null, "messageId": "676", "endLine": 12, "endColumn": 8}, {"ruleId": "674", "severity": 1, "message": "678", "line": 13, "column": 3, "nodeType": null, "messageId": "676", "endLine": 13, "endColumn": 16}, {"ruleId": "674", "severity": 1, "message": "679", "line": 14, "column": 3, "nodeType": null, "messageId": "676", "endLine": 14, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "680", "line": 15, "column": 3, "nodeType": null, "messageId": "676", "endLine": 15, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "675", "line": 16, "column": 3, "nodeType": null, "messageId": "676", "endLine": 16, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "681", "line": 17, "column": 3, "nodeType": null, "messageId": "676", "endLine": 17, "endColumn": 12}, {"ruleId": "674", "severity": 1, "message": "682", "line": 18, "column": 3, "nodeType": null, "messageId": "676", "endLine": 18, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "683", "line": 19, "column": 3, "nodeType": null, "messageId": "676", "endLine": 19, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "684", "line": 20, "column": 3, "nodeType": null, "messageId": "676", "endLine": 20, "endColumn": 16}, {"ruleId": "674", "severity": 1, "message": "685", "line": 21, "column": 3, "nodeType": null, "messageId": "676", "endLine": 21, "endColumn": 8}, {"ruleId": "686", "severity": 2, "message": "687", "line": 77, "column": 19, "nodeType": "688", "messageId": "689", "endLine": 77, "endColumn": 22, "suggestions": "690"}, {"ruleId": "691", "severity": 1, "message": "692", "line": 86, "column": 6, "nodeType": "693", "endLine": 86, "endColumn": 49, "suggestions": "694"}, {"ruleId": "674", "severity": 1, "message": "695", "line": 6, "column": 10, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 15}, {"ruleId": "674", "severity": 1, "message": "696", "line": 7, "column": 10, "nodeType": null, "messageId": "676", "endLine": 7, "endColumn": 15}, {"ruleId": "674", "severity": 1, "message": "697", "line": 15, "column": 3, "nodeType": null, "messageId": "676", "endLine": 15, "endColumn": 8}, {"ruleId": "674", "severity": 1, "message": "685", "line": 16, "column": 3, "nodeType": null, "messageId": "676", "endLine": 16, "endColumn": 8}, {"ruleId": "674", "severity": 1, "message": "698", "line": 43, "column": 10, "nodeType": null, "messageId": "676", "endLine": 43, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "699", "line": 44, "column": 10, "nodeType": null, "messageId": "676", "endLine": 44, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "695", "line": 6, "column": 10, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 15}, {"ruleId": "674", "severity": 1, "message": "675", "line": 10, "column": 3, "nodeType": null, "messageId": "676", "endLine": 10, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "679", "line": 12, "column": 3, "nodeType": null, "messageId": "676", "endLine": 12, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "677", "line": 13, "column": 3, "nodeType": null, "messageId": "676", "endLine": 13, "endColumn": 8}, {"ruleId": "674", "severity": 1, "message": "700", "line": 6, "column": 29, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 44}, {"ruleId": "674", "severity": 1, "message": "675", "line": 14, "column": 3, "nodeType": null, "messageId": "676", "endLine": 14, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "682", "line": 16, "column": 3, "nodeType": null, "messageId": "676", "endLine": 16, "endColumn": 11}, {"ruleId": "701", "severity": 2, "message": "702", "line": 113, "column": 19, "nodeType": "703", "messageId": "704", "suggestions": "705"}, {"ruleId": "674", "severity": 1, "message": "695", "line": 6, "column": 10, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 15}, {"ruleId": "674", "severity": 1, "message": "675", "line": 10, "column": 3, "nodeType": null, "messageId": "676", "endLine": 10, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "682", "line": 11, "column": 3, "nodeType": null, "messageId": "676", "endLine": 11, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "677", "line": 13, "column": 3, "nodeType": null, "messageId": "676", "endLine": 13, "endColumn": 8}, {"ruleId": "674", "severity": 1, "message": "706", "line": 163, "column": 14, "nodeType": null, "messageId": "676", "endLine": 163, "endColumn": 19}, {"ruleId": "674", "severity": 1, "message": "706", "line": 184, "column": 14, "nodeType": null, "messageId": "676", "endLine": 184, "endColumn": 19}, {"ruleId": "674", "severity": 1, "message": "706", "line": 205, "column": 14, "nodeType": null, "messageId": "676", "endLine": 205, "endColumn": 19}, {"ruleId": "674", "severity": 1, "message": "706", "line": 226, "column": 14, "nodeType": null, "messageId": "676", "endLine": 226, "endColumn": 19}, {"ruleId": "686", "severity": 2, "message": "687", "line": 459, "column": 115, "nodeType": "688", "messageId": "689", "endLine": 459, "endColumn": 118, "suggestions": "707"}, {"ruleId": "674", "severity": 1, "message": "677", "line": 7, "column": 10, "nodeType": null, "messageId": "676", "endLine": 7, "endColumn": 15}, {"ruleId": "674", "severity": 1, "message": "708", "line": 7, "column": 17, "nodeType": null, "messageId": "676", "endLine": 7, "endColumn": 23}, {"ruleId": "686", "severity": 2, "message": "687", "line": 16, "column": 43, "nodeType": "688", "messageId": "689", "endLine": 16, "endColumn": 46, "suggestions": "709"}, {"ruleId": "674", "severity": 1, "message": "710", "line": 1, "column": 10, "nodeType": null, "messageId": "676", "endLine": 1, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "711", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 18}, {"ruleId": "674", "severity": 1, "message": "712", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "713", "line": 5, "column": 10, "nodeType": null, "messageId": "676", "endLine": 5, "endColumn": 23}, {"ruleId": "674", "severity": 1, "message": "710", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "711", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 18}, {"ruleId": "674", "severity": 1, "message": "712", "line": 4, "column": 10, "nodeType": null, "messageId": "676", "endLine": 4, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "713", "line": 6, "column": 10, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 23}, {"ruleId": "674", "severity": 1, "message": "714", "line": 12, "column": 24, "nodeType": null, "messageId": "676", "endLine": 12, "endColumn": 32}, {"ruleId": "686", "severity": 2, "message": "687", "line": 42, "column": 16, "nodeType": "688", "messageId": "689", "endLine": 42, "endColumn": 19, "suggestions": "715"}, {"ruleId": "674", "severity": 1, "message": "710", "line": 1, "column": 10, "nodeType": null, "messageId": "676", "endLine": 1, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "711", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 18}, {"ruleId": "674", "severity": 1, "message": "712", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "713", "line": 5, "column": 10, "nodeType": null, "messageId": "676", "endLine": 5, "endColumn": 23}, {"ruleId": "674", "severity": 1, "message": "677", "line": 9, "column": 10, "nodeType": null, "messageId": "676", "endLine": 9, "endColumn": 15}, {"ruleId": "674", "severity": 1, "message": "716", "line": 9, "column": 17, "nodeType": null, "messageId": "676", "endLine": 9, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "710", "line": 1, "column": 10, "nodeType": null, "messageId": "676", "endLine": 1, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "711", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 18}, {"ruleId": "674", "severity": 1, "message": "712", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "713", "line": 5, "column": 10, "nodeType": null, "messageId": "676", "endLine": 5, "endColumn": 23}, {"ruleId": "674", "severity": 1, "message": "700", "line": 8, "column": 29, "nodeType": null, "messageId": "676", "endLine": 8, "endColumn": 44}, {"ruleId": "674", "severity": 1, "message": "717", "line": 13, "column": 3, "nodeType": null, "messageId": "676", "endLine": 13, "endColumn": 7}, {"ruleId": "674", "severity": 1, "message": "675", "line": 15, "column": 3, "nodeType": null, "messageId": "676", "endLine": 15, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "718", "line": 16, "column": 3, "nodeType": null, "messageId": "676", "endLine": 16, "endColumn": 9}, {"ruleId": "686", "severity": 2, "message": "687", "line": 57, "column": 19, "nodeType": "688", "messageId": "689", "endLine": 57, "endColumn": 22, "suggestions": "719"}, {"ruleId": "691", "severity": 1, "message": "720", "line": 66, "column": 6, "nodeType": "693", "endLine": 66, "endColumn": 35, "suggestions": "721"}, {"ruleId": "674", "severity": 1, "message": "710", "line": 1, "column": 10, "nodeType": null, "messageId": "676", "endLine": 1, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "711", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 18}, {"ruleId": "674", "severity": 1, "message": "712", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "713", "line": 4, "column": 10, "nodeType": null, "messageId": "676", "endLine": 4, "endColumn": 23}, {"ruleId": "674", "severity": 1, "message": "677", "line": 12, "column": 3, "nodeType": null, "messageId": "676", "endLine": 12, "endColumn": 8}, {"ruleId": "674", "severity": 1, "message": "678", "line": 13, "column": 3, "nodeType": null, "messageId": "676", "endLine": 13, "endColumn": 16}, {"ruleId": "674", "severity": 1, "message": "679", "line": 14, "column": 3, "nodeType": null, "messageId": "676", "endLine": 14, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "680", "line": 15, "column": 3, "nodeType": null, "messageId": "676", "endLine": 15, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "675", "line": 16, "column": 3, "nodeType": null, "messageId": "676", "endLine": 16, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "681", "line": 17, "column": 3, "nodeType": null, "messageId": "676", "endLine": 17, "endColumn": 12}, {"ruleId": "674", "severity": 1, "message": "682", "line": 18, "column": 3, "nodeType": null, "messageId": "676", "endLine": 18, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "683", "line": 19, "column": 3, "nodeType": null, "messageId": "676", "endLine": 19, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "684", "line": 20, "column": 3, "nodeType": null, "messageId": "676", "endLine": 20, "endColumn": 16}, {"ruleId": "674", "severity": 1, "message": "685", "line": 21, "column": 3, "nodeType": null, "messageId": "676", "endLine": 21, "endColumn": 8}, {"ruleId": "686", "severity": 2, "message": "687", "line": 86, "column": 19, "nodeType": "688", "messageId": "689", "endLine": 86, "endColumn": 22, "suggestions": "722"}, {"ruleId": "691", "severity": 1, "message": "723", "line": 95, "column": 6, "nodeType": "693", "endLine": 95, "endColumn": 49, "suggestions": "724"}, {"ruleId": "674", "severity": 1, "message": "725", "line": 54, "column": 16, "nodeType": null, "messageId": "676", "endLine": 54, "endColumn": 19}, {"ruleId": "686", "severity": 2, "message": "687", "line": 74, "column": 21, "nodeType": "688", "messageId": "689", "endLine": 74, "endColumn": 24, "suggestions": "726"}, {"ruleId": "674", "severity": 1, "message": "725", "line": 57, "column": 16, "nodeType": null, "messageId": "676", "endLine": 57, "endColumn": 19}, {"ruleId": "686", "severity": 2, "message": "687", "line": 77, "column": 21, "nodeType": "688", "messageId": "689", "endLine": 77, "endColumn": 24, "suggestions": "727"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 56, "column": 21, "nodeType": "688", "messageId": "689", "endLine": 56, "endColumn": 24, "suggestions": "728"}, {"ruleId": "674", "severity": 1, "message": "675", "line": 10, "column": 3, "nodeType": null, "messageId": "676", "endLine": 10, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "729", "line": 45, "column": 17, "nodeType": null, "messageId": "676", "endLine": 45, "endColumn": 24}, {"ruleId": "691", "severity": 1, "message": "730", "line": 56, "column": 6, "nodeType": "693", "endLine": 56, "endColumn": 37, "suggestions": "731"}, {"ruleId": "674", "severity": 1, "message": "700", "line": 5, "column": 29, "nodeType": null, "messageId": "676", "endLine": 5, "endColumn": 44}, {"ruleId": "674", "severity": 1, "message": "732", "line": 10, "column": 3, "nodeType": null, "messageId": "676", "endLine": 10, "endColumn": 7}, {"ruleId": "674", "severity": 1, "message": "678", "line": 11, "column": 3, "nodeType": null, "messageId": "676", "endLine": 11, "endColumn": 16}, {"ruleId": "674", "severity": 1, "message": "733", "line": 12, "column": 3, "nodeType": null, "messageId": "676", "endLine": 12, "endColumn": 9}, {"ruleId": "674", "severity": 1, "message": "677", "line": 19, "column": 3, "nodeType": null, "messageId": "676", "endLine": 19, "endColumn": 8}, {"ruleId": "701", "severity": 2, "message": "702", "line": 120, "column": 17, "nodeType": "703", "messageId": "704", "suggestions": "734"}, {"ruleId": "674", "severity": 1, "message": "675", "line": 10, "column": 3, "nodeType": null, "messageId": "676", "endLine": 10, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "729", "line": 35, "column": 17, "nodeType": null, "messageId": "676", "endLine": 35, "endColumn": 24}, {"ruleId": "686", "severity": 2, "message": "687", "line": 63, "column": 19, "nodeType": "688", "messageId": "689", "endLine": 63, "endColumn": 22, "suggestions": "735"}, {"ruleId": "691", "severity": 1, "message": "736", "line": 72, "column": 6, "nodeType": "693", "endLine": 72, "endColumn": 52, "suggestions": "737"}, {"ruleId": "674", "severity": 1, "message": "675", "line": 10, "column": 3, "nodeType": null, "messageId": "676", "endLine": 10, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "738", "line": 14, "column": 3, "nodeType": null, "messageId": "676", "endLine": 14, "endColumn": 7}, {"ruleId": "674", "severity": 1, "message": "729", "line": 47, "column": 17, "nodeType": null, "messageId": "676", "endLine": 47, "endColumn": 24}, {"ruleId": "691", "severity": 1, "message": "739", "line": 55, "column": 6, "nodeType": "693", "endLine": 55, "endColumn": 37, "suggestions": "740"}, {"ruleId": "674", "severity": 1, "message": "700", "line": 6, "column": 29, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 44}, {"ruleId": "674", "severity": 1, "message": "675", "line": 12, "column": 3, "nodeType": null, "messageId": "676", "endLine": 12, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "685", "line": 13, "column": 3, "nodeType": null, "messageId": "676", "endLine": 13, "endColumn": 8}, {"ruleId": "674", "severity": 1, "message": "677", "line": 15, "column": 3, "nodeType": null, "messageId": "676", "endLine": 15, "endColumn": 8}, {"ruleId": "674", "severity": 1, "message": "741", "line": 67, "column": 9, "nodeType": null, "messageId": "676", "endLine": 67, "endColumn": 15}, {"ruleId": "674", "severity": 1, "message": "729", "line": 68, "column": 17, "nodeType": null, "messageId": "676", "endLine": 68, "endColumn": 24}, {"ruleId": "691", "severity": 1, "message": "742", "line": 77, "column": 6, "nodeType": "693", "endLine": 77, "endColumn": 14, "suggestions": "743"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 113, "column": 56, "nodeType": "688", "messageId": "689", "endLine": 113, "endColumn": 59, "suggestions": "744"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 114, "column": 54, "nodeType": "688", "messageId": "689", "endLine": 114, "endColumn": 57, "suggestions": "745"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 121, "column": 81, "nodeType": "688", "messageId": "689", "endLine": 121, "endColumn": 84, "suggestions": "746"}, {"ruleId": "674", "severity": 1, "message": "700", "line": 6, "column": 29, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 44}, {"ruleId": "674", "severity": 1, "message": "675", "line": 12, "column": 3, "nodeType": null, "messageId": "676", "endLine": 12, "endColumn": 11}, {"ruleId": "674", "severity": 1, "message": "729", "line": 72, "column": 17, "nodeType": null, "messageId": "676", "endLine": 72, "endColumn": 24}, {"ruleId": "691", "severity": 1, "message": "742", "line": 80, "column": 6, "nodeType": "693", "endLine": 80, "endColumn": 14, "suggestions": "747"}, {"ruleId": "748", "severity": 2, "message": "749", "line": 195, "column": 16, "nodeType": "750", "messageId": "751", "endLine": 195, "endColumn": 27}, {"ruleId": "674", "severity": 1, "message": "732", "line": 15, "column": 3, "nodeType": null, "messageId": "676", "endLine": 15, "endColumn": 7}, {"ruleId": "701", "severity": 2, "message": "702", "line": 111, "column": 17, "nodeType": "703", "messageId": "704", "suggestions": "752"}, {"ruleId": "701", "severity": 2, "message": "702", "line": 266, "column": 73, "nodeType": "703", "messageId": "704", "suggestions": "753"}, {"ruleId": "674", "severity": 1, "message": "710", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "712", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 21}, {"ruleId": "686", "severity": 2, "message": "687", "line": 19, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 19, "endColumn": 21, "suggestions": "754"}, {"ruleId": "674", "severity": 1, "message": "710", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "712", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "713", "line": 5, "column": 10, "nodeType": null, "messageId": "676", "endLine": 5, "endColumn": 23}, {"ruleId": "674", "severity": 1, "message": "755", "line": 30, "column": 11, "nodeType": null, "messageId": "676", "endLine": 30, "endColumn": 19}, {"ruleId": "674", "severity": 1, "message": "710", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "712", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "713", "line": 5, "column": 10, "nodeType": null, "messageId": "676", "endLine": 5, "endColumn": 23}, {"ruleId": "674", "severity": 1, "message": "756", "line": 6, "column": 27, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 34}, {"ruleId": "686", "severity": 2, "message": "687", "line": 19, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 19, "endColumn": 21, "suggestions": "757"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 19, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 19, "endColumn": 21, "suggestions": "758"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 32, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 32, "endColumn": 21, "suggestions": "759"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 26, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 26, "endColumn": 21, "suggestions": "760"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 189, "column": 24, "nodeType": "688", "messageId": "689", "endLine": 189, "endColumn": 27, "suggestions": "761"}, {"ruleId": "674", "severity": 1, "message": "710", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "712", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "713", "line": 6, "column": 10, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 23}, {"ruleId": "674", "severity": 1, "message": "710", "line": 2, "column": 10, "nodeType": null, "messageId": "676", "endLine": 2, "endColumn": 26}, {"ruleId": "674", "severity": 1, "message": "712", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 21}, {"ruleId": "674", "severity": 1, "message": "713", "line": 6, "column": 10, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 23}, {"ruleId": "686", "severity": 2, "message": "687", "line": 155, "column": 27, "nodeType": "688", "messageId": "689", "endLine": 155, "endColumn": 30, "suggestions": "762"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 162, "column": 30, "nodeType": "688", "messageId": "689", "endLine": 162, "endColumn": 33, "suggestions": "763"}, {"ruleId": "674", "severity": 1, "message": "764", "line": 172, "column": 11, "nodeType": null, "messageId": "676", "endLine": 172, "endColumn": 22}, {"ruleId": "674", "severity": 1, "message": "713", "line": 5, "column": 10, "nodeType": null, "messageId": "676", "endLine": 5, "endColumn": 23}, {"ruleId": "686", "severity": 2, "message": "687", "line": 32, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 32, "endColumn": 21, "suggestions": "765"}, {"ruleId": "674", "severity": 1, "message": "755", "line": 38, "column": 11, "nodeType": null, "messageId": "676", "endLine": 38, "endColumn": 19}, {"ruleId": "674", "severity": 1, "message": "713", "line": 5, "column": 10, "nodeType": null, "messageId": "676", "endLine": 5, "endColumn": 23}, {"ruleId": "686", "severity": 2, "message": "687", "line": 24, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 24, "endColumn": 21, "suggestions": "766"}, {"ruleId": "674", "severity": 1, "message": "756", "line": 6, "column": 27, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 34}, {"ruleId": "686", "severity": 2, "message": "687", "line": 29, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 29, "endColumn": 21, "suggestions": "767"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 28, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 28, "endColumn": 21, "suggestions": "768"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 36, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 36, "endColumn": 21, "suggestions": "769"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 257, "column": 38, "nodeType": "688", "messageId": "689", "endLine": 257, "endColumn": 41, "suggestions": "770"}, {"ruleId": "674", "severity": 1, "message": "756", "line": 6, "column": 27, "nodeType": null, "messageId": "676", "endLine": 6, "endColumn": 34}, {"ruleId": "686", "severity": 2, "message": "687", "line": 29, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 29, "endColumn": 21, "suggestions": "771"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 36, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 36, "endColumn": 21, "suggestions": "772"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 16, "column": 40, "nodeType": "688", "messageId": "689", "endLine": 16, "endColumn": 43, "suggestions": "773"}, {"ruleId": "701", "severity": 2, "message": "702", "line": 22, "column": 20, "nodeType": "703", "messageId": "704", "suggestions": "774"}, {"ruleId": "701", "severity": 2, "message": "702", "line": 27, "column": 25, "nodeType": "703", "messageId": "704", "suggestions": "775"}, {"ruleId": "701", "severity": 2, "message": "702", "line": 27, "column": 99, "nodeType": "703", "messageId": "704", "suggestions": "776"}, {"ruleId": "674", "severity": 1, "message": "777", "line": 10, "column": 10, "nodeType": null, "messageId": "676", "endLine": 10, "endColumn": 15}, {"ruleId": "686", "severity": 2, "message": "687", "line": 114, "column": 19, "nodeType": "688", "messageId": "689", "endLine": 114, "endColumn": 22, "suggestions": "778"}, {"ruleId": "674", "severity": 1, "message": "779", "line": 7, "column": 10, "nodeType": null, "messageId": "676", "endLine": 7, "endColumn": 19}, {"ruleId": "686", "severity": 2, "message": "687", "line": 103, "column": 19, "nodeType": "688", "messageId": "689", "endLine": 103, "endColumn": 22, "suggestions": "780"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 153, "column": 75, "nodeType": "688", "messageId": "689", "endLine": 153, "endColumn": 78, "suggestions": "781"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 12, "column": 15, "nodeType": "688", "messageId": "689", "endLine": 12, "endColumn": 18, "suggestions": "782"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 127, "column": 19, "nodeType": "688", "messageId": "689", "endLine": 127, "endColumn": 22, "suggestions": "783"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 86, "column": 21, "nodeType": "688", "messageId": "689", "endLine": 86, "endColumn": 24, "suggestions": "784"}, {"ruleId": "674", "severity": 1, "message": "785", "line": 8, "column": 29, "nodeType": null, "messageId": "676", "endLine": 8, "endColumn": 51}, {"ruleId": "674", "severity": 1, "message": "786", "line": 141, "column": 17, "nodeType": null, "messageId": "676", "endLine": 141, "endColumn": 18}, {"ruleId": "674", "severity": 1, "message": "787", "line": 9, "column": 3, "nodeType": null, "messageId": "676", "endLine": 9, "endColumn": 6}, {"ruleId": "674", "severity": 1, "message": "788", "line": 3, "column": 20, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 29}, {"ruleId": "674", "severity": 1, "message": "777", "line": 10, "column": 10, "nodeType": null, "messageId": "676", "endLine": 10, "endColumn": 15}, {"ruleId": "686", "severity": 2, "message": "687", "line": 13, "column": 13, "nodeType": "688", "messageId": "689", "endLine": 13, "endColumn": 16, "suggestions": "789"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 22, "column": 50, "nodeType": "688", "messageId": "689", "endLine": 22, "endColumn": 53, "suggestions": "790"}, {"ruleId": "686", "severity": 2, "message": "687", "line": 104, "column": 19, "nodeType": "688", "messageId": "689", "endLine": 104, "endColumn": 22, "suggestions": "791"}, {"ruleId": "674", "severity": 1, "message": "788", "line": 3, "column": 20, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 29}, {"ruleId": "674", "severity": 1, "message": "792", "line": 9, "column": 10, "nodeType": null, "messageId": "676", "endLine": 9, "endColumn": 15}, {"ruleId": "674", "severity": 1, "message": "793", "line": 9, "column": 17, "nodeType": null, "messageId": "676", "endLine": 9, "endColumn": 33}, {"ruleId": "686", "severity": 2, "message": "687", "line": 96, "column": 21, "nodeType": "688", "messageId": "689", "endLine": 96, "endColumn": 24, "suggestions": "794"}, {"ruleId": "674", "severity": 1, "message": "795", "line": 103, "column": 9, "nodeType": null, "messageId": "676", "endLine": 103, "endColumn": 19}, {"ruleId": "796", "severity": 2, "message": "797", "line": 4, "column": 18, "nodeType": "798", "messageId": "799", "endLine": 4, "endColumn": 28, "suggestions": "800"}, {"ruleId": "674", "severity": 1, "message": "788", "line": 3, "column": 10, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 19}, {"ruleId": "674", "severity": 1, "message": "801", "line": 3, "column": 21, "nodeType": null, "messageId": "676", "endLine": 3, "endColumn": 29}, {"ruleId": "674", "severity": 1, "message": "802", "line": 14, "column": 11, "nodeType": null, "messageId": "676", "endLine": 14, "endColumn": 16}, {"ruleId": "686", "severity": 2, "message": "687", "line": 101, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 101, "endColumn": 13, "suggestions": "803"}, {"ruleId": "674", "severity": 1, "message": "804", "line": 47, "column": 48, "nodeType": null, "messageId": "676", "endLine": 47, "endColumn": 54}, {"ruleId": "674", "severity": 1, "message": "804", "line": 73, "column": 3, "nodeType": null, "messageId": "676", "endLine": 73, "endColumn": 9}, {"ruleId": "674", "severity": 1, "message": "805", "line": 120, "column": 28, "nodeType": null, "messageId": "676", "endLine": 120, "endColumn": 33}, {"ruleId": "674", "severity": 1, "message": "806", "line": 70, "column": 67, "nodeType": null, "messageId": "676", "endLine": 70, "endColumn": 76}, {"ruleId": "674", "severity": 1, "message": "807", "line": 70, "column": 87, "nodeType": null, "messageId": "676", "endLine": 70, "endColumn": 101}, {"ruleId": "674", "severity": 1, "message": "806", "line": 83, "column": 65, "nodeType": null, "messageId": "676", "endLine": 83, "endColumn": 74}, {"ruleId": "674", "severity": 1, "message": "808", "line": 83, "column": 85, "nodeType": null, "messageId": "676", "endLine": 83, "endColumn": 92}, {"ruleId": "674", "severity": 1, "message": "809", "line": 1, "column": 8, "nodeType": null, "messageId": "676", "endLine": 1, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'Calendar' is defined but never used.", "unusedVar", "'Users' is defined but never used.", "'GraduationCap' is defined but never used.", "'BookOpen' is defined but never used.", "'FileText' is defined but never used.", "'BarChart3' is defined but never used.", "'Settings' is defined but never used.", "'UserPlus' is defined but never used.", "'ClipboardList' is defined but never used.", "'Award' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["810", "811"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchClasses'. Either include it or remove the dependency array.", "ArrayExpression", ["812"], "'Input' is defined but never used.", "'Label' is defined but never used.", "'Clock' is defined but never used.", "'showAddTerm' is assigned a value but never used.", "'showAddExam' is assigned a value but never used.", "'CardDescription' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["813", "814", "815", "816"], "'error' is defined but never used.", ["817", "818"], "'Upload' is defined but never used.", ["819", "820"], "'getServerSession' is defined but never used.", "'redirect' is defined but never used.", "'authOptions' is defined but never used.", "'hasPermission' is defined but never used.", "'Download' is defined but never used.", ["821", "822"], "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'MapPin' is defined but never used.", ["823", "824"], "React Hook useEffect has a missing dependency: 'fetchSubjects'. Either include it or remove the dependency array.", ["825"], ["826", "827"], "React Hook useEffect has a missing dependency: 'fetchTeachers'. Either include it or remove the dependency array.", ["828"], "'err' is defined but never used.", ["829", "830"], ["831", "832"], ["833", "834"], "'session' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMarks'. Either include it or remove the dependency array.", ["835"], "'Bell' is defined but never used.", "'Target' is defined but never used.", ["836", "837", "838", "839"], ["840", "841"], "React Hook useEffect has a missing dependency: 'fetchAttendance'. Either include it or remove the dependency array.", ["842"], "'Plus' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["843"], "'router' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExamData'. Either include it or remove the dependency array.", ["844"], ["845", "846"], ["847", "848"], ["849", "850"], ["851"], "react/jsx-no-undef", "'CheckCircle' is not defined.", "JSXIdentifier", "undefined", ["852", "853", "854", "855"], ["856", "857", "858", "859"], ["860", "861"], "'isActive' is assigned a value but never used.", "'request' is defined but never used.", ["862", "863"], ["864", "865"], ["866", "867"], ["868", "869"], ["870", "871"], ["872", "873"], ["874", "875"], "'updatedUser' is assigned a value but never used.", ["876", "877"], ["878", "879"], ["880", "881"], ["882", "883"], ["884", "885"], ["886", "887"], ["888", "889"], ["890", "891"], ["892", "893"], ["894", "895", "896", "897"], ["898", "899", "900", "901"], ["902", "903", "904", "905"], "'Badge' is defined but never used.", ["906", "907"], "'checkAuth' is defined but never used.", ["908", "909"], ["910", "911"], ["912", "913"], ["914", "915"], ["916", "917"], "'formatValidationErrors' is defined but never used.", "'_' is defined but never used.", "'Eye' is defined but never used.", "'useEffect' is defined but never used.", ["918", "919"], ["920", "921"], ["922", "923"], "'Alert' is defined but never used.", "'AlertDescription' is defined but never used.", ["924", "925"], "'formatDate' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["926"], "'useState' is defined but never used.", "'theme' is assigned a value but never used.", ["927", "928"], "'config' is assigned a value but never used.", "'index' is defined but never used.", "'teacherId' is defined but never used.", "'studentClassId' is defined but never used.", "'classId' is defined but never used.", "'NextAuth' is defined but never used.", {"messageId": "929", "fix": "930", "desc": "931"}, {"messageId": "932", "fix": "933", "desc": "934"}, {"desc": "935", "fix": "936"}, {"messageId": "937", "data": "938", "fix": "939", "desc": "940"}, {"messageId": "937", "data": "941", "fix": "942", "desc": "943"}, {"messageId": "937", "data": "944", "fix": "945", "desc": "946"}, {"messageId": "937", "data": "947", "fix": "948", "desc": "949"}, {"messageId": "929", "fix": "950", "desc": "931"}, {"messageId": "932", "fix": "951", "desc": "934"}, {"messageId": "929", "fix": "952", "desc": "931"}, {"messageId": "932", "fix": "953", "desc": "934"}, {"messageId": "929", "fix": "954", "desc": "931"}, {"messageId": "932", "fix": "955", "desc": "934"}, {"messageId": "929", "fix": "956", "desc": "931"}, {"messageId": "932", "fix": "957", "desc": "934"}, {"desc": "958", "fix": "959"}, {"messageId": "929", "fix": "960", "desc": "931"}, {"messageId": "932", "fix": "961", "desc": "934"}, {"desc": "962", "fix": "963"}, {"messageId": "929", "fix": "964", "desc": "931"}, {"messageId": "932", "fix": "965", "desc": "934"}, {"messageId": "929", "fix": "966", "desc": "931"}, {"messageId": "932", "fix": "967", "desc": "934"}, {"messageId": "929", "fix": "968", "desc": "931"}, {"messageId": "932", "fix": "969", "desc": "934"}, {"desc": "970", "fix": "971"}, {"messageId": "937", "data": "972", "fix": "973", "desc": "940"}, {"messageId": "937", "data": "974", "fix": "975", "desc": "943"}, {"messageId": "937", "data": "976", "fix": "977", "desc": "946"}, {"messageId": "937", "data": "978", "fix": "979", "desc": "949"}, {"messageId": "929", "fix": "980", "desc": "931"}, {"messageId": "932", "fix": "981", "desc": "934"}, {"desc": "982", "fix": "983"}, {"desc": "984", "fix": "985"}, {"desc": "986", "fix": "987"}, {"messageId": "929", "fix": "988", "desc": "931"}, {"messageId": "932", "fix": "989", "desc": "934"}, {"messageId": "929", "fix": "990", "desc": "931"}, {"messageId": "932", "fix": "991", "desc": "934"}, {"messageId": "929", "fix": "992", "desc": "931"}, {"messageId": "932", "fix": "993", "desc": "934"}, {"desc": "986", "fix": "994"}, {"messageId": "937", "data": "995", "fix": "996", "desc": "940"}, {"messageId": "937", "data": "997", "fix": "998", "desc": "943"}, {"messageId": "937", "data": "999", "fix": "1000", "desc": "946"}, {"messageId": "937", "data": "1001", "fix": "1002", "desc": "949"}, {"messageId": "937", "data": "1003", "fix": "1004", "desc": "940"}, {"messageId": "937", "data": "1005", "fix": "1006", "desc": "943"}, {"messageId": "937", "data": "1007", "fix": "1008", "desc": "946"}, {"messageId": "937", "data": "1009", "fix": "1010", "desc": "949"}, {"messageId": "929", "fix": "1011", "desc": "931"}, {"messageId": "932", "fix": "1012", "desc": "934"}, {"messageId": "929", "fix": "1013", "desc": "931"}, {"messageId": "932", "fix": "1014", "desc": "934"}, {"messageId": "929", "fix": "1015", "desc": "931"}, {"messageId": "932", "fix": "1016", "desc": "934"}, {"messageId": "929", "fix": "1017", "desc": "931"}, {"messageId": "932", "fix": "1018", "desc": "934"}, {"messageId": "929", "fix": "1019", "desc": "931"}, {"messageId": "932", "fix": "1020", "desc": "934"}, {"messageId": "929", "fix": "1021", "desc": "931"}, {"messageId": "932", "fix": "1022", "desc": "934"}, {"messageId": "929", "fix": "1023", "desc": "931"}, {"messageId": "932", "fix": "1024", "desc": "934"}, {"messageId": "929", "fix": "1025", "desc": "931"}, {"messageId": "932", "fix": "1026", "desc": "934"}, {"messageId": "929", "fix": "1027", "desc": "931"}, {"messageId": "932", "fix": "1028", "desc": "934"}, {"messageId": "929", "fix": "1029", "desc": "931"}, {"messageId": "932", "fix": "1030", "desc": "934"}, {"messageId": "929", "fix": "1031", "desc": "931"}, {"messageId": "932", "fix": "1032", "desc": "934"}, {"messageId": "929", "fix": "1033", "desc": "931"}, {"messageId": "932", "fix": "1034", "desc": "934"}, {"messageId": "929", "fix": "1035", "desc": "931"}, {"messageId": "932", "fix": "1036", "desc": "934"}, {"messageId": "929", "fix": "1037", "desc": "931"}, {"messageId": "932", "fix": "1038", "desc": "934"}, {"messageId": "929", "fix": "1039", "desc": "931"}, {"messageId": "932", "fix": "1040", "desc": "934"}, {"messageId": "929", "fix": "1041", "desc": "931"}, {"messageId": "932", "fix": "1042", "desc": "934"}, {"messageId": "929", "fix": "1043", "desc": "931"}, {"messageId": "932", "fix": "1044", "desc": "934"}, {"messageId": "937", "data": "1045", "fix": "1046", "desc": "940"}, {"messageId": "937", "data": "1047", "fix": "1048", "desc": "943"}, {"messageId": "937", "data": "1049", "fix": "1050", "desc": "946"}, {"messageId": "937", "data": "1051", "fix": "1052", "desc": "949"}, {"messageId": "937", "data": "1053", "fix": "1054", "desc": "940"}, {"messageId": "937", "data": "1055", "fix": "1056", "desc": "943"}, {"messageId": "937", "data": "1057", "fix": "1058", "desc": "946"}, {"messageId": "937", "data": "1059", "fix": "1060", "desc": "949"}, {"messageId": "937", "data": "1061", "fix": "1062", "desc": "940"}, {"messageId": "937", "data": "1063", "fix": "1064", "desc": "943"}, {"messageId": "937", "data": "1065", "fix": "1066", "desc": "946"}, {"messageId": "937", "data": "1067", "fix": "1068", "desc": "949"}, {"messageId": "929", "fix": "1069", "desc": "931"}, {"messageId": "932", "fix": "1070", "desc": "934"}, {"messageId": "929", "fix": "1071", "desc": "931"}, {"messageId": "932", "fix": "1072", "desc": "934"}, {"messageId": "929", "fix": "1073", "desc": "931"}, {"messageId": "932", "fix": "1074", "desc": "934"}, {"messageId": "929", "fix": "1075", "desc": "931"}, {"messageId": "932", "fix": "1076", "desc": "934"}, {"messageId": "929", "fix": "1077", "desc": "931"}, {"messageId": "932", "fix": "1078", "desc": "934"}, {"messageId": "929", "fix": "1079", "desc": "931"}, {"messageId": "932", "fix": "1080", "desc": "934"}, {"messageId": "929", "fix": "1081", "desc": "931"}, {"messageId": "932", "fix": "1082", "desc": "934"}, {"messageId": "929", "fix": "1083", "desc": "931"}, {"messageId": "932", "fix": "1084", "desc": "934"}, {"messageId": "929", "fix": "1085", "desc": "931"}, {"messageId": "932", "fix": "1086", "desc": "934"}, {"messageId": "929", "fix": "1087", "desc": "931"}, {"messageId": "932", "fix": "1088", "desc": "934"}, {"messageId": "1089", "fix": "1090", "desc": "1091"}, {"messageId": "929", "fix": "1092", "desc": "931"}, {"messageId": "932", "fix": "1093", "desc": "934"}, "suggestUnknown", {"range": "1094", "text": "1095"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1096", "text": "1097"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [pagination.page, searchTerm, filterActive, fetchClasses]", {"range": "1098", "text": "1099"}, "replaceWithAlt", {"alt": "1100"}, {"range": "1101", "text": "1102"}, "Replace with `&apos;`.", {"alt": "1103"}, {"range": "1104", "text": "1105"}, "Replace with `&lsquo;`.", {"alt": "1106"}, {"range": "1107", "text": "1108"}, "Replace with `&#39;`.", {"alt": "1109"}, {"range": "1110", "text": "1111"}, "Replace with `&rsquo;`.", {"range": "1112", "text": "1095"}, {"range": "1113", "text": "1097"}, {"range": "1114", "text": "1095"}, {"range": "1115", "text": "1097"}, {"range": "1116", "text": "1095"}, {"range": "1117", "text": "1097"}, {"range": "1118", "text": "1095"}, {"range": "1119", "text": "1097"}, "Update the dependencies array to be: [fetchSubjects, pagination.page, searchTerm]", {"range": "1120", "text": "1121"}, {"range": "1122", "text": "1095"}, {"range": "1123", "text": "1097"}, "Update the dependencies array to be: [pagination.page, searchTerm, filterActive, fetchTeachers]", {"range": "1124", "text": "1125"}, {"range": "1126", "text": "1095"}, {"range": "1127", "text": "1097"}, {"range": "1128", "text": "1095"}, {"range": "1129", "text": "1097"}, {"range": "1130", "text": "1095"}, {"range": "1131", "text": "1097"}, "Update the dependencies array to be: [selectedTerm, selectedSubject, fetchMarks]", {"range": "1132", "text": "1133"}, {"alt": "1100"}, {"range": "1134", "text": "1135"}, {"alt": "1103"}, {"range": "1136", "text": "1137"}, {"alt": "1106"}, {"range": "1138", "text": "1139"}, {"alt": "1109"}, {"range": "1140", "text": "1141"}, {"range": "1142", "text": "1095"}, {"range": "1143", "text": "1097"}, "Update the dependencies array to be: [fetchAttendance, pagination.page, selectedClass, selectedDate]", {"range": "1144", "text": "1145"}, "Update the dependencies array to be: [selectedTerm, selectedSubject, fetchExams]", {"range": "1146", "text": "1147"}, "Update the dependencies array to be: [examId, fetchExamData]", {"range": "1148", "text": "1149"}, {"range": "1150", "text": "1095"}, {"range": "1151", "text": "1097"}, {"range": "1152", "text": "1095"}, {"range": "1153", "text": "1097"}, {"range": "1154", "text": "1095"}, {"range": "1155", "text": "1097"}, {"range": "1156", "text": "1149"}, {"alt": "1100"}, {"range": "1157", "text": "1158"}, {"alt": "1103"}, {"range": "1159", "text": "1160"}, {"alt": "1106"}, {"range": "1161", "text": "1162"}, {"alt": "1109"}, {"range": "1163", "text": "1164"}, {"alt": "1100"}, {"range": "1165", "text": "1166"}, {"alt": "1103"}, {"range": "1167", "text": "1168"}, {"alt": "1106"}, {"range": "1169", "text": "1170"}, {"alt": "1109"}, {"range": "1171", "text": "1172"}, {"range": "1173", "text": "1095"}, {"range": "1174", "text": "1097"}, {"range": "1175", "text": "1095"}, {"range": "1176", "text": "1097"}, {"range": "1177", "text": "1095"}, {"range": "1178", "text": "1097"}, {"range": "1179", "text": "1095"}, {"range": "1180", "text": "1097"}, {"range": "1181", "text": "1095"}, {"range": "1182", "text": "1097"}, {"range": "1183", "text": "1095"}, {"range": "1184", "text": "1097"}, {"range": "1185", "text": "1095"}, {"range": "1186", "text": "1097"}, {"range": "1187", "text": "1095"}, {"range": "1188", "text": "1097"}, {"range": "1189", "text": "1095"}, {"range": "1190", "text": "1097"}, {"range": "1191", "text": "1095"}, {"range": "1192", "text": "1097"}, {"range": "1193", "text": "1095"}, {"range": "1194", "text": "1097"}, {"range": "1195", "text": "1095"}, {"range": "1196", "text": "1097"}, {"range": "1197", "text": "1095"}, {"range": "1198", "text": "1097"}, {"range": "1199", "text": "1095"}, {"range": "1200", "text": "1097"}, {"range": "1201", "text": "1095"}, {"range": "1202", "text": "1097"}, {"range": "1203", "text": "1095"}, {"range": "1204", "text": "1097"}, {"range": "1205", "text": "1095"}, {"range": "1206", "text": "1097"}, {"alt": "1100"}, {"range": "1207", "text": "1208"}, {"alt": "1103"}, {"range": "1209", "text": "1210"}, {"alt": "1106"}, {"range": "1211", "text": "1212"}, {"alt": "1109"}, {"range": "1213", "text": "1214"}, {"alt": "1100"}, {"range": "1215", "text": "1216"}, {"alt": "1103"}, {"range": "1217", "text": "1218"}, {"alt": "1106"}, {"range": "1219", "text": "1220"}, {"alt": "1109"}, {"range": "1221", "text": "1222"}, {"alt": "1100"}, {"range": "1223", "text": "1224"}, {"alt": "1103"}, {"range": "1225", "text": "1226"}, {"alt": "1106"}, {"range": "1227", "text": "1228"}, {"alt": "1109"}, {"range": "1229", "text": "1230"}, {"range": "1231", "text": "1095"}, {"range": "1232", "text": "1097"}, {"range": "1233", "text": "1095"}, {"range": "1234", "text": "1097"}, {"range": "1235", "text": "1095"}, {"range": "1236", "text": "1097"}, {"range": "1237", "text": "1095"}, {"range": "1238", "text": "1097"}, {"range": "1239", "text": "1095"}, {"range": "1240", "text": "1097"}, {"range": "1241", "text": "1095"}, {"range": "1242", "text": "1097"}, {"range": "1243", "text": "1095"}, {"range": "1244", "text": "1097"}, {"range": "1245", "text": "1095"}, {"range": "1246", "text": "1097"}, {"range": "1247", "text": "1095"}, {"range": "1248", "text": "1097"}, {"range": "1249", "text": "1095"}, {"range": "1250", "text": "1097"}, "replaceEmptyInterfaceWithSuper", {"range": "1251", "text": "1252"}, "Replace empty interface with a type alias.", {"range": "1253", "text": "1095"}, {"range": "1254", "text": "1097"}, [2001, 2004], "unknown", [2001, 2004], "never", [2135, 2178], "[pagination.page, searchTerm, filterActive, fetchClasses]", "&apos;", [3042, 3122], "\n              Here&apos;s an overview of your school management system.\n            ", "&lsquo;", [3042, 3122], "\n              Here&lsquo;s an overview of your school management system.\n            ", "&#39;", [3042, 3122], "\n              Here&#39;s an overview of your school management system.\n            ", "&rsquo;", [3042, 3122], "\n              Here&rsquo;s an overview of your school management system.\n            ", [17422, 17425], [17422, 17425], [572, 575], [572, 575], [1346, 1349], [1346, 1349], [1635, 1638], [1635, 1638], [1770, 1799], "[fetchSubjects, pagination.page, searchTerm]", [2181, 2184], [2181, 2184], [2316, 2359], "[pagination.page, searchTerm, filterActive, fetchTeachers]", [1915, 1918], [1915, 1918], [2028, 2031], [2028, 2031], [1420, 1423], [1420, 1423], [1366, 1397], "[selectedTerm, selectedSubject, fetchMarks]", [3136, 3213], "\r\n            Here&apos;s your academic overview and progress summary.\r\n          ", [3136, 3213], "\r\n            Here&lsquo;s your academic overview and progress summary.\r\n          ", [3136, 3213], "\r\n            Here&#39;s your academic overview and progress summary.\r\n          ", [3136, 3213], "\r\n            Here&rsquo;s your academic overview and progress summary.\r\n          ", [1913, 1916], [1913, 1916], [2059, 2105], "[fetchAttendance, pagination.page, selectedClass, selectedDate]", [1137, 1168], "[selectedTerm, selectedSubject, fetchExams]", [1610, 1618], "[examId, fetchExamData]", [2596, 2599], [2596, 2599], [2675, 2678], [2675, 2678], [3030, 3033], [3030, 3033], [1618, 1626], [2935, 3019], "\r\n            Here&apos;s your teaching overview and quick actions for today.\r\n          ", [2935, 3019], "\r\n            Here&lsquo;s your teaching overview and quick actions for today.\r\n          ", [2935, 3019], "\r\n            Here&#39;s your teaching overview and quick actions for today.\r\n          ", [2935, 3019], "\r\n            Here&rsquo;s your teaching overview and quick actions for today.\r\n          ", [9961, 9977], "Today&apos;s Schedule", [9961, 9977], "Today&lsquo;s Schedule", [9961, 9977], "Today&#39;s Schedule", [9961, 9977], "Today&rsquo;s Schedule", [653, 656], [653, 656], [663, 666], [663, 666], [657, 660], [657, 660], [1174, 1177], [1174, 1177], [934, 937], [934, 937], [5486, 5489], [5486, 5489], [4765, 4768], [4765, 4768], [5169, 5172], [5169, 5172], [1172, 1175], [1172, 1175], [931, 934], [931, 934], [900, 903], [900, 903], [847, 850], [847, 850], [1380, 1383], [1380, 1383], [7663, 7666], [7663, 7666], [872, 875], [872, 875], [1053, 1056], [1053, 1056], [569, 572], [569, 572], [836, 907], "\r\n            You don&apos;t have permission to access this page\r\n          ", [836, 907], "\r\n            You don&lsquo;t have permission to access this page\r\n          ", [836, 907], "\r\n            You don&#39;t have permission to access this page\r\n          ", [836, 907], "\r\n            You don&rsquo;t have permission to access this page\r\n          ", [1034, 1154], "\r\n            The page you&apos;re trying to access requires specific permissions that your account doesn't have.\r\n          ", [1034, 1154], "\r\n            The page you&lsquo;re trying to access requires specific permissions that your account doesn't have.\r\n          ", [1034, 1154], "\r\n            The page you&#39;re trying to access requires specific permissions that your account doesn't have.\r\n          ", [1034, 1154], "\r\n            The page you&rsquo;re trying to access requires specific permissions that your account doesn't have.\r\n          ", [1034, 1154], "\r\n            The page you're trying to access requires specific permissions that your account doesn&apos;t have.\r\n          ", [1034, 1154], "\r\n            The page you're trying to access requires specific permissions that your account doesn&lsquo;t have.\r\n          ", [1034, 1154], "\r\n            The page you're trying to access requires specific permissions that your account doesn&#39;t have.\r\n          ", [1034, 1154], "\r\n            The page you're trying to access requires specific permissions that your account doesn&rsquo;t have.\r\n          ", [3342, 3345], [3342, 3345], [2649, 2652], [2649, 2652], [3844, 3847], [3844, 3847], [447, 450], [447, 450], [3509, 3512], [3509, 3512], [2126, 2129], [2126, 2129], [495, 498], [495, 498], [830, 833], [830, 833], [3217, 3220], [3217, 3220], [2298, 2301], [2298, 2301], [75, 153], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [2431, 2434], [2431, 2434]]