{"version": 3, "sources": ["turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOM\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "vendored", "ReactJsxRuntime", "React", "AppRouterContext", "HooksClientContext", "ServerInsertedHtml", "ReactDOM", "ReactServerDOMTurbopackClient"], "mappings": "2kCA0BQG,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,iCC1BjCF,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEC,eAAe,+BCFxCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEE,KAAK,+BCF9BR,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,QAAW,CAACG,gBAAgB,+BCFvCT,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,QAAW,CAACI,kBAAkB,8BCFzCV,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,QAAW,CAACK,kBAAkB,+BCFzCX,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEM,QAAQ,+BCFjCZ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEO,6BAA6B", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}