{"version": 3, "sources": ["turbopack:///[project]/school-management-system/src/app/(dash)/admin/settings/page.tsx", "turbopack:///[project]/school-management-system/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/refresh-cw.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/database.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/mail.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/shield.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport DashboardLayout from '@/components/layout/dashboard-layout'\nimport { \n  Calendar, \n  Settings, \n  School, \n  Users, \n  Bell,\n  Shield,\n  Database,\n  Mail,\n  Save,\n  RefreshCw,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react'\n\ninterface SchoolInfo {\n  name: string\n  address: string\n  phone: string\n  email: string\n  website: string\n  principal: string\n  establishedYear: string\n}\n\ninterface AcademicSettings {\n  academicYear: string\n  currentTerm: string\n  gradingSystem: 'LETTER' | 'PERCENTAGE' | 'NUMERIC'\n  passPercentage: number\n  maxAttendancePercentage: number\n}\n\ninterface NotificationSettings {\n  attendanceAlerts: boolean\n  examResults: boolean\n  reportCardGeneration: boolean\n  systemUpdates: boolean\n}\n\ninterface SecuritySettings {\n  sessionTimeout: number\n  passwordPolicy: string\n  twoFactorAuth: boolean\n  loginAttempts: boolean\n}\n\nimport { adminNavigation } from '@/lib/navigation';\n\nexport default function SettingsPage() {\n  const [activeTab, setActiveTab] = useState<'general' | 'academic' | 'notifications' | 'security'>('general')\n  const [loading, setLoading] = useState(false)\n  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)\n  \n  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo>({\n    name: 'Advance School',\n    address: '123 Education Street, City, State 12345',\n    phone: '+****************',\n    email: '<EMAIL>',\n    website: 'www.advanceschool.edu',\n    principal: 'Dr. John Smith',\n    establishedYear: '1995'\n  })\n  const [academicSettings, setAcademicSettings] = useState<AcademicSettings>({\n    academicYear: '2024-2025',\n    currentTerm: 'Term 1',\n    gradingSystem: 'LETTER',\n    passPercentage: 40,\n    maxAttendancePercentage: 75\n  })\n  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({\n    attendanceAlerts: true,\n    examResults: true,\n    reportCardGeneration: false,\n    systemUpdates: true\n  })\n  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({\n    sessionTimeout: 30,\n    passwordPolicy: 'strong',\n    twoFactorAuth: false,\n    loginAttempts: true\n  })\n\n  // Load settings from API\n  useEffect(() => {\n    const loadSettings = async () => {\n      try {\n        const response = await fetch('/api/admin/settings')\n        if (response.ok) {\n          const data = await response.json()\n          if (data.general) {\n            setSchoolInfo({\n              name: data.general.schoolName,\n              address: data.general.address,\n              phone: data.general.phone,\n              email: data.general.email,\n              website: data.general.website,\n              principal: data.general.principal,\n              establishedYear: data.general.establishedYear\n            })\n          }\n          if (data.academic) {\n            setAcademicSettings({\n              academicYear: data.academic.academicYear,\n              currentTerm: data.academic.currentTerm,\n              gradingSystem: data.academic.gradingSystem,\n              passPercentage: data.academic.passPercentage,\n              maxAttendancePercentage: data.academic.maxAttendancePercentage\n            })\n          }\n          if (data.notifications) {\n            setNotificationSettings({\n              attendanceAlerts: data.notifications.attendanceAlerts,\n              examResults: data.notifications.examResults,\n              reportCardGeneration: data.notifications.reportCardGeneration,\n              systemUpdates: data.notifications.systemUpdates\n            })\n          }\n          if (data.security) {\n            setSecuritySettings({\n              sessionTimeout: data.security.sessionTimeout,\n              passwordPolicy: data.security.passwordPolicy,\n              twoFactorAuth: data.security.twoFactorAuth,\n              loginAttempts: data.security.loginAttempts\n            })\n          }\n        }\n      } catch (error) {\n        console.error('Error loading settings:', error)\n      }\n    }\n\n    loadSettings()\n  }, [])\n\n  const showMessage = (type: 'success' | 'error', text: string) => {\n    setMessage({ type, text })\n    setTimeout(() => setMessage(null), 3000)\n  }\n\n  const handleSaveSchoolInfo = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/admin/settings', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ type: 'general', data: schoolInfo })\n      })\n      \n      if (response.ok) {\n        showMessage('success', 'School information saved successfully!')\n      } else {\n        showMessage('error', 'Failed to save school information')\n      }\n    } catch (error) {\n      showMessage('error', 'Error saving school information')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveAcademicSettings = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/admin/settings', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ type: 'academic', data: academicSettings })\n      })\n      \n      if (response.ok) {\n        showMessage('success', 'Academic settings saved successfully!')\n      } else {\n        showMessage('error', 'Failed to save academic settings')\n      }\n    } catch (error) {\n      showMessage('error', 'Error saving academic settings')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveNotificationSettings = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/admin/settings', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ type: 'notifications', data: notificationSettings })\n      })\n      \n      if (response.ok) {\n        showMessage('success', 'Notification settings saved successfully!')\n      } else {\n        showMessage('error', 'Failed to save notification settings')\n      }\n    } catch (error) {\n      showMessage('error', 'Error saving notification settings')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveSecuritySettings = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/admin/settings', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ type: 'security', data: securitySettings })\n      })\n      \n      if (response.ok) {\n        showMessage('success', 'Security settings saved successfully!')\n      } else {\n        showMessage('error', 'Failed to save security settings')\n      }\n    } catch (error) {\n      showMessage('error', 'Error saving security settings')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <DashboardLayout title=\"System Settings\" navigation={adminNavigation}>\n      <div className=\"space-y-6\">\n        {/* Message Display */}\n        {message && (\n          <div className={`p-4 rounded-md flex items-center ${\n            message.type === 'success' \n              ? 'bg-green-50 border border-green-200 text-green-800' \n              : 'bg-red-50 border border-red-200 text-red-800'\n          }`}>\n            {message.type === 'success' ? (\n              <CheckCircle className=\"w-5 h-5 mr-2\" />\n            ) : (\n              <AlertCircle className=\"w-5 h-5 mr-2\" />\n            )}\n            {message.text}\n          </div>\n        )}\n\n        {/* Header */}\n        <div className=\"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0\">\n          <div>\n            <h1 className=\"text-xl sm:text-2xl font-bold text-gray-900\">System Settings</h1>\n            <p className=\"text-sm sm:text-base text-gray-600\">Manage school configuration and preferences</p>\n          </div>\n          <div className=\"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2\">\n            <Button variant=\"outline\" disabled={loading} className=\"w-full sm:w-auto\">\n              <RefreshCw className=\"w-4 h-4 mr-2\" />\n              <span className=\"sm:hidden\">Reset</span>\n              <span className=\"hidden sm:inline\">Reset to Default</span>\n            </Button>\n            <Button disabled={loading} className=\"w-full sm:w-auto\">\n              <Save className=\"w-4 h-4 mr-2\" />\n              <span className=\"sm:hidden\">Save All</span>\n              <span className=\"hidden sm:inline\">Save All Changes</span>\n            </Button>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex flex-wrap gap-2 sm:gap-8\">\n            <button\n              onClick={() => setActiveTab('general')}\n              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${\n                activeTab === 'general'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <School className=\"inline w-4 h-4 mr-1 sm:mr-2\" />\n              <span className=\"hidden sm:inline\">General Settings</span>\n              <span className=\"sm:hidden\">General</span>\n            </button>\n            <button\n              onClick={() => setActiveTab('academic')}\n              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${\n                activeTab === 'academic'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <Calendar className=\"inline w-4 h-4 mr-1 sm:mr-2\" />\n              <span className=\"hidden sm:inline\">Academic Settings</span>\n              <span className=\"sm:hidden\">Academic</span>\n            </button>\n            <button\n              onClick={() => setActiveTab('notifications')}\n              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${\n                activeTab === 'notifications'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <Bell className=\"inline w-4 h-4 mr-1 sm:mr-2\" />\n              <span className=\"hidden sm:inline\">Notifications</span>\n              <span className=\"sm:hidden\">Alerts</span>\n            </button>\n            <button\n              onClick={() => setActiveTab('security')}\n              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${\n                activeTab === 'security'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <Shield className=\"inline w-4 h-4 mr-1 sm:mr-2\" />\n              <span className=\"hidden sm:inline\">Security</span>\n              <span className=\"sm:hidden\">Security</span>\n            </button>\n          </nav>\n        </div>\n\n        {/* General Settings Tab */}\n        {activeTab === 'general' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <School className=\"w-5 h-5 mr-2\" />\n                  School Information\n                </CardTitle>\n                <CardDescription>\n                  Update basic school information and contact details\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <Label htmlFor=\"schoolName\">School Name</Label>\n                    <Input\n                      id=\"schoolName\"\n                      value={schoolInfo.name}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, name: e.target.value})}\n                      placeholder=\"Enter school name\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"principal\">Principal Name</Label>\n                    <Input\n                      id=\"principal\"\n                      value={schoolInfo.principal}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, principal: e.target.value})}\n                      placeholder=\"Enter principal name\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"phone\">Phone Number</Label>\n                    <Input\n                      id=\"phone\"\n                      value={schoolInfo.phone}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, phone: e.target.value})}\n                      placeholder=\"Enter phone number\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"email\">Email Address</Label>\n                    <Input\n                      id=\"email\"\n                      type=\"email\"\n                      value={schoolInfo.email}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, email: e.target.value})}\n                      placeholder=\"Enter email address\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"website\">Website</Label>\n                    <Input\n                      id=\"website\"\n                      value={schoolInfo.website}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, website: e.target.value})}\n                      placeholder=\"Enter website URL\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"establishedYear\">Established Year</Label>\n                    <Input\n                      id=\"establishedYear\"\n                      value={schoolInfo.establishedYear}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, establishedYear: e.target.value})}\n                      placeholder=\"Enter established year\"\n                    />\n                  </div>\n                  <div className=\"md:col-span-2\">\n                    <Label htmlFor=\"address\">Address</Label>\n                    <Input\n                      id=\"address\"\n                      value={schoolInfo.address}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, address: e.target.value})}\n                      placeholder=\"Enter complete address\"\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-6\">\n                  <Button onClick={handleSaveSchoolInfo} disabled={loading}>\n                    <Save className=\"w-4 h-4 mr-2\" />\n                    {loading ? 'Saving...' : 'Save School Information'}\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Academic Settings Tab */}\n        {activeTab === 'academic' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Calendar className=\"w-5 h-5 mr-2\" />\n                  Academic Configuration\n                </CardTitle>\n                <CardDescription>\n                  Configure academic year, grading system, and performance criteria\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <Label htmlFor=\"academicYear\">Academic Year</Label>\n                    <Input\n                      id=\"academicYear\"\n                      value={academicSettings.academicYear}\n                      onChange={(e) => setAcademicSettings({...academicSettings, academicYear: e.target.value})}\n                      placeholder=\"e.g., 2024-2025\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"currentTerm\">Current Term</Label>\n                    <select\n                      id=\"currentTerm\"\n                      value={academicSettings.currentTerm}\n                      onChange={(e) => setAcademicSettings({...academicSettings, currentTerm: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"Term 1\">Term 1</option>\n                      <option value=\"Term 2\">Term 2</option>\n                      <option value=\"Term 3\">Term 3</option>\n                    </select>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"gradingSystem\">Grading System</Label>\n                    <select\n                      id=\"gradingSystem\"\n                      value={academicSettings.gradingSystem}\n                      onChange={(e) => setAcademicSettings({...academicSettings, gradingSystem: e.target.value as any})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"LETTER\">Letter Grades (A+, A, B+, B, etc.)</option>\n                      <option value=\"PERCENTAGE\">Percentage</option>\n                      <option value=\"NUMERIC\">Numeric (1-10)</option>\n                    </select>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"passPercentage\">Pass Percentage</Label>\n                    <Input\n                      id=\"passPercentage\"\n                      type=\"number\"\n                      value={academicSettings.passPercentage}\n                      onChange={(e) => setAcademicSettings({...academicSettings, passPercentage: parseInt(e.target.value)})}\n                      placeholder=\"40\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"maxAttendancePercentage\">Minimum Attendance %</Label>\n                    <Input\n                      id=\"maxAttendancePercentage\"\n                      type=\"number\"\n                      value={academicSettings.maxAttendancePercentage}\n                      onChange={(e) => setAcademicSettings({...academicSettings, maxAttendancePercentage: parseInt(e.target.value)})}\n                      placeholder=\"75\"\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-6\">\n                  <Button onClick={handleSaveAcademicSettings} disabled={loading}>\n                    <Save className=\"w-4 h-4 mr-2\" />\n                    {loading ? 'Saving...' : 'Save Academic Settings'}\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Notifications Tab */}\n        {activeTab === 'notifications' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Bell className=\"w-5 h-5 mr-2\" />\n                  Notification Settings\n                </CardTitle>\n                <CardDescription>\n                  Configure email notifications and alerts\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Attendance Alerts</h4>\n                      <p className=\"text-sm text-gray-600\">Send notifications for low attendance</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={notificationSettings.attendanceAlerts}\n                      onChange={(e) => setNotificationSettings({\n                        ...notificationSettings,\n                        attendanceAlerts: e.target.checked\n                      })}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Exam Results</h4>\n                      <p className=\"text-sm text-gray-600\">Notify when exam results are published</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={notificationSettings.examResults}\n                      onChange={(e) => setNotificationSettings({\n                        ...notificationSettings,\n                        examResults: e.target.checked\n                      })}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Report Card Generation</h4>\n                      <p className=\"text-sm text-gray-600\">Notify when report cards are ready</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={notificationSettings.reportCardGeneration}\n                      onChange={(e) => setNotificationSettings({\n                        ...notificationSettings,\n                        reportCardGeneration: e.target.checked\n                      })}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">System Updates</h4>\n                      <p className=\"text-sm text-gray-600\">Receive system maintenance notifications</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={notificationSettings.systemUpdates}\n                      onChange={(e) => setNotificationSettings({\n                        ...notificationSettings,\n                        systemUpdates: e.target.checked\n                      })}\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-6\">\n                  <Button onClick={handleSaveNotificationSettings} disabled={loading}>\n                    <Save className=\"w-4 h-4 mr-2\" />\n                    {loading ? 'Saving...' : 'Save Notification Settings'}\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Security Tab */}\n        {activeTab === 'security' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Shield className=\"w-5 h-5 mr-2\" />\n                  Security Settings\n                </CardTitle>\n                <CardDescription>\n                  Manage password policies and security settings\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"sessionTimeout\">Session Timeout (minutes)</Label>\n                    <Input\n                      id=\"sessionTimeout\"\n                      type=\"number\"\n                      value={securitySettings.sessionTimeout}\n                      onChange={(e) => setSecuritySettings({\n                        ...securitySettings,\n                        sessionTimeout: parseInt(e.target.value)\n                      })}\n                      placeholder=\"30\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"passwordPolicy\">Password Policy</Label>\n                    <select\n                      id=\"passwordPolicy\"\n                      value={securitySettings.passwordPolicy}\n                      onChange={(e) => setSecuritySettings({\n                        ...securitySettings,\n                        passwordPolicy: e.target.value\n                      })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"strong\">Strong (8+ chars, mixed case, numbers)</option>\n                      <option value=\"medium\">Medium (6+ chars, mixed case)</option>\n                      <option value=\"weak\">Weak (4+ chars)</option>\n                    </select>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Two-Factor Authentication</h4>\n                      <p className=\"text-sm text-gray-600\">Require 2FA for admin accounts</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={securitySettings.twoFactorAuth}\n                      onChange={(e) => setSecuritySettings({\n                        ...securitySettings,\n                        twoFactorAuth: e.target.checked\n                      })}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Login Attempts</h4>\n                      <p className=\"text-sm text-gray-600\">Lock account after failed attempts</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={securitySettings.loginAttempts}\n                      onChange={(e) => setSecuritySettings({\n                        ...securitySettings,\n                        loginAttempts: e.target.checked\n                      })}\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-6\">\n                  <Button onClick={handleSaveSecuritySettings} disabled={loading}>\n                    <Save className=\"w-4 h-4 mr-2\" />\n                    {loading ? 'Saving...' : 'Save Security Settings'}\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Database className=\"w-5 h-5 mr-2\" />\n                  System Maintenance\n                </CardTitle>\n                <CardDescription>\n                  Database backup and system maintenance options\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <Button variant=\"outline\" className=\"w-full\">\n                    <Database className=\"w-4 h-4 mr-2\" />\n                    Create Database Backup\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full\">\n                    <RefreshCw className=\"w-4 h-4 mr-2\" />\n                    Clear Cache\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full\">\n                    <Mail className=\"w-4 h-4 mr-2\" />\n                    Test Email Configuration\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n      </div>\n    </DashboardLayout>\n  )\n}\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('refresh-cw', __iconNode);\n\nexport default RefreshCw;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['ellipse', { cx: '12', cy: '5', rx: '9', ry: '3', key: 'msslwz' }],\n  ['path', { d: 'M3 5V19A9 3 0 0 0 21 19V5', key: '1wlel7' }],\n  ['path', { d: 'M3 12A9 3 0 0 0 21 12', key: 'mv7ke4' }],\n];\n\n/**\n * @component @name Database\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8ZWxsaXBzZSBjeD0iMTIiIGN5PSI1IiByeD0iOSIgcnk9IjMiIC8+CiAgPHBhdGggZD0iTTMgNVYxOUE5IDMgMCAwIDAgMjEgMTlWNSIgLz4KICA8cGF0aCBkPSJNMyAxMkE5IDMgMCAwIDAgMjEgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/database\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Database = createLucideIcon('database', __iconNode);\n\nexport default Database;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7', key: '132q7q' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgNy04Ljk5MSA1LjcyN2EyIDIgMCAwIDEtMi4wMDkgMEwyIDciIC8+CiAgPHJlY3QgeD0iMiIgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjE2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('mail', __iconNode);\n\nexport default Mail;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z',\n      key: 'oel41y',\n    },\n  ],\n];\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTNjMCA1LTMuNSA3LjUtNy42NiA4Ljk1YTEgMSAwIDAgMS0uNjctLjAxQzcuNSAyMC41IDQgMTggNCAxM1Y2YTEgMSAwIDAgMSAxLTFjMiAwIDQuNS0xLjIgNi4yNC0yLjcyYTEuMTcgMS4xNyAwIDAgMSAxLjUyIDBDMTQuNTEgMy44MSAxNyA1IDE5IDVhMSAxIDAgMCAxIDEgMXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('shield', __iconNode);\n\nexport default Shield;\n"], "names": [], "mappings": "qFAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,oBIgBA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,EAAS,CAAA,AAAT,CAAA,AAAS,CAAT,AAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,yKAhBzC,CAAA,CAAA,CAAA,AFYL,CEZK,AFYL,CEZK,AFYL,GEZK,CHaX,CGZI,CAEJ,EFSM,EAAW,CEZX,AFYW,CEZX,AFYW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,AAhB1C,CAAA,8RCC0C,CDAN,ACAM,CAAA,CAAI,IAAK,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAChF,EHEA,IAAA,EAAA,EAAA,CAAA,CAAA,OCaA,CGbW,AHaX,CGbW,AHaX,CGbW,AHaX,CAAM,EAAA,CAAA,EAAY,CAAA,CAAA,CAAA,CAAZ,AAAY,CAAZ,AAAY,CAAZ,AAAY,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAlBC,CAkBa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAjBtC,AAiBsC,CAjBtC,ACAR,ACAQ,AFiBsC,CAAU,CAAA,AAjB3C,CEAA,ADAA,ADAA,CAAA,ACAA,ACAA,CAAA,AFAA,kDAAsD,CEAN,AFAM,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,SAC1E,CAAA,AEAA,ADAA,ADAE,CEAA,CAAA,AFAG,CEAA,AFAA,ACAA,CCAA,ADAA,ADAA,CCAA,ADAA,CCAA,ADAA,SAAc,CAAA,AEAD,ADAb,CCAa,ADAb,ADAc,CEAD,ADAb,ADAc,CCAd,ACAa,AFAC,AAAK,YAChC,OAAQ,CCAA,ADAA,AAAE,EAAG,CAAA,ACAA,CAAA,ADAA,CCAA,ADAA,CCAA,ADAA,CCAA,ADAA,CCAA,ADAA,CCAA,ADAA,CCAA,ADAA,CCAA,ADAA,CAAA,ACAA,CDAA,ACAA,CAAA,ADAA,CCAA,ADAA,CAAA,ACAA,CAAA,ADAA,CAAA,ACAA,CAAA,ADAA,CAAA,ACAA,CDAA,ACAA,CAAA,ADAA,CCAA,ADAA,CCAA,ADAA,CCAA,ADAA,CCAA,ADAA,8BAAuD,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACpF,CAAC,CGDC,ADaJ,AFZG,CEYH,ACbI,AHCD,KAAQ,CGAF,AHAE,AAAE,AEYP,CCZG,ADYI,CCZJ,ADYI,AFZG,CGAP,ADYI,AFZG,CEYH,ACZJ,AHAO,CGAP,ADYI,AFZG,CAAA,AGAP,ADYI,CCZJ,AHAO,AEYH,CFZG,AGAP,ADYI,CAAA,ACZJ,AHAO,CAAA,AGAP,ADYI,CCZJ,AHAO,AEYH,CAAA,ACZJ,AHAO,CAAA,AGAP,ADYI,CFZG,AGAP,ADYI,IFZqB,CGAzB,AHAyB,AEYJ,CAAA,ACZrB,AHAyB,CAAA,AGAzB,QHCT,CGDS,AFaT,CAAA,AFZA,AIDS,CFaT,AEbS,CAAA,AFaT,EFZA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OA+CA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,GAAM,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAwD,WAC5F,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAqD,MAErF,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAa,CACvD,KAAM,iBACN,QAAS,0CACT,MAAO,oBACP,MAAO,yBACP,QAAS,wBACT,UAAW,iBACX,gBAAiB,MACnB,GACM,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAmB,CACzE,aAAc,YACd,YAAa,SACb,cAAe,SACf,eAAgB,GAChB,wBAAyB,EAC3B,GACM,CAAC,EAAsB,EAAwB,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAA+B,CACrF,kBAAkB,EAClB,YAAa,GACb,sBAAsB,EACtB,eAAe,CACjB,GACM,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAmB,CACzE,eAAgB,GAChB,eAAgB,SAChB,eAAe,EACf,eAAe,CACjB,GAGA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KAgDR,CA/CqB,UACnB,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,uBAC7B,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAC5B,EAAK,OAAO,EAAE,AAChB,EAAc,CACZ,KAAM,EAAK,OAAO,CAAC,UAAU,CAC7B,QAAS,EAAK,OAAO,CAAC,OAAO,CAC7B,MAAO,EAAK,OAAO,CAAC,KAAK,CACzB,MAAO,EAAK,OAAO,CAAC,KAAK,CACzB,QAAS,EAAK,OAAO,CAAC,OAAO,CAC7B,UAAW,EAAK,OAAO,CAAC,SAAS,CACjC,gBAAiB,EAAK,OAAO,CAAC,eAAe,AAC/C,GAEE,EAAK,QAAQ,EACf,AADiB,EACG,CAClB,aAAc,EAAK,QAAQ,CAAC,YAAY,CACxC,YAAa,EAAK,QAAQ,CAAC,WAAW,CACtC,cAAe,EAAK,QAAQ,CAAC,aAAa,CAC1C,eAAgB,EAAK,QAAQ,CAAC,cAAc,CAC5C,wBAAyB,EAAK,QAAQ,CAAC,uBACzC,AADgE,GAG9D,EAAK,aAAa,EAAE,AACtB,EAAwB,CACtB,iBAAkB,EAAK,aAAa,CAAC,gBAAgB,CACrD,YAAa,EAAK,aAAa,CAAC,WAAW,CAC3C,qBAAsB,EAAK,aAAa,CAAC,oBAAoB,CAC7D,cAAe,EAAK,aAAa,CAAC,aAAa,AACjD,GAEE,EAAK,QAAQ,EAAE,AACjB,EAAoB,CAClB,eAAgB,EAAK,QAAQ,CAAC,cAAc,CAC5C,eAAgB,EAAK,QAAQ,CAAC,cAAc,CAC5C,cAAe,EAAK,QAAQ,CAAC,aAAa,CAC1C,cAAe,EAAK,QAAQ,CAAC,aAAa,AAC5C,EAEJ,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,0BAA2B,EAC3C,EACF,GAGF,EAAG,EAAE,EAEL,IAAM,EAAc,CAAC,EAA2B,KAC9C,EAAW,MAAE,OAAM,CAAK,GACxB,WAAW,IAAM,EAAW,MAAO,IACrC,EAEM,EAAuB,UAC3B,GAAW,GACX,GAAI,CAOE,CANa,MAAM,MAAM,sBAAuB,CAClD,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,CAAE,KAAM,UAAW,KAAM,CAAW,EAC3D,EAAA,EAEa,EAAE,CACb,CADe,CACH,UAAW,0CAEvB,EAAY,QAAS,oCAEzB,CAAE,MAAO,EAAO,CACd,EAAY,QAAS,kCACvB,QAAU,CACR,GAAW,EACb,CACF,EAEM,EAA6B,UACjC,GAAW,GACX,GAAI,CAOE,CANa,MAAM,MAAM,sBAAuB,CAClD,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,CAAE,KAAM,WAAY,KAAM,CAAiB,EAClE,EAAA,EAEa,EAAE,CACb,CADe,CACH,UAAW,yCAEvB,EAAY,QAAS,mCAEzB,CAAE,MAAO,EAAO,CACd,EAAY,QAAS,iCACvB,QAAU,CACR,GAAW,EACb,CACF,EAEM,EAAiC,UACrC,GAAW,GACX,GAAI,CAOE,CANa,MAAM,MAAM,sBAAuB,CAClD,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,CAAE,KAAM,gBAAiB,KAAM,CAAqB,EAC3E,EAAA,EAEa,EAAE,CACb,CADe,CACH,UAAW,6CAEvB,EAAY,QAAS,uCAEzB,CAAE,MAAO,EAAO,CACd,EAAY,QAAS,qCACvB,QAAU,CACR,GAAW,EACb,CACF,EAEM,EAA6B,UACjC,GAAW,GACX,GAAI,CAOE,CANa,MAAM,MAAM,sBAAuB,CAClD,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,CAAE,KAAM,WAAY,KAAM,CAAiB,EAClE,EAAA,EAEa,EAAE,CACb,CADe,CACH,UAAW,yCAEvB,EAAY,QAAS,mCAEzB,CAAE,MAAO,EAAO,CACd,EAAY,QAAS,iCACvB,QAAU,CACR,GAAW,EACb,CACF,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAe,CAAA,CAAC,MAAM,kBAAkB,WAAY,EAAA,eAAe,UAClE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEZ,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAW,CAAC,iCAAiC,EAC/B,YAAjB,EAAQ,IAAI,CACR,qDACA,+CAAA,CACJ,WACkB,YAAjB,EAAQ,IAAI,CACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,iBAEvB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,iBAExB,EAAQ,IAAI,IAKjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uDAA8C,oBAC5D,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8CAAqC,mDAEpD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0EACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,SAAU,EAAS,UAAU,6BACrD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAU,UAAU,iBACrB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,UAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,wBAErC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,SAAU,EAAS,UAAU,6BACnC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,aAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,8BAMzC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAa,WAC5B,UAAW,CAAC,mFAAmF,EAC/E,YAAd,EACI,gCACA,6EAAA,CACJ,WAEF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,gCAClB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,qBACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,eAE9B,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAa,YAC5B,UAAW,CAAC,mFAAmF,EAC/E,aAAd,EACI,gCACA,6EAAA,CACJ,WAEF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,gCACpB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,sBACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,gBAE9B,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAa,iBAC5B,UAAW,CAAC,mFAAmF,EAC/E,kBAAd,EACI,gCACA,6EAAA,CACJ,WAEF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,gCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,kBACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,cAE9B,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAa,YAC5B,UAAW,CAAC,mFAAmF,EAC7F,AAAc,eACV,gCACA,6EAAA,CACJ,WAEF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAO,UAAU,gCAClB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,aACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,qBAMnB,YAAd,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,8BACnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,iBAAiB,wBAGrC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,2DAInB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,WAAW,CAAA,WACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,sBAAa,gBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,aACH,MAAO,EAAW,IAAI,CACtB,SAAU,AAAC,GAAM,EAAc,CAAC,GAAG,CAAU,CAAE,KAAM,EAAE,MAAM,CAAC,KAAK,GACnE,YAAY,yBAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,qBAAY,mBAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,YACH,MAAO,EAAW,SAAS,CAC3B,SAAU,AAAC,GAAM,EAAc,CAAC,GAAG,CAAU,CAAE,UAAW,EAAE,MAAM,CAAC,KAAK,GACxE,YAAY,4BAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,iBAAQ,iBACvB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,QACH,MAAO,EAAW,KAAK,CACvB,SAAU,AAAC,GAAM,EAAc,CAAC,GAAG,CAAU,CAAE,MAAO,EAAE,MAAM,CAAC,KAAK,GACpE,YAAY,0BAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,iBAAQ,kBACvB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,QACH,KAAK,QACL,MAAO,EAAW,KAAK,CACvB,SAAU,AAAC,GAAM,EAAc,CAAC,GAAG,CAAU,CAAE,MAAO,EAAE,MAAM,CAAC,KAAK,GACpE,YAAY,2BAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,mBAAU,YACzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,UACH,MAAO,EAAW,OAAO,CACzB,SAAU,AAAC,GAAM,EAAc,CAAC,GAAG,CAAU,CAAE,QAAS,EAAE,MAAM,CAAC,KAAK,GACtE,YAAY,yBAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,2BAAkB,qBACjC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,kBACH,MAAO,EAAW,eAAe,CACjC,SAAW,AAAD,GAAO,EAAc,CAAC,GAAG,CAAU,CAAE,gBAAiB,EAAE,MAAM,CAAC,KAAK,GAC9E,YAAY,8BAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,mBAAU,YACzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,UACH,MAAO,EAAW,OAAO,CACzB,SAAU,AAAC,GAAM,EAAc,CAAC,GAAG,CAAU,CAAE,QAAS,EAAE,MAAM,CAAC,KAAK,GACtE,YAAY,iCAIlB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAS,EAAsB,SAAU,YAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBACf,EAAU,YAAc,uCAStB,aAAd,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,8BACnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBAAiB,4BAGvC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,yEAInB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,WAAW,CAAA,WACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,wBAAe,kBAC9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,eACH,MAAO,EAAiB,YAAY,CACpC,SAAU,AAAC,GAAM,EAAoB,CAAC,GAAG,CAAgB,CAAE,aAAc,EAAE,MAAM,CAAC,KAAK,GACvF,YAAY,uBAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,uBAAc,iBAC7B,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,GAAG,cACH,MAAO,EAAiB,WAAW,CACnC,SAAU,AAAC,GAAM,EAAoB,CAAC,GAAG,CAAgB,CAAE,YAAa,EAAE,MAAM,CAAC,KAAK,GACtF,UAAU,mHAEV,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,kBAAS,WACvB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,kBAAS,WACvB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,kBAAS,iBAG3B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,yBAAgB,mBAC/B,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,GAAG,gBACH,MAAO,EAAiB,aAAa,CACrC,SAAU,AAAC,GAAM,EAAoB,CAAC,GAAG,CAAgB,CAAE,cAAe,EAAE,MAAM,CAAC,KAAK,AAAO,GAC/F,UAAU,mHAEV,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,kBAAS,uCACvB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,sBAAa,eAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,mBAAU,yBAG5B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,0BAAiB,oBAChC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,iBACH,KAAK,SACL,MAAO,EAAiB,cAAc,CACtC,SAAU,AAAC,GAAM,EAAoB,CAAC,GAAG,CAAgB,CAAE,eAAgB,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,GACnG,YAAY,UAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,mCAA0B,yBACzC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,0BACH,KAAK,SACL,MAAO,EAAiB,uBAAuB,CAC/C,SAAU,AAAC,GAAM,EAAoB,CAAC,GAAG,CAAgB,CAAE,wBAAyB,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,GAC5G,YAAY,aAIlB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAS,EAA4B,SAAU,YACrD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBACf,EAAU,YAAc,sCAStB,kBAAd,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,8BACnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBAAiB,2BAGnC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,gDAInB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,WAAW,CAAA,WACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uBAAc,sBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,6CAEvC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,WACL,UAAU,UACV,QAAS,EAAqB,gBAAgB,CAC9C,SAAU,AAAC,GAAM,EAAwB,CACvC,GAAG,CAAoB,CACvB,iBAAkB,EAAE,MAAM,CAAC,OAAO,AACpC,QAGJ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uBAAc,iBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,8CAEvC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,WACL,UAAU,UACV,QAAS,EAAqB,WAAW,CACzC,SAAW,AAAD,GAAO,EAAwB,CACvC,GAAG,CAAoB,CACvB,YAAa,EAAE,MAAM,CAAC,OAAO,AAC/B,QAGJ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uBAAc,2BAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,0CAEvC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,WACL,UAAU,UACV,QAAS,EAAqB,oBAAoB,CAClD,SAAU,AAAC,GAAM,EAAwB,CACvC,GAAG,CAAoB,CACvB,qBAAsB,EAAE,MAAM,CAAC,OACjC,AADwC,QAI5C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uBAAc,mBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,gDAEvC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,WACL,UAAU,UACV,QAAS,EAAqB,aAAa,CAC3C,SAAU,AAAC,GAAM,EAAwB,CACvC,GAAG,CAAoB,CACvB,cAAe,EAAE,MAAM,CAAC,OAAO,AACjC,WAIN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAS,EAAgC,SAAU,YACzD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBACf,EAAU,YAAc,0CAStB,aAAd,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,8BACnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAO,UAAU,iBAAiB,uBAGrC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,sDAInB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,WAAW,CAAA,WACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,0BAAiB,8BAChC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACJ,GAAG,iBACH,KAAK,SACL,MAAO,EAAiB,cAAc,CACtC,SAAU,AAAC,GAAM,EAAoB,CACnC,GAAG,CAAgB,CACnB,eAAgB,SAAS,EAAE,MAAM,CAAC,KAAK,CACzC,GACA,YAAY,UAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,0BAAiB,oBAChC,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,GAAG,iBACH,MAAO,EAAiB,cAAc,CACtC,SAAW,AAAD,GAAO,EAAoB,CACnC,GAAG,CAAgB,CACnB,eAAgB,EAAE,MAAM,CAAC,KAAK,AAChC,GACA,UAAU,mHAEV,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,kBAAS,2CACvB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,kBAAS,kCACvB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,gBAAO,0BAGzB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uBAAc,8BAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,sCAEvC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,WACL,UAAU,UACV,QAAS,EAAiB,aAAa,CACvC,SAAU,AAAC,GAAM,EAAoB,CACnC,GAAG,CAAgB,CACnB,cAAe,EAAE,MAAM,CAAC,OAAO,AACjC,QAGJ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uBAAc,mBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,0CAEvC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,WACL,UAAU,UACV,QAAS,EAAiB,aAAa,CACvC,SAAU,AAAC,GAAM,EAAoB,CACnC,GAAG,CAAgB,CACnB,cAAe,EAAE,MAAM,CAAC,OAAO,AACjC,WAIN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAS,EAA4B,SAAU,YACrD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBACf,EAAU,YAAc,oCAMjC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,8BACnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAS,UAAU,iBAAiB,wBAGvC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,sDAInB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,UAAU,mBAClC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAS,UAAU,iBAAiB,4BAGvC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,UAAU,mBAClC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAU,UAAU,iBAAiB,iBAGxC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,UAAU,mBAClC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAK,UAAU,iBAAiB,6CAWrD", "ignoreList": [1, 2, 3, 4]}