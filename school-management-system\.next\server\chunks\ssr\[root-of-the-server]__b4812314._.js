module.exports=[56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},81453,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactDOM},82236,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactServerDOMTurbopackClient},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},36870,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(54159),c=a.i(81453),d=a.i(54472),e=a.i(41825),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},40770,a=>{"use strict";a.s(["Home",()=>b],40770);let b=(0,a.i(32639).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},32639,a=>{"use strict";a.s(["default",()=>g],32639);var b=a.i(54159);let c=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},d=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(0,b.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:f=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,b.createElement)("svg",{ref:l,...e,width:c,height:c,stroke:a,strokeWidth:g?24*Number(f)/Number(c):f,className:d("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,c])=>(0,b.createElement)(a,c)),...Array.isArray(i)?i:[i]])),g=(a,e)=>{let g=(0,b.forwardRef)(({className:g,...h},i)=>(0,b.createElement)(f,{ref:i,iconNode:e,className:d(`lucide-${c(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,g),...h}));return g.displayName=c(a),g}},59844,a=>{"use strict";a.s(["User",()=>b],59844);let b=(0,a.i(32639).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5492,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},75422,a=>{"use strict";a.s(["Alert",()=>g,"AlertDescription",()=>h]);var b=a.i(41825),c=a.i(54159),d=a.i(24311),e=a.i(18688);let f=(0,d.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),g=c.forwardRef(({className:a,variant:c,...d},g)=>(0,b.jsx)("div",{ref:g,role:"alert",className:(0,e.cn)(f({variant:c}),a),...d}));g.displayName="Alert",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("h5",{ref:d,className:(0,e.cn)("mb-1 font-medium leading-none tracking-tight",a),...c})).displayName="AlertTitle";let h=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:(0,e.cn)("text-sm [&_p]:leading-relaxed",a),...c}));h.displayName="AlertDescription"},43626,a=>{"use strict";a.s(["Loader2",()=>b],43626);let b=(0,a.i(32639).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},31787,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},19441,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(41825),e=a.r(31787),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},73211,a=>{"use strict";a.s(["Eye",()=>b],73211);let b=(0,a.i(32639).default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},74415,a=>{"use strict";a.s(["Trash2",()=>b],74415);let b=(0,a.i(32639).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},46221,a=>{"use strict";a.s(["StudentTable",()=>s],46221);var b=a.i(41825),c=a.i(54159),d=a.i(52963),e=a.i(2331),f=a.i(5492),g=a.i(4082),h=a.i(75422),i=a.i(15579),j=a.i(37906),k=a.i(57012),l=a.i(74415),m=a.i(73211),n=a.i(14401),o=a.i(62867);let p=(0,a.i(32639).default)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var q=a.i(42879),r=a.i(43626);function s({students:a,classes:s,pagination:t}){let u=(0,d.useRouter)(),[v,w]=(0,c.useState)(!1),[x,y]=(0,c.useState)(null),[z,A]=(0,c.useState)(""),[B,C]=(0,c.useState)(""),[D,E]=(0,c.useState)(""),F=()=>{let a=new URLSearchParams;z&&a.append("search",z),B&&a.append("classId",B),D&&a.append("gender",D),a.append("page","1"),u.push(`/admin/students?${a.toString()}`)},G=a=>{let b=new URLSearchParams;z&&b.append("search",z),B&&b.append("classId",B),D&&b.append("gender",D),b.append("page",a.toString()),u.push(`/admin/students?${b.toString()}`)},H=async(a,b)=>{if(confirm(`Are you sure you want to delete ${b}? This action cannot be undone.`)){w(!0),y(null);try{let b=await fetch(`/api/admin/students/${a}`,{method:"DELETE"});if(!b.ok){let a=await b.json();throw Error(a.error||"Failed to delete student")}u.refresh()}catch(a){y(a instanceof Error?a.message:"Failed to delete student")}finally{w(!1)}}},I=a=>new Date(a).toLocaleDateString(),J=a=>{switch(a){case"MALE":return"Male";case"FEMALE":return"Female";case"OTHER":return"Other";default:return a}};return(0,b.jsxs)(g.Card,{children:[(0,b.jsx)(g.CardHeader,{children:(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.CardTitle,{children:"Students"}),(0,b.jsx)(g.CardDescription,{children:"Manage student information and records"})]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsxs)(e.Button,{variant:"outline",onClick:()=>{let a=new URLSearchParams;B&&a.append("classId",B);let b=`/api/admin/students/bulk?${a.toString()}`;window.open(b,"_blank")},disabled:v,children:[(0,b.jsx)(n.Download,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,b.jsxs)(e.Button,{onClick:()=>u.push("/admin/students/bulk"),variant:"outline",disabled:v,children:[(0,b.jsx)(o.Upload,{className:"w-4 h-4 mr-2"}),"Import"]}),(0,b.jsxs)(e.Button,{onClick:()=>u.push("/admin/students/new"),disabled:v,children:[(0,b.jsx)(j.Plus,{className:"w-4 h-4 mr-2"}),"Add Student"]})]})]})}),(0,b.jsxs)(g.CardContent,{children:[x&&(0,b.jsx)(h.Alert,{variant:"destructive",className:"mb-4",children:(0,b.jsx)(h.AlertDescription,{children:x})}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)("label",{className:"text-sm font-medium",children:"Search"}),(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(i.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,b.jsx)(f.Input,{placeholder:"Search students...",value:z,onChange:a=>A(a.target.value),className:"pl-10",onKeyPress:a=>"Enter"===a.key&&F()})]})]}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)("label",{className:"text-sm font-medium",children:"Class"}),(0,b.jsxs)("select",{value:B,onChange:a=>C(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"",children:"All Classes"}),s.map(a=>a.sections.map(c=>(0,b.jsxs)("option",{value:`${a.id}-${c.id}`,children:[a.name," - ",c.name]},`${a.id}-${c.id}`)))]})]}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)("label",{className:"text-sm font-medium",children:"Gender"}),(0,b.jsxs)("select",{value:D,onChange:a=>E(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"",children:"All Genders"}),(0,b.jsx)("option",{value:"MALE",children:"Male"}),(0,b.jsx)("option",{value:"FEMALE",children:"Female"}),(0,b.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)("label",{className:"text-sm font-medium",children:" "}),(0,b.jsxs)(e.Button,{onClick:F,className:"w-full",disabled:v,children:[v?(0,b.jsx)(r.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}):(0,b.jsx)(i.Search,{className:"w-4 h-4 mr-2"}),"Search"]})]})]}),(0,b.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,b.jsxs)("table",{className:"w-full border-collapse border border-gray-200",children:[(0,b.jsx)("thead",{children:(0,b.jsxs)("tr",{className:"bg-gray-50",children:[(0,b.jsx)("th",{className:"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700",children:"Name"}),(0,b.jsx)("th",{className:"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700",children:"Email"}),(0,b.jsx)("th",{className:"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700",children:"Class"}),(0,b.jsx)("th",{className:"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700",children:"Gender"}),(0,b.jsx)("th",{className:"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700",children:"Phone"}),(0,b.jsx)("th",{className:"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700",children:"Admission Date"}),(0,b.jsx)("th",{className:"border border-gray-200 px-4 py-2 text-center text-sm font-medium text-gray-700",children:"Actions"})]})}),(0,b.jsx)("tbody",{children:a.map(a=>(0,b.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,b.jsx)("td",{className:"border border-gray-200 px-4 py-2",children:(0,b.jsxs)("div",{children:[(0,b.jsxs)("div",{className:"font-medium",children:[a.user.firstName," ",a.user.lastName]}),(0,b.jsx)("div",{className:"text-sm text-gray-500",children:I(a.dob)})]})}),(0,b.jsx)("td",{className:"border border-gray-200 px-4 py-2",children:a.user.email}),(0,b.jsx)("td",{className:"border border-gray-200 px-4 py-2",children:a.currentClass?`${a.currentClass.name} - ${a.currentSection?.name||"N/A"}`:"Not assigned"}),(0,b.jsx)("td",{className:"border border-gray-200 px-4 py-2",children:J(a.gender)}),(0,b.jsx)("td",{className:"border border-gray-200 px-4 py-2",children:a.user.phone||"-"}),(0,b.jsx)("td",{className:"border border-gray-200 px-4 py-2",children:I(a.createdAt)}),(0,b.jsx)("td",{className:"border border-gray-200 px-4 py-2",children:(0,b.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,b.jsx)(e.Button,{variant:"ghost",size:"sm",onClick:()=>u.push(`/admin/students/${a.id}`),disabled:v,children:(0,b.jsx)(m.Eye,{className:"w-4 h-4"})}),(0,b.jsx)(e.Button,{variant:"ghost",size:"sm",onClick:()=>u.push(`/admin/students/${a.id}/edit`),disabled:v,children:(0,b.jsx)(k.Edit,{className:"w-4 h-4"})}),(0,b.jsx)(e.Button,{variant:"ghost",size:"sm",onClick:()=>H(a.id,`${a.user.firstName} ${a.user.lastName}`),disabled:v,className:"text-red-600 hover:text-red-700",children:(0,b.jsx)(l.Trash2,{className:"w-4 h-4"})})]})})]},a.id))})]})}),(0,b.jsx)("div",{className:"lg:hidden space-y-4",children:a.map(a=>(0,b.jsx)(g.Card,{className:"p-4",children:(0,b.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-start justify-between",children:[(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 truncate",children:[a.user.firstName," ",a.user.lastName]}),(0,b.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 truncate",children:a.user.email})]}),(0,b.jsxs)("div",{className:"flex space-x-1 ml-4",children:[(0,b.jsx)(e.Button,{variant:"ghost",size:"sm",onClick:()=>u.push(`/admin/students/${a.id}`),disabled:v,children:(0,b.jsx)(m.Eye,{className:"w-4 h-4"})}),(0,b.jsx)(e.Button,{variant:"ghost",size:"sm",onClick:()=>u.push(`/admin/students/${a.id}/edit`),disabled:v,children:(0,b.jsx)(k.Edit,{className:"w-4 h-4"})}),(0,b.jsx)(e.Button,{variant:"ghost",size:"sm",onClick:()=>H(a.id,`${a.user.firstName} ${a.user.lastName}`),disabled:v,className:"text-red-600 hover:text-red-700",children:(0,b.jsx)(l.Trash2,{className:"w-4 h-4"})})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Class:"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:a.currentClass?`${a.currentClass.name} - ${a.currentSection?.name||"N/A"}`:"Not assigned"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Gender:"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:J(a.gender)})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Phone:"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:a.user.phone||"-"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"DOB:"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:I(a.dob)})]})]}),(0,b.jsx)("div",{className:"pt-2 border-t border-gray-200 dark:border-gray-700",children:(0,b.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Admitted: ",I(a.createdAt)]})})]})},a.id))}),t.totalPages>1&&(0,b.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0 mt-6",children:[(0,b.jsxs)("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",(t.page-1)*t.limit+1," to"," ",Math.min(t.page*t.limit,t.totalCount)," of"," ",t.totalCount," students"]}),(0,b.jsxs)("div",{className:"flex justify-center sm:justify-end gap-2",children:[(0,b.jsxs)(e.Button,{variant:"outline",size:"sm",onClick:()=>G(t.page-1),disabled:!t.hasPrevPage||v,children:[(0,b.jsx)(p,{className:"w-4 h-4 sm:mr-1"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Previous"})]}),(0,b.jsxs)("div",{className:"flex items-center px-3 py-2 text-sm bg-gray-50 rounded-md",children:[(0,b.jsx)("span",{className:"hidden sm:inline",children:"Page "}),t.page," ",(0,b.jsxs)("span",{className:"hidden sm:inline",children:["of ",t.totalPages]}),(0,b.jsxs)("span",{className:"sm:hidden",children:["/",t.totalPages]})]}),(0,b.jsxs)(e.Button,{variant:"outline",size:"sm",onClick:()=>G(t.page+1),disabled:!t.hasNextPage||v,children:[(0,b.jsx)("span",{className:"hidden sm:inline",children:"Next"}),(0,b.jsx)(q.ChevronRight,{className:"w-4 h-4 sm:ml-1"})]})]})]}),0===a.length&&(0,b.jsxs)("div",{className:"text-center py-8 text-gray-500",children:["No students found. ",z||B||D?"Try adjusting your search criteria.":"Add your first student to get started."]})]})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__b4812314._.js.map