{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yQAAO,EAAC,IAAA,mOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kVAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,4TAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,8UAAC;QACC,MAAM;QACN,WAAW,IAAA,8JAAE,EACX,yaACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,IAAA,uRAAG,EACvB;AAGF,MAAM,sBAAQ,4TAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,2TAAmB;QAClB,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,2TAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4TAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4TAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4TAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4TAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QAAI,KAAK;QAAK,WAAW,IAAA,8JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4TAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800\",\r\n        destructive:\r\n          \"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,IAAA,uRAAG,EACvB,sLACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,4TAAgB,MAG5B,QAAmC;QAAlC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;yBACjC,8UAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,IAAA,8JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,4TAAgB,OAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'FileText' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth-utils.ts"], "sourcesContent": ["import { signIn, signOut, getSession } from 'next-auth/react'\nimport { getRoleDashboardUrl } from './navigation'\n\nexport interface LoginCredentials {\n  email: string\n  password: string\n}\n\nexport interface LoginResult {\n  success: boolean\n  error?: string\n  redirectUrl?: string\n}\n\n/**\n * Login function for admin, teacher, and student users\n * @param credentials - Email and password\n * @returns Promise with login result\n */\nexport async function loginUser(credentials: LoginCredentials): Promise<LoginResult> {\n  try {\n    // Validate input\n    if (!credentials.email || !credentials.password) {\n      return {\n        success: false,\n        error: 'Email and password are required'\n      }\n    }\n\n    if (!credentials.email.includes('@')) {\n      return {\n        success: false,\n        error: 'Please enter a valid email address'\n      }\n    }\n\n    if (credentials.password.length < 6) {\n      return {\n        success: false,\n        error: 'Password must be at least 6 characters long'\n      }\n    }\n\n    // Attempt to sign in\n    const result = await signIn('credentials', {\n      email: credentials.email,\n      password: credentials.password,\n      redirect: false\n    })\n\n    if (result?.error) {\n      return {\n        success: false,\n        error: 'Invalid email or password. Please try again.'\n      }\n    }\n\n    // Get session to determine redirect URL\n    const session = await getSession()\n    const redirectUrl = session?.user?.role\n      ? getRoleDashboardUrl(session.user.role)\n      : '/'\n\n    return {\n      success: true,\n      redirectUrl\n    }\n  } catch (error) {\n    console.error('Login error:', error)\n    return {\n      success: false,\n      error: 'An unexpected error occurred. Please try again.'\n    }\n  }\n}\n\n/**\n * Logout function\n * @param redirectUrl - Optional URL to redirect to after logout\n */\nexport async function logoutUser(redirectUrl: string = '/login'): Promise<void> {\n  try {\n    await signOut({ \n      redirect: true,\n      callbackUrl: redirectUrl \n    })\n  } catch (error) {\n    console.error('Logout error:', error)\n    // Force redirect even if signOut fails\n    window.location.href = redirectUrl\n  }\n}\n\n/**\n * Check if user is authenticated and has the required role\n * @param requiredRole - Required user role (optional)\n * @returns Promise with authentication status\n */\nexport async function checkAuth(requiredRole?: string): Promise<{\n  isAuthenticated: boolean\n  user?: any\n  hasRequiredRole: boolean\n}> {\n  try {\n    const session = await getSession()\n    \n    if (!session?.user) {\n      return {\n        isAuthenticated: false,\n        hasRequiredRole: false\n      }\n    }\n\n    const hasRequiredRole = !requiredRole || session.user.role === requiredRole\n\n    return {\n      isAuthenticated: true,\n      user: session.user,\n      hasRequiredRole\n    }\n  } catch (error) {\n    console.error('Auth check error:', error)\n    return {\n      isAuthenticated: false,\n      hasRequiredRole: false\n    }\n  }\n}\n\n/**\n * Get current user session\n * @returns Promise with user session or null\n */\nexport async function getCurrentUser() {\n  try {\n    const session = await getSession()\n    return session?.user || null\n  } catch (error) {\n    console.error('Get current user error:', error)\n    return null\n  }\n}\n\n/**\n * Demo credentials for testing\n */\nexport const DEMO_CREDENTIALS = {\n  admin: {\n    email: '<EMAIL>',\n    password: 'Admin@12345',\n    role: 'ADMIN'\n  },\n  teacher: {\n    email: '<EMAIL>',\n    password: 'Teacher@12345',\n    role: 'TEACHER'\n  },\n  student: {\n    email: '<EMAIL>',\n    password: 'Student@12345',\n    role: 'STUDENT'\n  }\n} as const\n\n/**\n * Validate email format\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n/**\n * Validate password strength\n */\nexport function isValidPassword(password: string): {\n  isValid: boolean\n  errors: string[]\n} {\n  const errors: string[] = []\n  \n  if (password.length < 6) {\n    errors.push('Password must be at least 6 characters long')\n  }\n  \n  if (password.length > 128) {\n    errors.push('Password must be less than 128 characters')\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter')\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter')\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number')\n  }\n  \n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character')\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\n/**\n * Generate a secure random password\n */\nexport function generateSecurePassword(length: number = 12): string {\n  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n  const lowercase = 'abcdefghijklmnopqrstuvwxyz'\n  const numbers = '0123456789'\n  const symbols = '!@#$%^&*(),.?\":{}|<>'\n  \n  const allChars = uppercase + lowercase + numbers + symbols\n  let password = ''\n  \n  // Ensure at least one character from each category\n  password += uppercase[Math.floor(Math.random() * uppercase.length)]\n  password += lowercase[Math.floor(Math.random() * lowercase.length)]\n  password += numbers[Math.floor(Math.random() * numbers.length)]\n  password += symbols[Math.floor(Math.random() * symbols.length)]\n  \n  // Fill the rest randomly\n  for (let i = 4; i < length; i++) {\n    password += allChars[Math.floor(Math.random() * allChars.length)]\n  }\n  \n  // Shuffle the password\n  return password.split('').sort(() => Math.random() - 0.5).join('')\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAkBO,eAAe,UAAU,WAA6B;IAC3D,IAAI;YAuCkB;QAtCpB,iBAAiB;QACjB,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,QAAQ,EAAE;YAC/C,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI,CAAC,YAAY,KAAK,CAAC,QAAQ,CAAC,MAAM;YACpC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,qBAAqB;QACrB,MAAM,SAAS,MAAM,IAAA,ySAAM,EAAC,eAAe;YACzC,OAAO,YAAY,KAAK;YACxB,UAAU,YAAY,QAAQ;YAC9B,UAAU;QACZ;QAEA,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE;YACjB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,wCAAwC;QACxC,MAAM,UAAU,MAAM,IAAA,6SAAU;QAChC,MAAM,cAAc,CAAA,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI,IACnC,IAAA,oLAAmB,EAAC,QAAQ,IAAI,CAAC,IAAI,IACrC;QAEJ,OAAO;YACL,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAMO,eAAe;QAAW,cAAA,iEAAsB;IACrD,IAAI;QACF,MAAM,IAAA,0SAAO,EAAC;YACZ,UAAU;YACV,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,uCAAuC;QACvC,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;AACF;AAOO,eAAe,UAAU,YAAqB;IAKnD,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,6SAAU;QAEhC,IAAI,EAAC,oBAAA,8BAAA,QAAS,IAAI,GAAE;YAClB,OAAO;gBACL,iBAAiB;gBACjB,iBAAiB;YACnB;QACF;QAEA,MAAM,kBAAkB,CAAC,gBAAgB,QAAQ,IAAI,CAAC,IAAI,KAAK;QAE/D,OAAO;YACL,iBAAiB;YACjB,MAAM,QAAQ,IAAI;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO;YACL,iBAAiB;YACjB,iBAAiB;QACnB;IACF;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,6SAAU;QAChC,OAAO,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAKO,MAAM,mBAAmB;IAC9B,OAAO;QACL,OAAO;QACP,UAAU;QACV,MAAM;IACR;IACA,SAAS;QACP,OAAO;QACP,UAAU;QACV,MAAM;IACR;IACA,SAAS;QACP,OAAO;QACP,UAAU;QACV,MAAM;IACR;AACF;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,gBAAgB,QAAgB;IAI9C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,SAAS,MAAM,GAAG,KAAK;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAKO,SAAS;QAAuB,SAAA,iEAAiB;IACtD,MAAM,YAAY;IAClB,MAAM,YAAY;IAClB,MAAM,UAAU;IAChB,MAAM,UAAU;IAEhB,MAAM,WAAW,YAAY,YAAY,UAAU;IACnD,IAAI,WAAW;IAEf,mDAAmD;IACnD,YAAY,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;IACnE,YAAY,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;IACnE,YAAY,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;IAC/D,YAAY,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;IAE/D,yBAAyB;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,YAAY,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;IACnE;IAEA,uBAAuB;IACvB,OAAO,SAAS,KAAK,CAAC,IAAI,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC;AACjE", "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/hooks/use-login.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { loginUser, logoutUser, type LoginCredentials, type LoginResult } from '@/lib/auth-utils'\n\ninterface UseLoginState {\n  isLoading: boolean\n  error: string | null\n  success: boolean\n}\n\ninterface UseLoginReturn extends UseLoginState {\n  login: (credentials: LoginCredentials) => Promise<LoginResult>\n  logout: (redirectUrl?: string) => Promise<void>\n  clearError: () => void\n  clearSuccess: () => void\n  reset: () => void\n}\n\n/**\n * Custom hook for handling login functionality\n */\nexport function useLogin(): UseLoginReturn {\n  const router = useRouter()\n  const [state, setState] = useState<UseLoginState>({\n    isLoading: false,\n    error: null,\n    success: false\n  })\n\n  const login = useCallback(async (credentials: LoginCredentials): Promise<LoginResult> => {\n    setState(prev => ({\n      ...prev,\n      isLoading: true,\n      error: null,\n      success: false\n    }))\n\n    try {\n      const result = await loginUser(credentials)\n\n      if (result.success) {\n        setState(prev => ({\n          ...prev,\n          isLoading: false,\n          success: true\n        }))\n\n        // Redirect to dashboard\n        if (result.redirectUrl) {\n          router.push(result.redirectUrl)\n        }\n      } else {\n        setState(prev => ({\n          ...prev,\n          isLoading: false,\n          error: result.error || 'Login failed'\n        }))\n      }\n\n      return result\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'\n      \n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: errorMessage\n      }))\n\n      return {\n        success: false,\n        error: errorMessage\n      }\n    }\n  }, [router])\n\n  const logout = useCallback(async (redirectUrl: string = '/login'): Promise<void> => {\n    setState(prev => ({\n      ...prev,\n      isLoading: true,\n      error: null\n    }))\n\n    try {\n      await logoutUser(redirectUrl)\n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        success: true\n      }))\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Logout failed'\n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: errorMessage\n      }))\n    }\n  }, [])\n\n  const clearError = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      error: null\n    }))\n  }, [])\n\n  const clearSuccess = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      success: false\n    }))\n  }, [])\n\n  const reset = useCallback(() => {\n    setState({\n      isLoading: false,\n      error: null,\n      success: false\n    })\n  }, [])\n\n  return {\n    ...state,\n    login,\n    logout,\n    clearError,\n    clearSuccess,\n    reset\n  }\n}\n\n/**\n * Hook for form-based login with validation\n */\nexport function useLoginForm() {\n  const { login, ...loginState } = useLogin()\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  })\n  const [validationErrors, setValidationErrors] = useState<{\n    email?: string\n    password?: string\n  }>({})\n\n  const updateField = useCallback((field: keyof typeof formData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n\n    // Clear validation error for this field\n    if (validationErrors[field]) {\n      setValidationErrors(prev => ({\n        ...prev,\n        [field]: undefined\n      }))\n    }\n\n    // Clear login error when user starts typing\n    if (loginState.error) {\n      loginState.clearError()\n    }\n  }, [validationErrors, loginState])\n\n  const validateForm = useCallback((): boolean => {\n    const errors: typeof validationErrors = {}\n\n    if (!formData.email.trim()) {\n      errors.email = 'Email is required'\n    } else if (!formData.email.includes('@')) {\n      errors.email = 'Please enter a valid email address'\n    }\n\n    if (!formData.password.trim()) {\n      errors.password = 'Password is required'\n    } else if (formData.password.length < 6) {\n      errors.password = 'Password must be at least 6 characters long'\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }, [formData])\n\n  const handleSubmit = useCallback(async (e?: React.FormEvent) => {\n    if (e) {\n      e.preventDefault()\n    }\n\n    if (!validateForm()) {\n      return { success: false, error: 'Please fix the validation errors' }\n    }\n\n    return await login(formData)\n  }, [formData, validateForm, login])\n\n  const resetForm = useCallback(() => {\n    setFormData({\n      email: '',\n      password: ''\n    })\n    setValidationErrors({})\n    loginState.reset()\n  }, [loginState])\n\n  const fillDemoCredentials = useCallback((role: 'admin' | 'teacher' | 'student') => {\n    const credentials = {\n      admin: { email: '<EMAIL>', password: 'Admin@12345' },\n      teacher: { email: '<EMAIL>', password: 'Teacher@12345' },\n      student: { email: '<EMAIL>', password: 'Student@12345' }\n    }\n\n    setFormData(credentials[role])\n    setValidationErrors({})\n  }, [])\n\n  return {\n    ...loginState,\n    formData,\n    validationErrors,\n    updateField,\n    validateForm,\n    handleSubmit,\n    resetForm,\n    fillDemoCredentials\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;AAJA;;;;AAuBO,SAAS;;IACd,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0TAAQ,EAAgB;QAChD,WAAW;QACX,OAAO;QACP,SAAS;IACX;IAEA,MAAM,QAAQ,IAAA,6TAAW;uCAAC,OAAO;YAC/B;+CAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO;wBACP,SAAS;oBACX,CAAC;;YAED,IAAI;gBACF,MAAM,SAAS,MAAM,IAAA,6KAAS,EAAC;gBAE/B,IAAI,OAAO,OAAO,EAAE;oBAClB;uDAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,WAAW;gCACX,SAAS;4BACX,CAAC;;oBAED,wBAAwB;oBACxB,IAAI,OAAO,WAAW,EAAE;wBACtB,OAAO,IAAI,CAAC,OAAO,WAAW;oBAChC;gBACF,OAAO;oBACL;uDAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,WAAW;gCACX,OAAO,OAAO,KAAK,IAAI;4BACzB,CAAC;;gBACH;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAE9D;mDAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,WAAW;4BACX,OAAO;wBACT,CAAC;;gBAED,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;QACF;sCAAG;QAAC;KAAO;IAEX,MAAM,SAAS,IAAA,6TAAW;wCAAC;gBAAO,+EAAsB;YACtD;gDAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO;oBACT,CAAC;;YAED,IAAI;gBACF,MAAM,IAAA,8KAAU,EAAC;gBACjB;oDAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,WAAW;4BACX,SAAS;wBACX,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D;oDAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,WAAW;4BACX,OAAO;wBACT,CAAC;;YACH;QACF;uCAAG,EAAE;IAEL,MAAM,aAAa,IAAA,6TAAW;4CAAC;YAC7B;oDAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,OAAO;oBACT,CAAC;;QACH;2CAAG,EAAE;IAEL,MAAM,eAAe,IAAA,6TAAW;8CAAC;YAC/B;sDAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,SAAS;oBACX,CAAC;;QACH;6CAAG,EAAE;IAEL,MAAM,QAAQ,IAAA,6TAAW;uCAAC;YACxB,SAAS;gBACP,WAAW;gBACX,OAAO;gBACP,SAAS;YACX;QACF;sCAAG,EAAE;IAEL,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;IACF;AACF;GA7GgB;;QACC,mSAAS;;;AAiHnB,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,YAAY,GAAG;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,0TAAQ,EAAC;QACvC,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,0TAAQ,EAGrD,CAAC;IAEJ,MAAM,cAAc,IAAA,6TAAW;iDAAC,CAAC,OAA8B;YAC7D;yDAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,MAAM,EAAE;oBACX,CAAC;;YAED,wCAAwC;YACxC,IAAI,gBAAgB,CAAC,MAAM,EAAE;gBAC3B;6DAAoB,CAAA,OAAQ,CAAC;4BAC3B,GAAG,IAAI;4BACP,CAAC,MAAM,EAAE;wBACX,CAAC;;YACH;YAEA,4CAA4C;YAC5C,IAAI,WAAW,KAAK,EAAE;gBACpB,WAAW,UAAU;YACvB;QACF;gDAAG;QAAC;QAAkB;KAAW;IAEjC,MAAM,eAAe,IAAA,6TAAW;kDAAC;YAC/B,MAAM,SAAkC,CAAC;YAEzC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;gBAC1B,OAAO,KAAK,GAAG;YACjB,OAAO,IAAI,CAAC,SAAS,KAAK,CAAC,QAAQ,CAAC,MAAM;gBACxC,OAAO,KAAK,GAAG;YACjB;YAEA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;gBAC7B,OAAO,QAAQ,GAAG;YACpB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACvC,OAAO,QAAQ,GAAG;YACpB;YAEA,oBAAoB;YACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;iDAAG;QAAC;KAAS;IAEb,MAAM,eAAe,IAAA,6TAAW;kDAAC,OAAO;YACtC,IAAI,GAAG;gBACL,EAAE,cAAc;YAClB;YAEA,IAAI,CAAC,gBAAgB;gBACnB,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAmC;YACrE;YAEA,OAAO,MAAM,MAAM;QACrB;iDAAG;QAAC;QAAU;QAAc;KAAM;IAElC,MAAM,YAAY,IAAA,6TAAW;+CAAC;YAC5B,YAAY;gBACV,OAAO;gBACP,UAAU;YACZ;YACA,oBAAoB,CAAC;YACrB,WAAW,KAAK;QAClB;8CAAG;QAAC;KAAW;IAEf,MAAM,sBAAsB,IAAA,6TAAW;yDAAC,CAAC;YACvC,MAAM,cAAc;gBAClB,OAAO;oBAAE,OAAO;oBAAqB,UAAU;gBAAc;gBAC7D,SAAS;oBAAE,OAAO;oBAAwB,UAAU;gBAAgB;gBACpE,SAAS;oBAAE,OAAO;oBAAwB,UAAU;gBAAgB;YACtE;YAEA,YAAY,WAAW,CAAC,KAAK;YAC7B,oBAAoB,CAAC;QACvB;wDAAG,EAAE;IAEL,OAAO;QACL,GAAG,UAAU;QACb;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IA5FgB;;QACmB", "debugId": null}}, {"offset": {"line": 1009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28auth%29/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, Eye, EyeOff, User, Lock } from 'lucide-react'\nimport { DEMO_CREDENTIALS } from '@/lib/auth-utils'\nimport { useLoginForm } from '@/hooks/use-login'\n\nexport default function LoginPage() {\n  const [showPassword, setShowPassword] = useState(false)\n  const {\n    formData,\n    validationErrors,\n    isLoading,\n    error,\n    updateField,\n    handleSubmit,\n    fillDemoCredentials\n  } = useLoginForm()\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-950 p-4\">\n      <Card className=\"w-full max-w-md mx-auto\">\n        <CardHeader className=\"text-center px-4 sm:px-6\">\n          <CardTitle className=\"text-xl sm:text-2xl font-bold leading-tight\">\n            School Management System\n          </CardTitle>\n          <CardDescription className=\"text-sm sm:text-base mt-2\">\n            Sign in to your account\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"px-4 sm:px-6\">\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {error && (\n              <Alert variant=\"destructive\">\n                <AlertDescription>{error}</AlertDescription>\n              </Alert>\n            )}\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email Address</Label>\n              <div className=\"relative\">\n                <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  value={formData.email}\n                  onChange={(e) => updateField('email', e.target.value)}\n                  placeholder=\"Enter your email address\"\n                  className={`pl-10 min-h-[44px] text-base ${validationErrors.email ? 'border-red-500' : ''}`}\n                  required\n                  disabled={isLoading}\n                />\n              </div>\n              {validationErrors.email && (\n                <p className=\"text-sm text-red-600 dark:text-red-400\">{validationErrors.email}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">Password</Label>\n              <div className=\"relative\">\n                <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  id=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  value={formData.password}\n                  onChange={(e) => updateField('password', e.target.value)}\n                  placeholder=\"Enter your password\"\n                  className={`pl-10 pr-10 min-h-[44px] text-base ${validationErrors.password ? 'border-red-500' : ''}`}\n                  required\n                  disabled={isLoading}\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                  disabled={isLoading}\n                >\n                  {showPassword ? (\n                    <EyeOff className=\"h-4 w-4\" />\n                  ) : (\n                    <Eye className=\"h-4 w-4\" />\n                  )}\n                </button>\n              </div>\n              {validationErrors.password && (\n                <p className=\"text-sm text-red-600 dark:text-red-400\">{validationErrors.password}</p>\n              )}\n            </div>\n\n            <Button\n              type=\"submit\"\n              className=\"w-full min-h-[44px] text-base\"\n              disabled={isLoading}\n            >\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                  Signing in...\n                </>\n              ) : (\n                'Sign In'\n              )}\n            </Button>\n          </form>\n\n          <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n            <h3 className=\"font-semibold mb-3 text-center text-gray-900 dark:text-gray-100 text-sm sm:text-base\">\n              Demo Credentials\n            </h3>\n            <div className=\"space-y-3\">\n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => fillDemoCredentials('admin')}\n                  disabled={isLoading}\n                  className=\"text-xs\"\n                >\n                  Fill Admin\n                </Button>\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => fillDemoCredentials('teacher')}\n                  disabled={isLoading}\n                  className=\"text-xs\"\n                >\n                  Fill Teacher\n                </Button>\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => fillDemoCredentials('student')}\n                  disabled={isLoading}\n                  className=\"text-xs\"\n                >\n                  Fill Student\n                </Button>\n              </div>\n              <div className=\"space-y-1 text-xs text-gray-600 dark:text-gray-400\">\n                <div className=\"text-center\">\n                  <p><strong>Admin:</strong> {DEMO_CREDENTIALS.admin.email}</p>\n                  <p><strong>Teacher:</strong> {DEMO_CREDENTIALS.teacher.email}</p>\n                  <p><strong>Student:</strong> {DEMO_CREDENTIALS.student.email}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,0TAAQ,EAAC;IACjD,MAAM,EACJ,QAAQ,EACR,gBAAgB,EAChB,SAAS,EACT,KAAK,EACL,WAAW,EACX,YAAY,EACZ,mBAAmB,EACpB,GAAG,IAAA,iLAAY;IAEhB,qBACE,8UAAC;QAAI,WAAU;kBACb,cAAA,8UAAC,6KAAI;YAAC,WAAU;;8BACd,8UAAC,mLAAU;oBAAC,WAAU;;sCACpB,8UAAC,kLAAS;4BAAC,WAAU;sCAA8C;;;;;;sCAGnE,8UAAC,wLAAe;4BAAC,WAAU;sCAA4B;;;;;;;;;;;;8BAIzD,8UAAC,oLAAW;oBAAC,WAAU;;sCACrB,8UAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,uBACC,8UAAC,+KAAK;oCAAC,SAAQ;8CACb,cAAA,8UAAC,0LAAgB;kDAAE;;;;;;;;;;;8CAIvB,8UAAC;oCAAI,WAAU;;sDACb,8UAAC,+KAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8UAAC;4CAAI,WAAU;;8DACb,8UAAC,mUAAI;oDAAC,WAAU;;;;;;8DAChB,8UAAC,+KAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;oDACpD,aAAY;oDACZ,WAAW,AAAC,gCAA8E,OAA/C,iBAAiB,KAAK,GAAG,mBAAmB;oDACvF,QAAQ;oDACR,UAAU;;;;;;;;;;;;wCAGb,iBAAiB,KAAK,kBACrB,8UAAC;4CAAE,WAAU;sDAA0C,iBAAiB,KAAK;;;;;;;;;;;;8CAIjF,8UAAC;oCAAI,WAAU;;sDACb,8UAAC,+KAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,8UAAC;4CAAI,WAAU;;8DACb,8UAAC,mUAAI;oDAAC,WAAU;;;;;;8DAChB,8UAAC,+KAAK;oDACJ,IAAG;oDACH,MAAM,eAAe,SAAS;oDAC9B,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,YAAY,YAAY,EAAE,MAAM,CAAC,KAAK;oDACvD,aAAY;oDACZ,WAAW,AAAC,sCAAuF,OAAlD,iBAAiB,QAAQ,GAAG,mBAAmB;oDAChG,QAAQ;oDACR,UAAU;;;;;;8DAEZ,8UAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;oDACV,UAAU;8DAET,6BACC,8UAAC,6UAAM;wDAAC,WAAU;;;;;6EAElB,8UAAC,gUAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAIpB,iBAAiB,QAAQ,kBACxB,8UAAC;4CAAE,WAAU;sDAA0C,iBAAiB,QAAQ;;;;;;;;;;;;8CAIpF,8UAAC,iLAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU;8CAET,0BACC;;0DACE,8UAAC,qVAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;sCAKN,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAG,WAAU;8CAAuF;;;;;;8CAGrG,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;;8DACb,8UAAC,iLAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,oBAAoB;oDACnC,UAAU;oDACV,WAAU;8DACX;;;;;;8DAGD,8UAAC,iLAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,oBAAoB;oDACnC,UAAU;oDACV,WAAU;8DACX;;;;;;8DAGD,8UAAC,iLAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,oBAAoB;oDACnC,UAAU;oDACV,WAAU;8DACX;;;;;;;;;;;;sDAIH,8UAAC;4CAAI,WAAU;sDACb,cAAA,8UAAC;gDAAI,WAAU;;kEACb,8UAAC;;0EAAE,8UAAC;0EAAO;;;;;;4DAAe;4DAAE,oLAAgB,CAAC,KAAK,CAAC,KAAK;;;;;;;kEACxD,8UAAC;;0EAAE,8UAAC;0EAAO;;;;;;4DAAiB;4DAAE,oLAAgB,CAAC,OAAO,CAAC,KAAK;;;;;;;kEAC5D,8UAAC;;0EAAE,8UAAC;0EAAO;;;;;;4DAAiB;4DAAE,oLAAgB,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9E;GArJwB;;QAUlB,iLAAY;;;KAVM", "debugId": null}}]}