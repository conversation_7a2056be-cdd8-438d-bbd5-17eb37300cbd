{"version": 3, "sources": ["turbopack:///[project]/school-management-system/src/app/(dash)/student/marks/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport DashboardLayout from '@/components/layout/dashboard-layout'\nimport { studentNavigation } from '@/lib/navigation'\nimport {\n  Calendar,\n  Award,\n  BookOpen,\n  TrendingUp,\n  FileText,\n  Download,\n  Eye\n} from 'lucide-react'\n\ninterface MarkRecord {\n  id: string\n  obtainedMarks: number\n  remarks?: string\n  createdAt: string\n  updatedAt: string\n  exam: {\n    id: string\n    name: string\n    maxMarks: number\n    date: string\n    subject: {\n      id: string\n      name: string\n      code: string\n    }\n    term: {\n      id: string\n      name: string\n    }\n  }\n}\n\n\n\nexport default function StudentMarksPage() {\n  const { data: session } = useSession()\n  const [markRecords, setMarkRecords] = useState<MarkRecord[]>([])\n  const [selectedTerm, setSelectedTerm] = useState('all')\n  const [selectedSubject, setSelectedSubject] = useState('all')\n  const [loading, setLoading] = useState(true)\n  const [terms, setTerms] = useState<Array<{id: string, name: string}>>([])\n  const [subjects, setSubjects] = useState<Array<{id: string, name: string}>>([])\n\n  useEffect(() => {\n    fetchMarks()\n    fetchTermsAndSubjects()\n  }, [selectedTerm, selectedSubject])\n\n  const fetchMarks = async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams()\n      if (selectedTerm !== 'all') params.append('termId', selectedTerm)\n      if (selectedSubject !== 'all') params.append('subjectId', selectedSubject)\n\n      const response = await fetch(`/api/student/marks?${params}`)\n      if (response.ok) {\n        const data = await response.json()\n        setMarkRecords(data)\n      } else {\n        console.error('Failed to fetch marks')\n      }\n    } catch (error) {\n      console.error('Error fetching marks:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchTermsAndSubjects = async () => {\n    try {\n      // Fetch terms and subjects for filters\n      // For now, we'll use static data but this could be from API\n      setTerms([\n        { id: 'term1', name: 'Term 1' },\n        { id: 'term2', name: 'Term 2' },\n        { id: 'term3', name: 'Term 3' }\n      ])\n      setSubjects([\n        { id: 'math', name: 'Mathematics' },\n        { id: 'english', name: 'English' },\n        { id: 'science', name: 'Science' },\n        { id: 'social', name: 'Social Studies' },\n        { id: 'computer', name: 'Computer Science' }\n      ])\n    } catch (error) {\n      console.error('Error fetching terms and subjects:', error)\n    }\n  }\n\n  const getGradeColor = (grade: string) => {\n    switch (grade) {\n      case 'A+':\n        return 'bg-green-100 text-green-800'\n      case 'A':\n        return 'bg-green-100 text-green-800'\n      case 'B+':\n        return 'bg-blue-100 text-blue-800'\n      case 'B':\n        return 'bg-blue-100 text-blue-800'\n      case 'C+':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'C':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'D':\n        return 'bg-orange-100 text-orange-800'\n      case 'F':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getGrade = (percentage: number) => {\n    if (percentage >= 90) return 'A+'\n    if (percentage >= 80) return 'A'\n    if (percentage >= 70) return 'B+'\n    if (percentage >= 60) return 'B'\n    if (percentage >= 50) return 'C+'\n    if (percentage >= 40) return 'C'\n    if (percentage >= 30) return 'D'\n    return 'F'\n  }\n\n  const marksWithCalculations = markRecords.map(record => {\n    const percentage = Math.round((record.obtainedMarks / record.exam.maxMarks) * 100 * 100) / 100\n    const grade = getGrade(percentage)\n    return {\n      ...record,\n      percentage,\n      grade\n    }\n  })\n\n  const marksStats = {\n    total: marksWithCalculations.length,\n    average: marksWithCalculations.length > 0\n      ? Math.round(marksWithCalculations.reduce((sum, record) => sum + record.percentage, 0) / marksWithCalculations.length)\n      : 0,\n    highest: marksWithCalculations.length > 0 ? Math.max(...marksWithCalculations.map(r => r.percentage)) : 0,\n    lowest: marksWithCalculations.length > 0 ? Math.min(...marksWithCalculations.map(r => r.percentage)) : 0,\n    passed: marksWithCalculations.filter(r => r.percentage >= 40).length,\n    failed: marksWithCalculations.filter(r => r.percentage < 40).length\n  }\n\n  return (\n    <DashboardLayout title=\"My Marks\" navigation={studentNavigation}>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0\">\n          <div>\n            <h1 className=\"text-xl sm:text-2xl font-bold text-gray-900\">My Marks</h1>\n            <p className=\"text-sm sm:text-base text-gray-600\">View your examination marks and grades</p>\n          </div>\n          <div className=\"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2\">\n            <Button variant=\"outline\" className=\"w-full sm:w-auto\">\n              <Download className=\"w-4 h-4 mr-2\" />\n              <span className=\"sm:hidden\">Download</span>\n              <span className=\"hidden sm:inline\">Download Report</span>\n            </Button>\n            <Button className=\"w-full sm:w-auto\">\n              <FileText className=\"w-4 h-4 mr-2\" />\n              <span className=\"sm:hidden\">Report Card</span>\n              <span className=\"hidden sm:inline\">View Report Card</span>\n            </Button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Exams</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{marksStats.total}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average Score</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-blue-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-blue-600\">{marksStats.average}%</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Highest Score</CardTitle>\n              <Award className=\"h-4 w-4 text-green-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{marksStats.highest}%</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Lowest Score</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-red-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-red-600\">{marksStats.lowest}%</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Passed</CardTitle>\n              <Award className=\"h-4 w-4 text-green-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{marksStats.passed}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Failed</CardTitle>\n              <Award className=\"h-4 w-4 text-red-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-red-600\">{marksStats.failed}</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Filters</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label htmlFor=\"term\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Term\n                </label>\n                <select\n                  id=\"term\"\n                  value={selectedTerm}\n                  onChange={(e) => setSelectedTerm(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Terms</option>\n                  {terms.map(term => (\n                    <option key={term.id} value={term.id}>{term.name}</option>\n                  ))}\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"subject\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Subject\n                </label>\n                <select\n                  id=\"subject\"\n                  value={selectedSubject}\n                  onChange={(e) => setSelectedSubject(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Subjects</option>\n                  {subjects.map(subject => (\n                    <option key={subject.id} value={subject.id}>{subject.name}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Marks Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>My Examination Marks</CardTitle>\n            <CardDescription>\n              Detailed view of all your examination results\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            {loading ? (\n              <div className=\"text-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n                <p className=\"mt-2 text-gray-600\">Loading marks...</p>\n              </div>\n            ) : marksWithCalculations.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-600\">No marks found</p>\n              </div>\n            ) : (\n              <>\n                {/* Desktop Table */}\n                <div className=\"hidden lg:block overflow-x-auto\">\n                  <table className=\"min-w-full divide-y divide-gray-200\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Exam\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Subject\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Term\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Marks\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Percentage\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Grade\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Date\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Actions\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {marksWithCalculations.map((record) => (\n                        <tr key={record.id}>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <div className=\"flex items-center\">\n                              <BookOpen className=\"w-4 h-4 mr-2 text-blue-600\" />\n                              <span className=\"text-sm font-medium text-gray-900\">{record.exam.name}</span>\n                            </div>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {record.exam.subject.name}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {record.exam.term.name}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {record.obtainedMarks}/{record.exam.maxMarks}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {record.percentage}%\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(record.grade)}`}>\n                              {record.grade}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {new Date(record.exam.date).toLocaleDateString()}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                            <Button variant=\"outline\" size=\"sm\">\n                              <Eye className=\"w-4 h-4\" />\n                            </Button>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n\n                {/* Mobile Cards */}\n                <div className=\"lg:hidden space-y-4\">\n                  {marksWithCalculations.map((record) => (\n                    <Card key={record.id} className=\"p-4\">\n                      <div className=\"flex flex-col space-y-3\">\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n                            <BookOpen className=\"w-5 h-5 text-blue-600 flex-shrink-0\" />\n                            <div className=\"min-w-0 flex-1\">\n                              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\">\n                                {record.exam.name}\n                              </h3>\n                              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                                {record.exam.subject.name} • {record.exam.term.name}\n                              </p>\n                            </div>\n                          </div>\n                          <div className=\"flex items-center space-x-2 ml-4\">\n                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(record.grade)}`}>\n                              {record.grade}\n                            </span>\n                          </div>\n                        </div>\n\n                        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                          <div>\n                            <span className=\"font-medium text-gray-700 dark:text-gray-300\">Marks:</span>\n                            <p className=\"text-gray-600 dark:text-gray-400\">{record.obtainedMarks}/{record.exam.maxMarks}</p>\n                          </div>\n                          <div>\n                            <span className=\"font-medium text-gray-700 dark:text-gray-300\">Percentage:</span>\n                            <p className=\"text-gray-600 dark:text-gray-400 font-semibold\">{record.percentage}%</p>\n                          </div>\n                          <div>\n                            <span className=\"font-medium text-gray-700 dark:text-gray-300\">Date:</span>\n                            <p className=\"text-gray-600 dark:text-gray-400\">{new Date(record.exam.date).toLocaleDateString()}</p>\n                          </div>\n                          <div className=\"flex justify-end\">\n                            <Button variant=\"outline\" size=\"sm\">\n                              <Eye className=\"w-4 h-4 mr-1\" />\n                              View Details\n                            </Button>\n                          </div>\n                          {record.remarks && (\n                            <div className=\"col-span-2\">\n                              <span className=\"font-medium text-gray-700 dark:text-gray-300\">Remarks:</span>\n                              <p className=\"text-gray-600 dark:text-gray-400\">{record.remarks}</p>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </Card>\n                  ))}\n                </div>\n              </>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": "+EAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAmCe,SAAS,IACtB,GAAM,CAAE,KAAM,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IAC9B,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAe,EAAE,EACzD,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,OAC3C,CAAC,EAAiB,EAAmB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,OACjD,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAA4C,EAAE,EAClE,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAoC,EAAE,EAE9E,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,IACA,GACF,EAAG,CAAC,EAAc,EAAgB,EAElC,IAAM,EAAa,UACjB,GAAI,CACF,GAAW,GACX,IAAM,EAAS,IAAI,gBACE,QAAjB,GAAwB,EAAO,MAAM,CAAC,SAAU,GAC5B,QAApB,GAA2B,EAAO,MAAM,CAAC,YAAa,GAE1D,IAAM,EAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,EAAA,CAAQ,EAC3D,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,EAAe,EACjB,MACE,CADK,OACG,KAAK,CAAC,wBAElB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wBAAyB,EACzC,QAAU,CACR,GAAW,EACb,CACF,EAEM,EAAwB,UAC5B,GAAI,CAGF,EAAS,CACP,CAAE,GAAI,QAAS,KAAM,QAAS,EAC9B,CAAE,GAAI,QAAS,KAAM,QAAS,EAC9B,CAAE,GAAI,QAAS,KAAM,QAAS,EAC/B,EACD,EAAY,CACV,CAAE,GAAI,OAAQ,KAAM,aAAc,EAClC,CAAE,GAAI,UAAW,KAAM,SAAU,EACjC,CAAE,GAAI,UAAW,KAAM,SAAU,EACjC,CAAE,GAAI,SAAU,KAAM,gBAAiB,EACvC,CAAE,GAAI,WAAY,KAAM,kBAAmB,EAC5C,CACH,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,qCAAsC,EACtD,CACF,EAEM,EAAgB,AAAC,IACrB,OAAQ,GACN,IAAK,KAEL,IAAK,IADH,MAAO,6BAGT,KAAK,KAEL,IAAK,IADH,MAAO,2BAGT,KAAK,KAEL,IAAK,IADH,MAAO,+BAGT,KAAK,IACH,MAAO,+BACT,KAAK,IACH,MAAO,yBACT,SACE,MAAO,2BACX,CACF,EAaM,EAAwB,EAAY,GAAG,CAAC,IAC5C,IAAM,EAAa,KAAK,KAAK,CAAE,EAAO,aAAa,CAAG,EAAO,IAAI,CAAC,QAAQ,GAAI,GAAa,GAAP,CAC9E,EAZN,AAAI,GAAc,GAAW,AAYf,CAZQ,IAClB,GAAc,GAAW,CAAP,GAClB,AAUmB,GAVL,GAAW,CAAP,IAClB,GAAc,GAAW,CAAP,GAClB,GAAc,GAAW,CAAP,IAClB,GAAc,GAAW,CAAP,GAClB,GAAc,GAAW,CAAP,GACf,IAMP,MAAO,CACL,GAAG,CAAM,YACT,QACA,CACF,CACF,GAEM,EAAa,CACjB,MAAO,EAAsB,MAAM,CACnC,QAAS,EAAsB,MAAM,CAAG,EACpC,KAAK,KAAK,CAAC,EAAsB,MAAM,CAAC,CAAC,EAAK,IAAW,EAAM,EAAO,UAAU,CAAE,GAAK,EAAsB,MAAM,EACnH,EACJ,QAAS,EAAsB,MAAM,CAAG,EAAI,KAAK,GAAG,IAAI,EAAsB,GAAG,CAAC,GAAK,EAAE,UAAU,GAAK,EACxG,OAAQ,EAAsB,MAAM,CAAG,EAAI,KAAK,GAAG,IAAI,EAAsB,GAAG,CAAC,GAAK,EAAE,UAAU,GAAK,EACvG,OAAQ,EAAsB,MAAM,CAAC,GAAK,EAAE,UAAU,EAAI,IAAI,MAAM,CACpE,OAAQ,EAAsB,MAAM,CAAC,GAAK,EAAE,UAAU,CAAG,IAAI,MAAM,AACrE,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAe,CAAA,CAAC,MAAM,WAAW,WAAY,EAAA,iBAAiB,UAC7D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uDAA8C,aAC5D,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8CAAqC,8CAEpD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0EACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,UAAU,6BAClC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,aAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,uBAErC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,6BAChB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,gBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,8BAMzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,gBAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,qCAEtB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,EAAW,KAAK,QAGzD,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,kBAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,6BAExB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CAAoC,EAAW,OAAO,CAAC,YAG1E,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,kBAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,8BAEnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CAAqC,EAAW,OAAO,CAAC,YAG3E,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,iBAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,4BAExB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4CAAmC,EAAW,MAAM,CAAC,YAGxE,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,WAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,8BAEnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CAAqC,EAAW,MAAM,QAGzE,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,WAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,4BAEnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CAAmC,EAAW,MAAM,WAMzE,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,UACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,cAEb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,OAAO,UAAU,wDAA+C,SAG/E,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,GAAG,OACH,MAAO,EACP,SAAU,AAAC,GAAM,EAAgB,EAAE,MAAM,CAAC,KAAK,EAC/C,UAAU,mHAEV,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,eAAM,cACnB,EAAM,GAAG,CAAC,GACT,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAqB,MAAO,EAAK,EAAE,UAAG,EAAK,IAAI,EAAnC,EAAK,EAAE,SAI1B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,UAAU,UAAU,wDAA+C,YAGlF,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,GAAG,UACH,MAAO,EACP,SAAU,AAAC,GAAM,EAAmB,EAAE,MAAM,CAAC,KAAK,EAClD,UAAU,mHAEV,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,eAAM,iBACnB,EAAS,GAAG,CAAC,GACZ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAwB,MAAO,EAAQ,EAAE,UAAG,EAAQ,IAAI,EAA5C,EAAQ,EAAE,iBASnC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,yBACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,qDAInB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACT,EACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yEACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,wBAED,IAAjC,EAAsB,MAAM,CAC9B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,yCACpB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,sBAG/B,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WAEE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAM,UAAU,gDACf,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,sBACf,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,SAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,YAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,SAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,UAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,eAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,UAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,SAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,iBAKnG,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CACd,EAAsB,GAAG,CAAC,AAAC,GAC1B,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uCACZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,+BACpB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,6CAAqC,EAAO,IAAI,CAAC,IAAI,QAGzE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DACX,EAAO,IAAI,CAAC,OAAO,CAAC,IAAI,GAE3B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DACX,EAAO,IAAI,CAAC,IAAI,CAAC,IAAI,GAExB,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,8DACX,EAAO,aAAa,CAAC,IAAE,EAAO,IAAI,CAAC,QAAQ,IAE9C,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,8DACX,EAAO,UAAU,CAAC,OAErB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uCACZ,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,CAAC,yDAAyD,EAAE,EAAc,EAAO,KAAK,EAAA,CAAG,UACvG,EAAO,KAAK,KAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DACX,IAAI,KAAK,EAAO,IAAI,CAAC,IAAI,EAAE,kBAAkB,KAEhD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,2DACZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,KAAK,cAC7B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,kBA7BZ,EAAO,EAAE,UAuC1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+BACZ,EAAsB,GAAG,CAAC,AAAC,GAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAiB,UAAU,eAC9B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uDACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,wCACpB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yEACX,EAAO,IAAI,CAAC,IAAI,GAEnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,qDACV,EAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAI,EAAO,IAAI,CAAC,IAAI,CAAC,IAAI,UAIzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,CAAC,yDAAyD,EAAE,EAAc,EAAO,KAAK,EAAA,CAAG,UACvG,EAAO,KAAK,QAKnB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,wDAA+C,WAC/D,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,6CAAoC,EAAO,aAAa,CAAC,IAAE,EAAO,IAAI,CAAC,QAAQ,OAE9F,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,wDAA+C,gBAC/D,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,2DAAkD,EAAO,UAAU,CAAC,UAEnF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,wDAA+C,UAC/D,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,4CAAoC,IAAI,KAAK,EAAO,IAAI,CAAC,IAAI,EAAE,kBAAkB,QAEhG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,KAAK,eAC7B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,iBAAiB,oBAInC,EAAO,OAAO,EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,wDAA+C,aAC/D,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,4CAAoC,EAAO,OAAO,aA3C9D,EAAO,EAAE,iBA0DxC"}