(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,t,a)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>l,"CardHeader",()=>n,"CardTitle",()=>i]);var t=e.i(53379),a=e.i(46686),r=e.i(36946);let s=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...n})});s.displayName="Card";let n=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...n})});n.displayName="CardHeader";let i=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("h3",{ref:a,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});i.displayName="CardTitle";let l=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("p",{ref:a,className:(0,r.cn)("text-sm text-gray-600 dark:text-gray-400",s),...n})});l.displayName="CardDescription";let d=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("p-6 pt-0",s),...n})});d.displayName="CardContent",a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>h],30151);var t=e.i(53379),a=e.i(46686);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return e=>{let a=!1,s=t.map(t=>{let s=r(t,e);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let e=0;e<s.length;e++){let a=s[e];"function"==typeof a?a():r(t[e],null)}}}}function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.useCallback(s(...t),t)}function i(e){let r=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){var i,l,d;let e,c,o=(c=(e=null==(l=Object.getOwnPropertyDescriptor((i=r).props,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(c=(e=null==(d=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,m=function(e,t){let a={...t};for(let r in t){let s=e[r],n=t[r];/^on[A-Z]/.test(r)?s&&n?a[r]=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];let r=n(...t);return s(...t),r}:s&&(a[r]=s):"style"===r?a[r]={...s,...n}:"className"===r&&(a[r]=[s,n].filter(Boolean).join(" "))}return{...e,...a}}(n,r.props);return r.type!==a.Fragment&&(m.ref=t?s(t,o):o),a.cloneElement(r,m)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=a.forwardRef((e,s)=>{let{children:n,...i}=e,l=a.Children.toArray(n),d=l.find(c);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,t.jsx)(r,{...i,ref:s,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,t.jsx)(r,{...i,ref:s,children:n})});return n.displayName="".concat(e,".Slot"),n}e.s(["Slot",()=>l,"createSlot",()=>i],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>n],35952);var l=i("Slot"),d=Symbol("radix.slottable");function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var o=e.i(94323),m=e.i(36946);let x=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),h=a.forwardRef((e,a)=>{let{className:r,variant:s,size:n,asChild:i=!1,...d}=e;return(0,t.jsx)(i?l:"button",{className:(0,m.cn)(x({variant:s,size:n,className:r})),ref:a,...d})});h.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(46686),a=e.i(50321),r=e.i(88338),s=e.i(53379),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,a)=>{let n=(0,r.createSlot)("Primitive.".concat(a)),i=t.forwardRef((e,t)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(r?n:a,{...i,ref:t})});return i.displayName="Primitive.".concat(a),{...e,[a]:i}},{});function i(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4741,e=>{"use strict";e.s(["default",()=>i],4741);var t=e.i(46686);let a=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},r=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,a)=>{let{color:n="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:m,...x}=e;return(0,t.createElement)("svg",{ref:a,...s,width:i,height:i,stroke:n,strokeWidth:d?24*Number(l)/Number(i):l,className:r("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(x)&&{"aria-hidden":"true"},...x},[...m.map(e=>{let[a,r]=e;return(0,t.createElement)(a,r)}),...Array.isArray(o)?o:[o]])}),i=(e,s)=>{let i=(0,t.forwardRef)((i,l)=>{let{className:d,...c}=i;return(0,t.createElement)(n,{ref:l,iconNode:s,className:r("lucide-".concat(a(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...c})});return i.displayName=a(e),i}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>s,"studentNavigation",()=>r,"teacherNavigation",()=>a]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"FileText"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],a=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],r=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function s(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},40280,e=>{"use strict";e.s(["Trash2",()=>t],40280);let t=(0,e.i(4741).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},36349,e=>{"use strict";e.s(["default",()=>x]);var t=e.i(53379),a=e.i(46686),r=e.i(32668),s=e.i(30151),n=e.i(89559),i=e.i(66505),l=e.i(35255),d=e.i(7418),c=e.i(40280),o=e.i(69556),m=e.i(84633);function x(){let[e,x]=(0,a.useState)("terms"),[h,u]=(0,a.useState)([]),[f,p]=(0,a.useState)([]),[g,y]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{u([{id:"1",name:"Term 1",startDate:"2024-09-01",endDate:"2024-12-15",academicYear:"2024-2025"},{id:"2",name:"Term 2",startDate:"2025-01-15",endDate:"2025-04-30",academicYear:"2024-2025"}]),p([{id:"1",name:"Unit Test 1",termName:"Term 1",subjectName:"Mathematics",maxMarks:50,weightagePercent:20,date:"2024-10-15"},{id:"2",name:"Mid Term Exam",termName:"Term 1",subjectName:"English",maxMarks:100,weightagePercent:40,date:"2024-11-20"}])},[]),(0,t.jsx)(n.default,{title:"Terms & Exams Management",navigation:m.adminNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex justify-between items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Terms & Exams"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage academic terms and examinations"})]})}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,t.jsxs)("button",{onClick:()=>x("terms"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("terms"===e?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,t.jsx)(i.Calendar,{className:"inline w-4 h-4 mr-2"}),"Academic Terms"]}),(0,t.jsxs)("button",{onClick:()=>x("exams"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("exams"===e?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,t.jsx)(o.BookOpen,{className:"inline w-4 h-4 mr-2"}),"Examinations"]})]})}),"terms"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Academic Terms"}),(0,t.jsxs)(s.Button,{onClick:()=>y(!0),children:[(0,t.jsx)(l.Plus,{className:"w-4 h-4 mr-2"}),"Add Term"]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:h.map(e=>(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{children:[(0,t.jsxs)(r.CardTitle,{className:"flex items-center",children:[(0,t.jsx)(i.Calendar,{className:"w-5 h-5 mr-2 text-blue-600"}),e.name]}),(0,t.jsx)(r.CardDescription,{children:e.academicYear})]}),(0,t.jsxs)(r.CardContent,{children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Start Date:"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:new Date(e.startDate).toLocaleDateString()})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"End Date:"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:new Date(e.endDate).toLocaleDateString()})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:(0,t.jsx)(d.Edit,{className:"w-4 h-4"})}),(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:(0,t.jsx)(c.Trash2,{className:"w-4 h-4"})})]})]})]},e.id))})]}),"exams"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Examinations"}),(0,t.jsxs)(s.Button,{onClick:()=>v(!0),children:[(0,t.jsx)(l.Plus,{className:"w-4 h-4 mr-2"}),"Add Exam"]})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg border",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Exam Name"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Term"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subject"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Max Marks"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Weightage"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:f.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.BookOpen,{className:"w-4 h-4 mr-2 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.name})]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.termName}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.subjectName}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.maxMarks}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.weightagePercent,"%"]}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(e.date).toLocaleDateString()}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:(0,t.jsx)(d.Edit,{className:"w-4 h-4"})}),(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:(0,t.jsx)(c.Trash2,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]})]})})}}]);