module.exports=[36870,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(54159),c=a.i(81453),d=a.i(54472),e=a.i(41825),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},81453,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactDOM},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},40770,a=>{"use strict";a.s(["Home",()=>b],40770);let b=(0,a.i(32639).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},32639,a=>{"use strict";a.s(["default",()=>g],32639);var b=a.i(54159);let c=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},d=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(0,b.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:f=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,b.createElement)("svg",{ref:l,...e,width:c,height:c,stroke:a,strokeWidth:g?24*Number(f)/Number(c):f,className:d("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,c])=>(0,b.createElement)(a,c)),...Array.isArray(i)?i:[i]])),g=(a,e)=>{let g=(0,b.forwardRef)(({className:g,...h},i)=>(0,b.createElement)(f,{ref:i,iconNode:e,className:d(`lucide-${c(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,g),...h}));return g.displayName=c(a),g}},59844,a=>{"use strict";a.s(["User",()=>b],59844);let b=(0,a.i(32639).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75422,a=>{"use strict";a.s(["Alert",()=>g,"AlertDescription",()=>h]);var b=a.i(41825),c=a.i(54159),d=a.i(24311),e=a.i(18688);let f=(0,d.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),g=c.forwardRef(({className:a,variant:c,...d},g)=>(0,b.jsx)("div",{ref:g,role:"alert",className:(0,e.cn)(f({variant:c}),a),...d}));g.displayName="Alert",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("h5",{ref:d,className:(0,e.cn)("mb-1 font-medium leading-none tracking-tight",a),...c})).displayName="AlertTitle";let h=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:(0,e.cn)("text-sm [&_p]:leading-relaxed",a),...c}));h.displayName="AlertDescription"},43626,a=>{"use strict";a.s(["Loader2",()=>b],43626);let b=(0,a.i(32639).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8653,a=>{"use strict";a.s(["XCircle",()=>b],8653);let b=(0,a.i(32639).default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},29683,a=>{"use strict";a.s(["CheckCircle",()=>b],29683);let b=(0,a.i(32639).default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},43001,a=>{"use strict";a.s(["AlertCircle",()=>b],43001);let b=(0,a.i(32639).default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},55371,a=>{"use strict";a.s(["ArrowLeft",()=>b],55371);let b=(0,a.i(32639).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},95347,a=>{"use strict";a.s(["BulkImport",()=>p]);var b=a.i(41825),c=a.i(54159),d=a.i(52963),e=a.i(2331),f=a.i(4082),g=a.i(75422),h=a.i(62867),i=a.i(14401),j=a.i(78402),k=a.i(29683),l=a.i(8653),m=a.i(43001),n=a.i(55371),o=a.i(43626);function p(){let a=(0,d.useRouter)(),p=(0,c.useRef)(null),[q,r]=(0,c.useState)(null),[s,t]=(0,c.useState)(!1),[u,v]=(0,c.useState)(null),[w,x]=(0,c.useState)(null),y=async()=>{if(!q)return void v("Please select a file first");t(!0),v(null),x(null);try{let a=new FormData;a.append("file",q);let b=await fetch("/api/admin/students/bulk",{method:"POST",body:a}),c=await b.json();if(!b.ok)throw Error(c.error||"Import failed");x(c.results)}catch(a){v(a instanceof Error?a.message:"Import failed")}finally{t(!1)}},z=()=>{r(null),v(null),x(null),p.current&&(p.current.value="")};return(0,b.jsxs)(f.Card,{className:"max-w-4xl mx-auto",children:[(0,b.jsx)(f.CardHeader,{children:(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsxs)(e.Button,{variant:"ghost",onClick:()=>a.push("/admin/students"),className:"p-0 h-auto",children:[(0,b.jsx)(n.ArrowLeft,{className:"w-4 h-4 mr-2"}),"Back to Students"]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(f.CardTitle,{children:"Bulk Import Students"}),(0,b.jsx)(f.CardDescription,{children:"Import multiple students from a CSV file"})]})]})}),(0,b.jsxs)(f.CardContent,{className:"space-y-6",children:[u&&(0,b.jsxs)(g.Alert,{variant:"destructive",children:[(0,b.jsx)(l.XCircle,{className:"w-4 h-4"}),(0,b.jsx)(g.AlertDescription,{children:u})]}),(0,b.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,b.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"Instructions:"}),(0,b.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,b.jsx)("li",{children:"• Download the template below to see the required format"}),(0,b.jsx)("li",{children:"• Required fields: First Name, Last Name, Email, Date of Birth, Gender, Admission Date, Class, Section"}),(0,b.jsx)("li",{children:"• Dates should be in YYYY-MM-DD format (e.g., 2010-05-15)"}),(0,b.jsx)("li",{children:"• Gender should be one of: MALE, FEMALE, OTHER"}),(0,b.jsx)("li",{children:"• Class and Section must match existing classes in the system"}),(0,b.jsx)("li",{children:"• Email addresses must be unique"})]})]}),(0,b.jsx)("div",{className:"flex justify-center",children:(0,b.jsxs)(e.Button,{variant:"outline",onClick:()=>{let a=new Blob([["First Name,Last Name,Email,Date of Birth,Gender,Phone Number,Address,Emergency Contact,Emergency Phone,Admission Date,Class,Section,Parent Name,Parent Phone,Parent Email",["John","Doe","<EMAIL>","2010-05-15","MALE","+1234567890","123 Main St, City, State","Jane Doe","+1234567891","2024-09-01","Grade 8","A","Jane Doe","+1234567891","<EMAIL>"].map(a=>`"${a}"`).join(",")].join("\n")],{type:"text/csv"}),b=window.URL.createObjectURL(a),c=document.createElement("a");c.href=b,c.download="student-import-template.csv",c.click(),window.URL.revokeObjectURL(b)},className:"flex items-center gap-2",children:[(0,b.jsx)(i.Download,{className:"w-4 h-4"}),"Download Template"]})}),(0,b.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-8 text-center ${q?"border-green-300 bg-green-50":"border-gray-300 hover:border-gray-400"}`,onDrop:a=>{a.preventDefault();let b=a.dataTransfer.files[0];if(b){if(!b.name.endsWith(".csv"))return void v("Please select a CSV file");r(b),v(null),x(null)}},onDragOver:a=>{a.preventDefault()},children:[(0,b.jsx)("input",{ref:p,type:"file",accept:".csv",onChange:a=>{let b=a.target.files?.[0];if(b){if(!b.name.endsWith(".csv"))return void v("Please select a CSV file");r(b),v(null),x(null)}},className:"hidden"}),q?(0,b.jsxs)("div",{children:[(0,b.jsx)(j.FileText,{className:"w-12 h-12 mx-auto text-green-600 mb-4"}),(0,b.jsxs)("p",{className:"text-lg font-medium text-green-700 mb-2",children:["File Selected: ",q.name]}),(0,b.jsxs)("p",{className:"text-sm text-green-600 mb-4",children:["Size: ",(q.size/1024).toFixed(1)," KB"]}),(0,b.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,b.jsxs)(e.Button,{onClick:y,disabled:s,children:[s?(0,b.jsx)(o.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}):(0,b.jsx)(h.Upload,{className:"w-4 h-4 mr-2"}),"Import Students"]}),(0,b.jsx)(e.Button,{variant:"outline",onClick:z,disabled:s,children:"Clear"})]})]}):(0,b.jsxs)("div",{children:[(0,b.jsx)(h.Upload,{className:"w-12 h-12 mx-auto text-gray-400 mb-4"}),(0,b.jsx)("p",{className:"text-lg font-medium text-gray-700 mb-2",children:"Drop your CSV file here, or click to browse"}),(0,b.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"Only CSV files are supported"}),(0,b.jsx)(e.Button,{variant:"outline",onClick:()=>p.current?.click(),children:"Choose File"})]})]}),w&&(0,b.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,b.jsx)("h3",{className:"font-medium text-lg mb-4",children:"Import Results"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:w.success}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:"Successfully Imported"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-red-600",children:w.errors.length}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:w.total}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:"Total Records"})]})]}),w.success>0&&(0,b.jsxs)(g.Alert,{className:"mb-4",children:[(0,b.jsx)(k.CheckCircle,{className:"w-4 h-4"}),(0,b.jsxs)(g.AlertDescription,{children:["Successfully imported ",w.success," students!"]})]}),w.errors.length>0&&(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium text-red-700 mb-2",children:"Errors:"}),(0,b.jsx)("div",{className:"max-h-40 overflow-y-auto space-y-1",children:w.errors.map((a,c)=>(0,b.jsxs)("div",{className:"text-sm text-red-600 flex items-start gap-2",children:[(0,b.jsx)(m.AlertCircle,{className:"w-4 h-4 mt-0.5 flex-shrink-0"}),(0,b.jsx)("span",{children:a})]},c))})]}),(0,b.jsxs)("div",{className:"mt-4 flex gap-2",children:[(0,b.jsx)(e.Button,{onClick:()=>a.push("/admin/students"),className:"flex-1",children:"View Students"}),(0,b.jsx)(e.Button,{variant:"outline",onClick:z,children:"Import Another File"})]})]})]})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__984cd889._.js.map