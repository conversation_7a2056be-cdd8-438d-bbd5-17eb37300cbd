{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\r\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\r\nimport bcrypt from 'bcryptjs'\r\nimport { prisma } from './db'\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null\r\n        }\r\n\r\n        try {\r\n          const user = await prisma.user.findUnique({\r\n            where: {\r\n              email: credentials.email\r\n            }\r\n          })\r\n\r\n          if (!user || !user.hashedPassword) {\r\n            return null\r\n          }\r\n\r\n          const isCorrectPassword = await bcrypt.compare(\r\n            credentials.password,\r\n            user.hashedPassword\r\n          )\r\n\r\n          if (!isCorrectPassword) {\r\n            return null\r\n          }\r\n\r\n          return {\r\n            id: user.id,\r\n            email: user.email,\r\n            name: `${user.firstName} ${user.lastName}`,\r\n            role: user.role,\r\n            firstName: user.firstName,\r\n            lastName: user.lastName\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth error:', error)\r\n          return null\r\n        }\r\n      }\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.role = user.role\r\n        token.firstName = user.firstName\r\n        token.lastName = user.lastName\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token) {\r\n        session.user.id = token.sub!\r\n        session.user.role = token.role as string\r\n        session.user.firstName = token.firstName as string\r\n        session.user.lastName = token.lastName as string\r\n      }\r\n      return session\r\n    }\r\n  },\r\n  pages: {\r\n    signIn: '/login',\r\n    error: '/login'\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,IAAA,mTAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,OAAO,YAAY,KAAK;wBAC1B;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,MAAM,oBAAoB,MAAM,qOAAM,CAAC,OAAO,CAC5C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,mBAAmB;wBACtB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/admin/dashboard/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/db'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user.role !== 'ADMIN') {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Get all the statistics in parallel for better performance\n    const [\n      totalStudents,\n      totalTeachers,\n      totalClasses,\n      totalSubjects,\n      attendanceStats,\n      marksStats,\n      activeStudents,\n      activeTeachers\n    ] = await Promise.all([\n      // Total students count\n      prisma.student.count(),\n      \n      // Total teachers count\n      prisma.teacher.count(),\n      \n      // Total classes count\n      prisma.class.count(),\n      \n      // Total subjects count\n      prisma.subject.count(),\n      \n      // Attendance statistics (last 30 days)\n      prisma.attendance.aggregate({\n        where: {\n          date: {\n            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n          }\n        },\n        _count: {\n          id: true\n        }\n      }).then(async (total) => {\n        const present = await prisma.attendance.count({\n          where: {\n            date: {\n              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n            },\n            status: 'PRESENT'\n          }\n        })\n        return {\n          total: total._count.id,\n          present,\n          rate: total._count.id > 0 ? (present / total._count.id) * 100 : 0\n        }\n      }),\n      \n      // Marks statistics (average marks)\n      prisma.mark.aggregate({\n        _avg: {\n          obtainedMarks: true\n        },\n        _count: {\n          id: true\n        }\n      }),\n      \n      // Active students (students with recent attendance)\n      prisma.student.count({\n        where: {\n          attendances: {\n            some: {\n              date: {\n                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n              }\n            }\n          }\n        }\n      }),\n\n      // Active teachers (teachers who took attendance recently)\n      prisma.teacher.count({\n        where: {\n          attendances: {\n            some: {\n              date: {\n                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n              }\n            }\n          }\n        }\n      })\n    ])\n\n    // Calculate additional statistics\n    const stats = {\n      totalStudents,\n      totalTeachers,\n      totalClasses,\n      totalSubjects,\n      activeStudents,\n      activeTeachers,\n      attendanceRate: Math.round(attendanceStats.rate * 10) / 10, // Round to 1 decimal\n      averageMarks: marksStats._avg.obtainedMarks \n        ? Math.round(marksStats._avg.obtainedMarks * 10) / 10 \n        : 0,\n      totalMarksRecords: marksStats._count.id,\n      totalAttendanceRecords: attendanceStats.total\n    }\n\n    return NextResponse.json(stats)\n  } catch (error) {\n    console.error('Error fetching admin dashboard stats:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,4DAA4D;QAC5D,MAAM,CACJ,eACA,eACA,cACA,eACA,iBACA,YACA,gBACA,eACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,uBAAuB;YACvB,8JAAM,CAAC,OAAO,CAAC,KAAK;YAEpB,uBAAuB;YACvB,8JAAM,CAAC,OAAO,CAAC,KAAK;YAEpB,sBAAsB;YACtB,8JAAM,CAAC,KAAK,CAAC,KAAK;YAElB,uBAAuB;YACvB,8JAAM,CAAC,OAAO,CAAC,KAAK;YAEpB,uCAAuC;YACvC,8JAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC1B,OAAO;oBACL,MAAM;wBACJ,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,eAAe;oBACtE;gBACF;gBACA,QAAQ;oBACN,IAAI;gBACN;YACF,GAAG,IAAI,CAAC,OAAO;gBACb,MAAM,UAAU,MAAM,8JAAM,CAAC,UAAU,CAAC,KAAK,CAAC;oBAC5C,OAAO;wBACL,MAAM;4BACJ,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;wBACjD;wBACA,QAAQ;oBACV;gBACF;gBACA,OAAO;oBACL,OAAO,MAAM,MAAM,CAAC,EAAE;oBACtB;oBACA,MAAM,MAAM,MAAM,CAAC,EAAE,GAAG,IAAI,AAAC,UAAU,MAAM,MAAM,CAAC,EAAE,GAAI,MAAM;gBAClE;YACF;YAEA,mCAAmC;YACnC,8JAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACpB,MAAM;oBACJ,eAAe;gBACjB;gBACA,QAAQ;oBACN,IAAI;gBACN;YACF;YAEA,oDAAoD;YACpD,8JAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACnB,OAAO;oBACL,aAAa;wBACX,MAAM;4BACJ,MAAM;gCACJ,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,eAAe;4BACtE;wBACF;oBACF;gBACF;YACF;YAEA,0DAA0D;YAC1D,8JAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACnB,OAAO;oBACL,aAAa;wBACX,MAAM;4BACJ,MAAM;gCACJ,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,eAAe;4BACtE;wBACF;oBACF;gBACF;YACF;SACD;QAED,kCAAkC;QAClC,MAAM,QAAQ;YACZ;YACA;YACA;YACA;YACA;YACA;YACA,gBAAgB,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,MAAM;YACxD,cAAc,WAAW,IAAI,CAAC,aAAa,GACvC,KAAK,KAAK,CAAC,WAAW,IAAI,CAAC,aAAa,GAAG,MAAM,KACjD;YACJ,mBAAmB,WAAW,MAAM,CAAC,EAAE;YACvC,wBAAwB,gBAAgB,KAAK;QAC/C;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}