(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,r,t)=>{r.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>n,"CardHeader",()=>l,"CardTitle",()=>i]);var r=e.i(53379),t=e.i(46686),a=e.i(36946);let s=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...l})});s.displayName="Card";let l=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...l})});l.displayName="CardHeader";let i=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...l})});i.displayName="CardTitle";let n=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",s),...l})});n.displayName="CardDescription";let d=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",s),...l})});d.displayName="CardContent",t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>m],30151);var r=e.i(53379),t=e.i(46686);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e=>{let t=!1,s=r.map(r=>{let s=a(r,e);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let e=0;e<s.length;e++){let t=s[e];"function"==typeof t?t():a(r[e],null)}}}}function l(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return t.useCallback(s(...r),r)}function i(e){let a=function(e){let r=t.forwardRef((e,r)=>{let{children:a,...l}=e;if(t.isValidElement(a)){var i,n,d;let e,c,o=(c=(e=null==(n=Object.getOwnPropertyDescriptor((i=a).props,"ref"))?void 0:n.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(c=(e=null==(d=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,x=function(e,r){let t={...r};for(let a in r){let s=e[a],l=r[a];/^on[A-Z]/.test(a)?s&&l?t[a]=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];let a=l(...r);return s(...r),a}:s&&(t[a]=s):"style"===a?t[a]={...s,...l}:"className"===a&&(t[a]=[s,l].filter(Boolean).join(" "))}return{...e,...t}}(l,a.props);return a.type!==t.Fragment&&(x.ref=r?s(r,o):o),t.cloneElement(a,x)}return t.Children.count(a)>1?t.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),l=t.forwardRef((e,s)=>{let{children:l,...i}=e,n=t.Children.toArray(l),d=n.find(c);if(d){let e=d.props.children,l=n.map(r=>r!==d?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,r.jsx)(a,{...i,ref:s,children:t.isValidElement(e)?t.cloneElement(e,void 0,l):null})}return(0,r.jsx)(a,{...i,ref:s,children:l})});return l.displayName="".concat(e,".Slot"),l}e.s(["Slot",()=>n,"createSlot",()=>i],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>l],35952);var n=i("Slot"),d=Symbol("radix.slottable");function c(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var o=e.i(94323),x=e.i(36946);let g=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=t.forwardRef((e,t)=>{let{className:a,variant:s,size:l,asChild:i=!1,...d}=e;return(0,r.jsx)(i?n:"button",{className:(0,x.cn)(g({variant:s,size:l,className:a})),ref:t,...d})});m.displayName="Button"},70307,e=>{"use strict";e.s(["Alert",()=>i,"AlertDescription",()=>n]);var r=e.i(53379),t=e.i(46686),a=e.i(94323),s=e.i(36946);let l=(0,a.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),i=t.forwardRef((e,t)=>{let{className:a,variant:i,...n}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,s.cn)(l({variant:i}),a),...n})});i.displayName="Alert",t.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("h5",{ref:t,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",a),...l})}).displayName="AlertTitle";let n=t.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",a),...l})});n.displayName="AlertDescription"},90285,e=>{"use strict";e.s(["Badge",()=>l]);var r=e.i(53379),t=e.i(94323),a=e.i(36946);let s=(0,t.cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",secondary:"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:l,...i}=e;return(0,r.jsx)("div",{className:(0,a.cn)(s({variant:l}),t),...i})}},27425,e=>{"use strict";e.s(["default",()=>d]);var r=e.i(53379),t=e.i(46686),a=e.i(18125),s=e.i(30151),l=e.i(32668),i=e.i(90285),n=e.i(70307);function d(e){let{params:d}=e,c=(0,a.useRouter)(),[o,x]=(0,t.useState)(null),[g,m]=(0,t.useState)(!0),[u,h]=(0,t.useState)(""),[f,p]=(0,t.useState)(null);(0,t.useEffect)(()=>{(async()=>{try{let e=await d;p(e.id)}catch(e){h("Failed to resolve route parameters"),m(!1)}})()},[d]),(0,t.useEffect)(()=>{f&&(async()=>{try{let e=await fetch("/api/admin/teachers/".concat(f));if(!e.ok)throw Error("Failed to fetch teacher");let r=await e.json();x(r.teacher)}catch(e){h(e.message)}finally{m(!1)}})()},[f]);let y=e=>new Date(e).toLocaleDateString();return g?(0,r.jsx)("div",{className:"flex justify-center p-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):u?(0,r.jsx)(n.Alert,{variant:"destructive",children:(0,r.jsx)(n.AlertDescription,{children:u})}):o?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:[o.firstName," ",o.lastName]}),(0,r.jsx)("p",{className:"text-gray-600",children:o.email})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(s.Button,{variant:"outline",onClick:()=>c.push("/admin/teachers/".concat(o.id,"/edit")),children:"Edit Teacher"}),(0,r.jsx)(s.Button,{onClick:()=>c.push("/admin/teachers"),children:"Back to List"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)(l.Card,{children:[(0,r.jsx)(l.CardHeader,{children:(0,r.jsx)(l.CardTitle,{children:"Personal Information"})}),(0,r.jsxs)(l.CardContent,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),(0,r.jsxs)("p",{className:"text-lg",children:[o.firstName," ",o.lastName]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,r.jsx)("p",{className:"text-lg",children:o.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Phone"}),(0,r.jsx)("p",{className:"text-lg",children:o.phone||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Gender"}),(0,r.jsx)("p",{className:"text-lg",children:o.gender?(e=>{switch(e){case"MALE":return"Male";case"FEMALE":return"Female";case"OTHER":return"Other";default:return"Not specified"}})(o.gender):"Not specified"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Date of Birth"}),(0,r.jsx)("p",{className:"text-lg",children:o.dateOfBirth?y(o.dateOfBirth):"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Address"}),(0,r.jsx)("p",{className:"text-lg",children:o.address||"Not provided"})]})]})]}),(0,r.jsxs)(l.Card,{children:[(0,r.jsx)(l.CardHeader,{children:(0,r.jsx)(l.CardTitle,{children:"Professional Information"})}),(0,r.jsxs)(l.CardContent,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Qualification"}),(0,r.jsx)("p",{className:"text-lg",children:o.qualification||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Experience"}),(0,r.jsx)("p",{className:"text-lg",children:o.experience?"".concat(o.experience," years"):"Not specified"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Joining Date"}),(0,r.jsx)("p",{className:"text-lg",children:o.joiningDate?y(o.joiningDate):"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Salary"}),(0,r.jsx)("p",{className:"text-lg",children:o.salary?"$".concat(o.salary.toLocaleString()):"Not specified"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Status"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(i.Badge,{variant:o.isActive?"default":"secondary",children:o.isActive?"Active":"Inactive"})})]})]})]}),(0,r.jsxs)(l.Card,{children:[(0,r.jsx)(l.CardHeader,{children:(0,r.jsx)(l.CardTitle,{children:"Account Information"})}),(0,r.jsxs)(l.CardContent,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"User ID"}),(0,r.jsx)("p",{className:"text-lg",children:o.user.id})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Role"}),(0,r.jsx)("p",{className:"text-lg",children:o.user.role})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Created"}),(0,r.jsx)("p",{className:"text-lg",children:y(o.createdAt)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),(0,r.jsx)("p",{className:"text-lg",children:y(o.updatedAt)})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(l.Card,{children:[(0,r.jsx)(l.CardHeader,{children:(0,r.jsx)(l.CardTitle,{children:"Assigned Classes"})}),(0,r.jsx)(l.CardContent,{children:o.classes&&o.classes.length>0?(0,r.jsx)("div",{className:"space-y-2",children:o.classes.map(e=>(0,r.jsxs)(i.Badge,{variant:"secondary",className:"mr-2 mb-2",children:[e.name," ",e.section.name]},e.id))}):(0,r.jsx)("p",{className:"text-gray-500",children:"No classes assigned"})})]}),(0,r.jsxs)(l.Card,{children:[(0,r.jsx)(l.CardHeader,{children:(0,r.jsx)(l.CardTitle,{children:"Teaching Subjects"})}),(0,r.jsx)(l.CardContent,{children:o.subjects&&o.subjects.length>0?(0,r.jsx)("div",{className:"space-y-2",children:o.subjects.map(e=>(0,r.jsx)(i.Badge,{variant:"outline",className:"mr-2 mb-2",children:e.name},e.id))}):(0,r.jsx)("p",{className:"text-gray-500",children:"No subjects assigned"})})]})]})]}):(0,r.jsx)(n.Alert,{children:(0,r.jsx)(n.AlertDescription,{children:"Teacher not found"})})}}]);