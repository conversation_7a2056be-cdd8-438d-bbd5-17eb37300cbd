{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yQAAO,EAAC,IAAA,mOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kVAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4TAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4TAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4TAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4TAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QAAI,KAAK;QAAK,WAAW,IAAA,8JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4TAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport Link from 'next/link'\n\nexport default function HomePage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === 'authenticated' && session?.user?.role) {\n      if (session.user.role === 'ADMIN') {\n        router.push('/admin')\n      } else if (session.user.role === 'TEACHER') {\n        router.push('/teacher')\n      } else if (session.user.role === 'STUDENT') {\n        router.push('/student')\n      }\n    }\n  }, [session, status, router])\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-lg\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-950 p-4\">\n      <Card className=\"w-full max-w-md mx-auto\">\n        <CardHeader className=\"text-center px-4 sm:px-6\">\n          <CardTitle className=\"text-2xl sm:text-3xl font-bold leading-tight\">\n            Welcome to School Management System\n          </CardTitle>\n          <CardDescription className=\"text-sm sm:text-base mt-2\">\n            A comprehensive platform for managing school operations\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4 px-4 sm:px-6\">\n          <div className=\"text-center\">\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4 text-sm sm:text-base\">\n              Please sign in to access your dashboard\n            </p>\n            <Link href=\"/login\">\n              <Button className=\"w-full min-h-[44px] text-base\">\n                Sign In\n              </Button>\n            </Link>\n          </div>\n          \n          <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n            <h3 className=\"font-semibold mb-3 text-sm sm:text-base\">Demo Accounts:</h3>\n            <div className=\"text-xs sm:text-sm space-y-2\">\n              <div className=\"flex flex-col sm:flex-row sm:justify-between\">\n                <strong className=\"text-blue-600\">Admin:</strong>\n                <span className=\"text-gray-600 dark:text-gray-400\"><EMAIL> / Admin@12345</span>\n              </div>\n              <div className=\"flex flex-col sm:flex-row sm:justify-between\">\n                <strong className=\"text-green-600\">Teacher:</strong>\n                <span className=\"text-gray-600 dark:text-gray-400\"><EMAIL> / Teacher@12345</span>\n              </div>\n              <div className=\"flex flex-col sm:flex-row sm:justify-between\">\n                <strong className=\"text-purple-600\">Student:</strong>\n                <span className=\"text-gray-600 dark:text-gray-400\"><EMAIL> / Student@12345</span>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,6SAAU;IAC5C,MAAM,SAAS,IAAA,mSAAS;IAExB,IAAA,2TAAS;8BAAC;gBAC0B;YAAlC,IAAI,WAAW,oBAAmB,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI,GAAE;gBACrD,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;oBACjC,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW;oBAC1C,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW;oBAC1C,OAAO,IAAI,CAAC;gBACd;YACF;QACF;6BAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,IAAI,WAAW,WAAW;QACxB,qBACE,8UAAC;YAAI,WAAU;sBACb,cAAA,8UAAC;gBAAI,WAAU;;kCACb,8UAAC;wBAAI,WAAU;;;;;;kCACf,8UAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;;;;;;IAIpC;IAEA,qBACE,8UAAC;QAAI,WAAU;kBACb,cAAA,8UAAC,6KAAI;YAAC,WAAU;;8BACd,8UAAC,mLAAU;oBAAC,WAAU;;sCACpB,8UAAC,kLAAS;4BAAC,WAAU;sCAA+C;;;;;;sCAGpE,8UAAC,wLAAe;4BAAC,WAAU;sCAA4B;;;;;;;;;;;;8BAIzD,8UAAC,oLAAW;oBAAC,WAAU;;sCACrB,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAE,WAAU;8CAA6D;;;;;;8CAG1E,8UAAC,2TAAI;oCAAC,MAAK;8CACT,cAAA,8UAAC,iLAAM;wCAAC,WAAU;kDAAgC;;;;;;;;;;;;;;;;;sCAMtD,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAO,WAAU;8DAAgB;;;;;;8DAClC,8UAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAO,WAAU;8DAAiB;;;;;;8DACnC,8UAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAO,WAAU;8DAAkB;;;;;;8DACpC,8UAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnE;GAvEwB;;QACY,6SAAU;QAC7B,mSAAS;;;KAFF", "debugId": null}}]}