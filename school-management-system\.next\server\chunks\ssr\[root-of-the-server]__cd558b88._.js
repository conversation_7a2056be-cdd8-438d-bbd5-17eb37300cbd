module.exports=[18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},59230,(a,b,c)=>{"use strict";b.exports=a.r(18622)},41825,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactJsxRuntime},54159,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].React},40140,a=>{"use strict";a.s(["ThemeProvider",()=>f,"useTheme",()=>e]);var b=a.i(41825),c=a.i(54159);let d=(0,c.createContext)(void 0);function e(){let a=(0,c.useContext)(d);return a||{theme:"system",setTheme:()=>{},actualTheme:"light",mounted:!1}}function f({children:a}){let[e,f]=(0,c.useState)("system"),[g,h]=(0,c.useState)("light"),[i,j]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{j(!0);let a=localStorage.getItem("theme");a&&["light","dark","system"].includes(a)?f(a):f("light")},[]),(0,c.useEffect)(()=>{let a;if(console.log("ThemeProvider: theme effect triggered",{theme:e,mounted:i}),!i)return;let b=window.document.documentElement;b.classList.remove("light","dark"),a="system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e,console.log("ThemeProvider: applying theme",{theme:e,effectiveTheme:a}),b.classList.add(a),h(a),console.log("ThemeProvider: DOM classes after applying theme:",b.className),localStorage.setItem("theme",e)},[e,i]),(0,c.useEffect)(()=>{if(i&&"system"===e){let a=window.matchMedia("(prefers-color-scheme: dark)"),b=a=>{let b=window.document.documentElement;b.classList.remove("light","dark");let c=a.matches?"dark":"light";b.classList.add(c),h(c)};return a.addEventListener("change",b),()=>a.removeEventListener("change",b)}},[e,i]),(0,b.jsx)(d.Provider,{value:{theme:e,setTheme:a=>{console.log("ThemeProvider: setTheme called with:",a),f(a)},actualTheme:g,mounted:i},children:a})}},60198,a=>{"use strict";a.s(["default",()=>e]);var b=a.i(41825),c=a.i(25384),d=a.i(40140);function e({children:a}){return(0,b.jsx)(c.SessionProvider,{children:(0,b.jsx)(d.ThemeProvider,{children:a})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__cd558b88._.js.map