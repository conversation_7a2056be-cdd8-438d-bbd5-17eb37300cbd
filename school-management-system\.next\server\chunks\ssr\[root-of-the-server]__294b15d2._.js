module.exports=[56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},75422,a=>{"use strict";a.s(["Alert",()=>g,"AlertDescription",()=>h]);var b=a.i(41825),c=a.i(54159),d=a.i(24311),e=a.i(18688);let f=(0,d.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),g=c.forwardRef(({className:a,variant:c,...d},g)=>(0,b.jsx)("div",{ref:g,role:"alert",className:(0,e.cn)(f({variant:c}),a),...d}));g.displayName="Alert",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("h5",{ref:d,className:(0,e.cn)("mb-1 font-medium leading-none tracking-tight",a),...c})).displayName="AlertTitle";let h=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:(0,e.cn)("text-sm [&_p]:leading-relaxed",a),...c}));h.displayName="AlertDescription"},12336,a=>{"use strict";a.s(["Badge",()=>f]);var b=a.i(41825),c=a.i(24311),d=a.i(18688);let e=(0,c.cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",secondary:"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"}},defaultVariants:{variant:"default"}});function f({className:a,variant:c,...f}){return(0,b.jsx)("div",{className:(0,d.cn)(e({variant:c}),a),...f})}},19102,a=>{"use strict";a.s(["default",()=>i]);var b=a.i(41825),c=a.i(54159),d=a.i(52963),e=a.i(2331),f=a.i(4082),g=a.i(12336),h=a.i(75422);function i({params:a}){let i=(0,d.useRouter)(),[j,k]=(0,c.useState)(null),[l,m]=(0,c.useState)(!0),[n,o]=(0,c.useState)(""),[p,q]=(0,c.useState)(null);(0,c.useEffect)(()=>{(async()=>{try{let b=await a;q(b.id)}catch(a){o("Failed to resolve route parameters"),m(!1)}})()},[a]),(0,c.useEffect)(()=>{p&&(async()=>{try{let a=await fetch(`/api/admin/teachers/${p}`);if(!a.ok)throw Error("Failed to fetch teacher");let b=await a.json();k(b.teacher)}catch(a){o(a.message)}finally{m(!1)}})()},[p]);let r=a=>new Date(a).toLocaleDateString();return l?(0,b.jsx)("div",{className:"flex justify-center p-8",children:(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):n?(0,b.jsx)(h.Alert,{variant:"destructive",children:(0,b.jsx)(h.AlertDescription,{children:n})}):j?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)("h1",{className:"text-3xl font-bold",children:[j.firstName," ",j.lastName]}),(0,b.jsx)("p",{className:"text-gray-600",children:j.email})]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(e.Button,{variant:"outline",onClick:()=>i.push(`/admin/teachers/${j.id}/edit`),children:"Edit Teacher"}),(0,b.jsx)(e.Button,{onClick:()=>i.push("/admin/teachers"),children:"Back to List"})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,b.jsxs)(f.Card,{children:[(0,b.jsx)(f.CardHeader,{children:(0,b.jsx)(f.CardTitle,{children:"Personal Information"})}),(0,b.jsxs)(f.CardContent,{className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),(0,b.jsxs)("p",{className:"text-lg",children:[j.firstName," ",j.lastName]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,b.jsx)("p",{className:"text-lg",children:j.email})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Phone"}),(0,b.jsx)("p",{className:"text-lg",children:j.phone||"Not provided"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Gender"}),(0,b.jsx)("p",{className:"text-lg",children:j.gender?(a=>{switch(a){case"MALE":return"Male";case"FEMALE":return"Female";case"OTHER":return"Other";default:return"Not specified"}})(j.gender):"Not specified"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Date of Birth"}),(0,b.jsx)("p",{className:"text-lg",children:j.dateOfBirth?r(j.dateOfBirth):"Not provided"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Address"}),(0,b.jsx)("p",{className:"text-lg",children:j.address||"Not provided"})]})]})]}),(0,b.jsxs)(f.Card,{children:[(0,b.jsx)(f.CardHeader,{children:(0,b.jsx)(f.CardTitle,{children:"Professional Information"})}),(0,b.jsxs)(f.CardContent,{className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Qualification"}),(0,b.jsx)("p",{className:"text-lg",children:j.qualification||"Not provided"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Experience"}),(0,b.jsx)("p",{className:"text-lg",children:j.experience?`${j.experience} years`:"Not specified"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Joining Date"}),(0,b.jsx)("p",{className:"text-lg",children:j.joiningDate?r(j.joiningDate):"Not provided"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Salary"}),(0,b.jsx)("p",{className:"text-lg",children:j.salary?`$${j.salary.toLocaleString()}`:"Not specified"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Status"}),(0,b.jsx)("div",{className:"mt-1",children:(0,b.jsx)(g.Badge,{variant:j.isActive?"default":"secondary",children:j.isActive?"Active":"Inactive"})})]})]})]}),(0,b.jsxs)(f.Card,{children:[(0,b.jsx)(f.CardHeader,{children:(0,b.jsx)(f.CardTitle,{children:"Account Information"})}),(0,b.jsxs)(f.CardContent,{className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"User ID"}),(0,b.jsx)("p",{className:"text-lg",children:j.user.id})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Role"}),(0,b.jsx)("p",{className:"text-lg",children:j.user.role})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Created"}),(0,b.jsx)("p",{className:"text-lg",children:r(j.createdAt)})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),(0,b.jsx)("p",{className:"text-lg",children:r(j.updatedAt)})]})]})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,b.jsxs)(f.Card,{children:[(0,b.jsx)(f.CardHeader,{children:(0,b.jsx)(f.CardTitle,{children:"Assigned Classes"})}),(0,b.jsx)(f.CardContent,{children:j.classes&&j.classes.length>0?(0,b.jsx)("div",{className:"space-y-2",children:j.classes.map(a=>(0,b.jsxs)(g.Badge,{variant:"secondary",className:"mr-2 mb-2",children:[a.name," ",a.section.name]},a.id))}):(0,b.jsx)("p",{className:"text-gray-500",children:"No classes assigned"})})]}),(0,b.jsxs)(f.Card,{children:[(0,b.jsx)(f.CardHeader,{children:(0,b.jsx)(f.CardTitle,{children:"Teaching Subjects"})}),(0,b.jsx)(f.CardContent,{children:j.subjects&&j.subjects.length>0?(0,b.jsx)("div",{className:"space-y-2",children:j.subjects.map(a=>(0,b.jsx)(g.Badge,{variant:"outline",className:"mr-2 mb-2",children:a.name},a.id))}):(0,b.jsx)("p",{className:"text-gray-500",children:"No subjects assigned"})})]})]})]}):(0,b.jsx)(h.Alert,{children:(0,b.jsx)(h.AlertDescription,{children:"Teacher not found"})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__294b15d2._.js.map