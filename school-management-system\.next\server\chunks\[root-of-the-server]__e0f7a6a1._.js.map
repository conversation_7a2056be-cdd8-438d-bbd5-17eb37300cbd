{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/admin/classes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/db';\nimport { hasPermission } from '@/lib/rbac';\nimport { z } from 'zod';\n\n// Validation schema for class data\nconst ClassSchema = z.object({\n  name: z.string().min(1, 'Class name is required'),\n  sectionId: z.number().min(1, 'Section is required'),\n  teacherId: z.number().optional(),\n  capacity: z.number().min(1, 'Capacity must be at least 1').optional(),\n  academicYear: z.string().optional(),\n  isActive: z.boolean().default(true),\n});\n\n// GET /api/admin/classes - List all classes\nexport async function GET(request: NextRequest) {\n  try {\n    // const session = await getServerSession(authOptions);\n    // if (!session?.user || !hasPermission(session.user.role, 'classes:read')) {\n    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    // }\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const search = searchParams.get('search') || '';\n    const isActive = searchParams.get('isActive');\n\n    // Fetch all classes with sections and student counts\n    const classes = await prisma.class.findMany({\n      include: {\n        sections: true,\n        _count: {\n          select: {\n            students: true,\n          },\n        },\n      },\n      orderBy: { name: 'asc' },\n    });\n    \n    // Transform classes to match frontend expectations\n    // Each class-section combination becomes a separate \"class\" entry\n    const transformedClasses = classes.flatMap(classItem =>\n      classItem.sections.map(section => ({\n        id: parseInt(section.id), // Convert string ID to number for frontend\n        name: `${classItem.name} - Section ${section.name}`,\n        capacity: 30, // Default capacity\n        academicYear: '2024-2025',\n        isActive: true,\n        section: {\n          id: parseInt(section.id),\n          name: section.name,\n        },\n        teacher: null, // No teacher assigned yet\n        _count: {\n          students: Math.floor(Math.random() * 25) + 5, // Random student count for now\n        },\n      }))\n    );\n\n    // Apply filters\n    let filteredClasses = transformedClasses;\n\n    if (search) {\n      filteredClasses = transformedClasses.filter(classItem =>\n        classItem.name.toLowerCase().includes(search.toLowerCase())\n      );\n    }\n\n    // Apply pagination\n    const total = filteredClasses.length;\n    const totalPages = Math.ceil(total / limit);\n\n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n    const paginatedClasses = filteredClasses.slice(startIndex, endIndex);\n\n    return NextResponse.json({\n      classes: paginatedClasses,\n      pagination: {\n        page,\n        limit,\n        total,\n        totalPages,\n      },\n    });\n  } catch (error) {\n    console.error('Error fetching classes:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch classes' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/admin/classes - Create new class\nexport async function POST(request: NextRequest) {\n  try {\n    // const session = await getServerSession(authOptions);\n    // if (!session?.user || !hasPermission(session.user.role, 'classes:write')) {\n    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    // }\n\n    const body = await request.json();\n    const validatedData = ClassSchema.parse(body);\n\n    // Check if section exists\n    const section = await prisma.section.findUnique({\n      where: { id: validatedData.sectionId },\n    });\n\n    if (!section) {\n      return NextResponse.json(\n        { error: 'Section not found' },\n        { status: 400 }\n      );\n    }\n\n    // Check if class with same name and section already exists\n    const existingClass = await prisma.class.findFirst({\n      where: {\n        name: validatedData.name,\n        sectionId: validatedData.sectionId,\n      },\n    });\n\n    if (existingClass) {\n      return NextResponse.json(\n        { error: 'Class with this name already exists in the selected section' },\n        { status: 400 }\n      );\n    }\n\n    // Check if teacher exists (if provided)\n    if (validatedData.teacherId) {\n      const teacher = await prisma.teacher.findUnique({\n        where: { id: validatedData.teacherId },\n      });\n\n      if (!teacher) {\n        return NextResponse.json(\n          { error: 'Teacher not found' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Create class\n    const newClass = await prisma.class.create({\n      data: validatedData,\n      include: {\n        section: true,\n        teacher: {\n          select: {\n            id: true,\n            firstName: true,\n            lastName: true,\n            email: true,\n          },\n        },\n      },\n    });\n\n    return NextResponse.json(\n      { \n        message: 'Class created successfully',\n        class: newClass,\n      },\n      { status: 201 }\n    );\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      );\n    }\n    console.error('Error creating class:', error);\n    return NextResponse.json(\n      { error: 'Failed to create class' },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/admin/classes - Update class\nexport async function PUT(request: NextRequest) {\n  try {\n    // const session = await getServerSession(authOptions);\n    // if (!session?.user || !hasPermission(session.user.role, 'classes:write')) {\n    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    // }\n\n    const body = await request.json();\n    const { id, ...updateData } = body;\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'Class ID is required' },\n        { status: 400 }\n      );\n    }\n\n    const validatedData = ClassSchema.partial().parse(updateData);\n\n    // Check if class exists\n    const existingClass = await prisma.class.findUnique({\n      where: { id: parseInt(id) },\n    });\n\n    if (!existingClass) {\n      return NextResponse.json(\n        { error: 'Class not found' },\n        { status: 404 }\n      );\n    }\n\n    // Check if section exists (if being updated)\n    if (validatedData.sectionId) {\n      const section = await prisma.section.findUnique({\n        where: { id: validatedData.sectionId },\n      });\n\n      if (!section) {\n        return NextResponse.json(\n          { error: 'Section not found' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Check if teacher exists (if being updated)\n    if (validatedData.teacherId) {\n      const teacher = await prisma.teacher.findUnique({\n        where: { id: validatedData.teacherId },\n      });\n\n      if (!teacher) {\n        return NextResponse.json(\n          { error: 'Teacher not found' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Update class\n    const updatedClass = await prisma.class.update({\n      where: { id: parseInt(id) },\n      data: validatedData,\n      include: {\n        section: true,\n        teacher: {\n          select: {\n            id: true,\n            firstName: true,\n            lastName: true,\n            email: true,\n          },\n        },\n      },\n    });\n\n    return NextResponse.json({\n      message: 'Class updated successfully',\n      class: updatedClass,\n    });\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      );\n    }\n    console.error('Error updating class:', error);\n    return NextResponse.json(\n      { error: 'Failed to update class' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAGA;AAEA;;;;AAEA,mCAAmC;AACnC,MAAM,cAAc,sQAAC,CAAC,MAAM,CAAC;IAC3B,MAAM,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,WAAW,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,WAAW,sQAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,UAAU,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,+BAA+B,QAAQ;IACnE,cAAc,sQAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,UAAU,sQAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,uDAAuD;QACvD,6EAA6E;QAC7E,0EAA0E;QAC1E,IAAI;QAEJ,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,WAAW,aAAa,GAAG,CAAC;QAElC,qDAAqD;QACrD,MAAM,UAAU,MAAM,8JAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC1C,SAAS;gBACP,UAAU;gBACV,QAAQ;oBACN,QAAQ;wBACN,UAAU;oBACZ;gBACF;YACF;YACA,SAAS;gBAAE,MAAM;YAAM;QACzB;QAEA,mDAAmD;QACnD,kEAAkE;QAClE,MAAM,qBAAqB,QAAQ,OAAO,CAAC,CAAA,YACzC,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBACjC,IAAI,SAAS,QAAQ,EAAE;oBACvB,MAAM,GAAG,UAAU,IAAI,CAAC,WAAW,EAAE,QAAQ,IAAI,EAAE;oBACnD,UAAU;oBACV,cAAc;oBACd,UAAU;oBACV,SAAS;wBACP,IAAI,SAAS,QAAQ,EAAE;wBACvB,MAAM,QAAQ,IAAI;oBACpB;oBACA,SAAS;oBACT,QAAQ;wBACN,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBAC7C;gBACF,CAAC;QAGH,gBAAgB;QAChB,IAAI,kBAAkB;QAEtB,IAAI,QAAQ;YACV,kBAAkB,mBAAmB,MAAM,CAAC,CAAA,YAC1C,UAAU,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW;QAE5D;QAEA,mBAAmB;QACnB,MAAM,QAAQ,gBAAgB,MAAM;QACpC,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;QAErC,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,mBAAmB,gBAAgB,KAAK,CAAC,YAAY;QAE3D,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,YAAY;gBACV;gBACA;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,uDAAuD;QACvD,8EAA8E;QAC9E,0EAA0E;QAC1E,IAAI;QAEJ,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,YAAY,KAAK,CAAC;QAExC,0BAA0B;QAC1B,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI,cAAc,SAAS;YAAC;QACvC;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,2DAA2D;QAC3D,MAAM,gBAAgB,MAAM,8JAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YACjD,OAAO;gBACL,MAAM,cAAc,IAAI;gBACxB,WAAW,cAAc,SAAS;YACpC;QACF;QAEA,IAAI,eAAe;YACjB,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8D,GACvE;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,IAAI,cAAc,SAAS,EAAE;YAC3B,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI,cAAc,SAAS;gBAAC;YACvC;YAEA,IAAI,CAAC,SAAS;gBACZ,OAAO,iSAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,eAAe;QACf,MAAM,WAAW,MAAM,8JAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACzC,MAAM;YACN,SAAS;gBACP,SAAS;gBACT,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,WAAW;wBACX,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF;QAEA,OAAO,iSAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,sQAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QACA,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,uDAAuD;QACvD,8EAA8E;QAC9E,0EAA0E;QAC1E,IAAI;QAEJ,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAE9B,IAAI,CAAC,IAAI;YACP,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gBAAgB,YAAY,OAAO,GAAG,KAAK,CAAC;QAElD,wBAAwB;QACxB,MAAM,gBAAgB,MAAM,8JAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAClD,OAAO;gBAAE,IAAI,SAAS;YAAI;QAC5B;QAEA,IAAI,CAAC,eAAe;YAClB,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,6CAA6C;QAC7C,IAAI,cAAc,SAAS,EAAE;YAC3B,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI,cAAc,SAAS;gBAAC;YACvC;YAEA,IAAI,CAAC,SAAS;gBACZ,OAAO,iSAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,6CAA6C;QAC7C,IAAI,cAAc,SAAS,EAAE;YAC3B,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI,cAAc,SAAS;gBAAC;YACvC;YAEA,IAAI,CAAC,SAAS;gBACZ,OAAO,iSAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,eAAe;QACf,MAAM,eAAe,MAAM,8JAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YAC7C,OAAO;gBAAE,IAAI,SAAS;YAAI;YAC1B,MAAM;YACN,SAAS;gBACP,SAAS;gBACT,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,WAAW;wBACX,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,sQAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QACA,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}