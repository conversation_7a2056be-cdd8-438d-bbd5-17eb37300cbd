module.exports=[62800,a=>{a.n(a.i(9240))},65333,a=>{"use strict";a.s(["ClientPageRoot",()=>Q.ClientPageRoot,"ClientSegmentRoot",()=>Q.ClientSegmentRoot,"GlobalError",()=>P.default,"HTTPAccessFallbackBoundary",()=>Q.HTTPAccessFallbackBoundary,"LayoutRouter",()=>Q.LayoutRouter,"MetadataBoundary",()=>Q.MetadataBoundary,"OutletBoundary",()=>Q.OutletBoundary,"Postpone",()=>Q.Postpone,"RenderFromTemplateContext",()=>Q.RenderFromTemplateContext,"RootLayoutBoundary",()=>Q.RootLayoutBoundary,"SegmentViewNode",()=>Q.SegmentViewNode,"SegmentViewStateNode",()=>Q.SegmentViewStateNode,"ViewportBoundary",()=>Q.ViewportBoundary,"__next_app__",()=>M,"actionAsyncStorage",()=>Q.actionAsyncStorage,"captureOwnerStack",()=>Q.captureOwnerStack,"collectSegmentData",()=>Q.collectSegmentData,"createMetadataComponents",()=>Q.createMetadataComponents,"createPrerenderParamsForClientSegment",()=>Q.createPrerenderParamsForClientSegment,"createPrerenderSearchParamsForClientPage",()=>Q.createPrerenderSearchParamsForClientPage,"createServerParamsForServerSegment",()=>Q.createServerParamsForServerSegment,"createServerSearchParamsForServerPage",()=>Q.createServerSearchParamsForServerPage,"createTemporaryReferenceSet",()=>Q.createTemporaryReferenceSet,"decodeAction",()=>Q.decodeAction,"decodeFormState",()=>Q.decodeFormState,"decodeReply",()=>Q.decodeReply,"handler",()=>O,"pages",()=>L,"patchFetch",()=>Q.patchFetch,"preconnect",()=>Q.preconnect,"preloadFont",()=>Q.preloadFont,"preloadStyle",()=>Q.preloadStyle,"prerender",()=>Q.prerender,"renderToReadableStream",()=>Q.renderToReadableStream,"routeModule",()=>N,"serverHooks",()=>Q.serverHooks,"taintObjectReference",()=>Q.taintObjectReference,"tree",()=>K,"workAsyncStorage",()=>Q.workAsyncStorage,"workUnitAsyncStorage",()=>Q.workUnitAsyncStorage],65333),a.s(["__next_app__",()=>M,"handler",()=>O,"pages",()=>L,"routeModule",()=>N,"tree",()=>K],37594);var b=a.i(61473),c=a.i(74315),d=a.i(95588),e=a.i(30072),f=a.i(42404),g=a.i(94846),h=a.i(3727),i=a.i(62800),j=a.i(70321),k=a.i(31521),l=a.i(61146),m=a.i(741),n=a.i(26974),o=a.i(98468),p=a.i(92406),q=a.i(40482),r=a.i(34057),s=a.i(68889),t=a.i(3676),u=a.i(35373),v=a.i(64422),w=a.i(52259),x=a.i(91795),y=a.i(67429),z=a.i(96598),A=a.i(75794);a.i(43731);var B=a.i(53055),C=a.i(40533),D=a.i(74019),E=a.i(20463),F=a.i(37984),G=a.i(57436),H=a.i(93695);a.i(18883);var I=a.i(35900),J=a.i(60358);let K=["",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/school-management-system/src/app/page.tsx"]}]},{metadata:{icon:[async a=>[{url:(0,b.fillMetadataSegment)("//",await a.params,"favicon.ico")+`?${c.default.src.split("/").splice(-1)[0]}`,sizes:`${c.default.width}x${c.default.height}`,type:"image/x-icon"}]]},layout:[()=>d,"[project]/school-management-system/src/app/layout.tsx"],"not-found":[()=>e,"[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/client/components/builtin/not-found.js"],forbidden:[()=>f,"[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>g,"[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/client/components/builtin/unauthorized.js"],"global-error":[()=>h,"[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/client/components/builtin/global-error.js"]}],L=["[project]/school-management-system/src/app/page.tsx"],M={require:a.r.bind(a),loadChunk:a.l.bind(a)},N=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:K},distDir:".next",relativeProjectDir:""});async function O(a,b,c){var d;let e="/page";e=e.replace(/\/index$/,"")||"/";let f=(0,n.getRequestMeta)(a,"postponed"),g=(0,n.getRequestMeta)(a,"minimalMode"),i=await N.prepare(a,b,{srcPage:e,multiZoneDraftMode:!1});if(!i)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:j,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac,interceptionRoutePatterns:ad}=i,ae=R.pathname||"/",af=(0,x.normalizeAppPath)(e),{isOnDemandRevalidate:ag}=i,ah=N.match(ae,Z),ai=!!Z.routes[_],aj=!!(ah||ai||Z.routes[af]),ak=a.headers["user-agent"]||"",al=(0,A.getBotType)(ak),am=(0,v.isHtmlBotRequest)(a),an=(0,n.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[z.NEXT_ROUTER_PREFETCH_HEADER],ao=(0,n.getRequestMeta)(a,"isRSCRequest")??!!a.headers[z.RSC_HEADER],ap=(0,y.getIsPossibleServerAction)(a),aq=(0,s.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[af]??Z.dynamicRoutes[af])?void 0:d.renderingMode)==="PARTIALLY_STATIC",ar=!1,as=!1,at=aq?f:void 0,au=aq&&ao&&!an,av=(0,n.getRequestMeta)(a,"segmentPrefetchRSCRequest"),aw=!ak||(0,v.shouldServeStreamingMetadata)(ak,ac.htmlLimitedBots);am&&aq&&(aj=!1,aw=!1);let ax=!0===N.isDev||!aj||"string"==typeof f||au,ay=am&&aq,az=null;$||!aj||ax||ap||at||au||(az=_);let aA=az;!aA&&N.isDev&&(aA=_),N.isDev||$||!aj||!ao||au||(0,q.stripFlightHeaders)(a.headers);let aB={...I,tree:K,pages:L,GlobalError:h.default,handler:O,routeModule:N,__next_app__:M};W&&X&&(0,u.setReferenceManifestsSingleton)({page:e,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,w.createServerModuleMap)({serverActionsManifest:W})});let aC=a.method||"GET",aD=(0,m.getTracer)(),aE=aD.getActiveScopeSpan();try{let d=N.getVaryHeader(_,ad);b.setHeader("Vary",d);let f=async(c,d)=>{let e=new r.NodeNextRequest(a),f=new r.NodeNextResponse(b);return N.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aD.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==o.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aC} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aC} ${a.url}`)})},h=async({span:d,postponed:g,fallbackRouteParams:h})=>{let i={query:P,params:Q,page:af,sharedContext:{buildId:j},serverComponentsHmrCache:(0,n.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:h,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aB,Component:(0,p.interopDefault)(aB),params:Q,routeModule:N,page:e,postponed:g,shouldWaitOnAllReady:ay,serveStreamingMetadata:aw,supportsDynamicResponse:"string"==typeof g||ax,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:require("path").join(process.cwd(),N.relativeProjectDir),isDraftMode:$,isRevalidate:aj&&!g&&!au,botType:al,isOnDemandRevalidate:ag,isPossibleServerAction:ap,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,n.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...ar?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:ar}:{},experimental:{isRoutePPREnabled:aq,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,cacheComponents:!!ac.experimental.cacheComponents,clientSegmentCache:!!ac.experimental.clientSegmentCache,clientParamParsing:!!ac.experimental.clientParamParsing,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>N.onRequestError(a,b,d,ab),err:(0,n.getRequestMeta)(a,"invokeError"),dev:N.isDev}},k=await f(d,i),{metadata:l}=k,{cacheControl:m,headers:o={},fetchTags:q}=l;if(q&&(o[E.NEXT_CACHE_TAGS_HEADER]=q),a.fetchMetrics=l.fetchMetrics,aj&&(null==m?void 0:m.revalidate)===0&&!N.isDev&&!aq){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:B.CachedRouteKind.APP_PAGE,html:k,headers:o,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},i=async({hasResolved:d,previousCacheEntry:e,isRevalidating:f,span:i})=>{let j,l=!1===N.isDev,m=d||b.writableEnded;if(ag&&aa&&!e&&!g)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ah&&(j=(0,C.parseFallbackField)(ah.fallback)),j===C.FallbackMode.PRERENDER&&(0,A.isBot)(ak)&&(!aq||am)&&(j=C.FallbackMode.BLOCKING_STATIC_RENDER),(null==e?void 0:e.isStale)===-1&&(ag=!0),ag&&(j!==C.FallbackMode.NOT_FOUND||e)&&(j=C.FallbackMode.BLOCKING_STATIC_RENDER),!g&&j!==C.FallbackMode.BLOCKING_STATIC_RENDER&&aA&&!m&&!$&&S&&(l||!ai)){let b;if((l||ah)&&j===C.FallbackMode.NOT_FOUND)throw new H.NoFallbackError;if(aq&&!ao){let d="string"==typeof(null==ah?void 0:ah.fallback)?ah.fallback:l?af:null;if(b=await N.handleResponse({cacheKey:d,req:a,nextConfig:ac,routeKind:k.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:aq,responseGenerator:async()=>h({span:i,postponed:void 0,fallbackRouteParams:l||as?(0,t.getFallbackRouteParams)(af):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ag||f||!at?void 0:at;if(ar&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:B.CachedRouteKind.PAGES,html:D.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=S&&aq&&((0,n.getRequestMeta)(a,"renderFallbackShell")||as)?(0,t.getFallbackRouteParams)(ae):null;return h({span:i,postponed:o,fallbackRouteParams:p})},l=async d=>{var e,f,j,l,m;let o,p=await N.handleResponse({cacheKey:az,responseGenerator:a=>i({span:d,...a}),routeKind:k.RouteKind.APP_PAGE,isOnDemandRevalidate:ag,isRoutePPREnabled:aq,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),N.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!p){if(az)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(e=p.value)?void 0:e.kind)!==B.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=p.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let q="string"==typeof p.value.postponed;aj&&!au&&(!q||an)&&(g||b.setHeader("x-nextjs-cache",ag?"REVALIDATED":p.isMiss?"MISS":p.isStale?"STALE":"HIT"),b.setHeader(z.NEXT_IS_PRERENDER_HEADER,"1"));let{value:r}=p;if(at)o={revalidate:0,expire:void 0};else if(g&&ao&&!an&&aq)o={revalidate:0,expire:void 0};else if(!N.isDev)if($)o={revalidate:0,expire:void 0};else if(aj){if(p.cacheControl)if("number"==typeof p.cacheControl.revalidate){if(p.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${p.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});o={revalidate:p.cacheControl.revalidate,expire:(null==(l=p.cacheControl)?void 0:l.expire)??ac.expireTime}}else o={revalidate:E.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(o={revalidate:0,expire:void 0});if(p.cacheControl=o,"string"==typeof av&&(null==r?void 0:r.kind)===B.CachedRouteKind.APP_PAGE&&r.segmentData){b.setHeader(z.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=r.headers)?void 0:m[E.NEXT_CACHE_TAGS_HEADER];g&&aj&&c&&"string"==typeof c&&b.setHeader(E.NEXT_CACHE_TAGS_HEADER,c);let d=r.segmentData.get(av);return void 0!==d?(0,G.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:D.default.fromStatic(d,z.RSC_CONTENT_TYPE_HEADER),cacheControl:p.cacheControl}):(b.statusCode=204,(0,G.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:D.default.EMPTY,cacheControl:p.cacheControl}))}let s=(0,n.getRequestMeta)(a,"onCacheEntry");if(s&&await s({...p,value:{...p.value,kind:"PAGE"}},{url:(0,n.getRequestMeta)(a,"initURL")}))return null;if(q&&at)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(r.headers){let a={...r.headers};for(let[c,d]of(g&&aj||delete a[E.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(f=r.headers)?void 0:f[E.NEXT_CACHE_TAGS_HEADER];if(g&&aj&&t&&"string"==typeof t&&b.setHeader(E.NEXT_CACHE_TAGS_HEADER,t),!r.status||ao&&aq||(b.statusCode=r.status),!g&&r.status&&J.RedirectStatusCode[r.status]&&ao&&(b.statusCode=200),q&&b.setHeader(z.NEXT_DID_POSTPONE_HEADER,"1"),ao&&!$){if(void 0===r.rscData){if(r.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,G.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:r.html,cacheControl:au?{revalidate:0,expire:void 0}:p.cacheControl})}return(0,G.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:D.default.fromStatic(r.rscData,z.RSC_CONTENT_TYPE_HEADER),cacheControl:p.cacheControl})}let u=r.html;if(!q||g||ao)return(0,G.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:u,cacheControl:p.cacheControl});if(ar)return u.push(new ReadableStream({start(a){a.enqueue(F.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,G.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let v=new TransformStream;return u.push(v.readable),h({span:d,postponed:r.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==B.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(v.writable)}).catch(a=>{v.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,G.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aE)return await aD.withPropagatedContext(a.headers,()=>aD.trace(o.BaseServerSpan.handleRequest,{spanName:`${aC} ${a.url}`,kind:m.SpanKind.SERVER,attributes:{"http.method":aC,"http.target":a.url}},l));await l(aE)}catch(b){throw aE||b instanceof H.NoFallbackError||await N.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"render",revalidateReason:(0,l.getRevalidateReason)({isRevalidate:aj,isOnDemandRevalidate:ag})},ab),b}}a.i(37594);var P=h,Q=I}];

//# sourceMappingURL=school-management-system_afc4cb05._.js.map