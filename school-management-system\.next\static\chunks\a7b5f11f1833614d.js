(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,t,r)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>l,"CardHeader",()=>n,"CardTitle",()=>i]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let s=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...n})});s.displayName="Card";let n=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...n})});n.displayName="CardHeader";let i=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});i.displayName="CardTitle";let l=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",s),...n})});l.displayName="CardDescription";let d=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",s),...n})});d.displayName="CardContent",r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>m],30151);var t=e.i(53379),r=e.i(46686);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{let r=!1,s=t.map(t=>{let s=a(t,e);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let e=0;e<s.length;e++){let r=s[e];"function"==typeof r?r():a(t[e],null)}}}}function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return r.useCallback(s(...t),t)}function i(e){let a=function(e){let t=r.forwardRef((e,t)=>{let{children:a,...n}=e;if(r.isValidElement(a)){var i,l,d;let e,c,o=(c=(e=null==(l=Object.getOwnPropertyDescriptor((i=a).props,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(c=(e=null==(d=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,x=function(e,t){let r={...t};for(let a in t){let s=e[a],n=t[a];/^on[A-Z]/.test(a)?s&&n?r[a]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let a=n(...t);return s(...t),a}:s&&(r[a]=s):"style"===a?r[a]={...s,...n}:"className"===a&&(r[a]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,a.props);return a.type!==r.Fragment&&(x.ref=t?s(t,o):o),r.cloneElement(a,x)}return r.Children.count(a)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=r.forwardRef((e,s)=>{let{children:n,...i}=e,l=r.Children.toArray(n),d=l.find(c);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,t.jsx)(a,{...i,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,t.jsx)(a,{...i,ref:s,children:n})});return n.displayName="".concat(e,".Slot"),n}e.s(["Slot",()=>l,"createSlot",()=>i],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>n],35952);var l=i("Slot"),d=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var o=e.i(94323),x=e.i(36946);let u=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=r.forwardRef((e,r)=>{let{className:a,variant:s,size:n,asChild:i=!1,...d}=e;return(0,t.jsx)(i?l:"button",{className:(0,x.cn)(u({variant:s,size:n,className:a})),ref:r,...d})});m.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(46686),r=e.i(50321),a=e.i(88338),s=e.i(53379),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let n=(0,a.createSlot)("Primitive.".concat(r)),i=t.forwardRef((e,t)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a?n:r,{...i,ref:t})});return i.displayName="Primitive.".concat(r),{...e,[r]:i}},{});function i(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4741,e=>{"use strict";e.s(["default",()=>i],4741);var t=e.i(46686);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,r)=>{let{color:n="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:x,...u}=e;return(0,t.createElement)("svg",{ref:r,...s,width:i,height:i,stroke:n,strokeWidth:d?24*Number(l)/Number(i):l,className:a("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(u)&&{"aria-hidden":"true"},...u},[...x.map(e=>{let[r,a]=e;return(0,t.createElement)(r,a)}),...Array.isArray(o)?o:[o]])}),i=(e,s)=>{let i=(0,t.forwardRef)((i,l)=>{let{className:d,...c}=i;return(0,t.createElement)(n,{ref:l,iconNode:s,className:a("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...c})});return i.displayName=r(e),i}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>s,"studentNavigation",()=>a,"teacherNavigation",()=>r]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"FileText"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],r=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],a=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function s(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},96487,e=>{"use strict";e.s(["Input",()=>s]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let s=r.forwardRef((e,r)=>{let{className:s,type:n,...i}=e;return(0,t.jsx)("input",{type:n,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...i})});s.displayName="Input"},92521,e=>{"use strict";e.s(["Label",()=>d],92521);var t=e.i(53379),r=e.i(46686),a=e.i(62521),s=r.forwardRef((e,r)=>(0,t.jsx)(a.Primitive.label,{...e,ref:r,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var n=e.i(94323),i=e.i(36946);let l=(0,n.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(s,{ref:r,className:(0,i.cn)(l(),a),...n})});d.displayName=s.displayName},9422,e=>{"use strict";e.s(["Clock",()=>t],9422);let t=(0,e.i(4741).default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4534,e=>{"use strict";e.s(["CheckCircle",()=>t],4534);let t=(0,e.i(4741).default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},43008,e=>{"use strict";e.s(["XCircle",()=>t],43008);let t=(0,e.i(4741).default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},35229,e=>{"use strict";e.s(["Filter",()=>t],35229);let t=(0,e.i(4741).default)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},17429,e=>{"use strict";e.s(["default",()=>g]);var t=e.i(53379),r=e.i(46686),a=e.i(32668),s=e.i(30151),n=e.i(96487),i=e.i(92521),l=e.i(89559),d=e.i(87523),c=e.i(4534),o=e.i(43008),x=e.i(9422),u=e.i(96274),m=e.i(23178),h=e.i(35229),f=e.i(84633);function g(){let[e,g]=(0,r.useState)([]),[p,y]=(0,r.useState)(new Date().toISOString().split("T")[0]),[b,j]=(0,r.useState)("all"),[v,N]=(0,r.useState)("all");(0,r.useEffect)(()=>{g([{id:"1",studentName:"John Doe",admissionNo:"STU001",className:"Grade 8",sectionName:"A",date:"2024-12-15",status:"PRESENT",takenBy:"Mrs. Smith"},{id:"2",studentName:"Jane Smith",admissionNo:"STU002",className:"Grade 8",sectionName:"A",date:"2024-12-15",status:"ABSENT",remarks:"Sick leave",takenBy:"Mrs. Smith"},{id:"3",studentName:"Mike Johnson",admissionNo:"STU003",className:"Grade 8",sectionName:"A",date:"2024-12-15",status:"LATE",takenBy:"Mrs. Smith"}])},[]);let k={total:e.length,present:e.filter(e=>"PRESENT"===e.status).length,absent:e.filter(e=>"ABSENT"===e.status).length,late:e.filter(e=>"LATE"===e.status).length,attendanceRate:e.length>0?Math.round(e.filter(e=>"PRESENT"===e.status).length/e.length*100):0};return(0,t.jsx)(l.default,{title:"Attendance Management",navigation:f.adminNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Attendance Management"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Monitor and manage student attendance records"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(s.Button,{variant:"outline",children:[(0,t.jsx)(m.Download,{className:"w-4 h-4 mr-2"}),"Export Report"]}),(0,t.jsxs)(s.Button,{children:[(0,t.jsx)(u.FileText,{className:"w-4 h-4 mr-2"}),"Generate Report"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Total Students"}),(0,t.jsx)(d.Users,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsx)(a.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:k.total})})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Present"}),(0,t.jsx)(c.CheckCircle,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsx)(a.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:k.present})})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Absent"}),(0,t.jsx)(o.XCircle,{className:"h-4 w-4 text-red-600"})]}),(0,t.jsx)(a.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:k.absent})})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Late"}),(0,t.jsx)(x.Clock,{className:"h-4 w-4 text-yellow-600"})]}),(0,t.jsx)(a.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:k.late})})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Attendance Rate"}),(0,t.jsx)(d.Users,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsx)(a.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[k.attendanceRate,"%"]})})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsx)(a.CardHeader,{children:(0,t.jsxs)(a.CardTitle,{className:"flex items-center",children:[(0,t.jsx)(h.Filter,{className:"w-5 h-5 mr-2"}),"Filters"]})}),(0,t.jsx)(a.CardContent,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(i.Label,{htmlFor:"date",children:"Date"}),(0,t.jsx)(n.Input,{id:"date",type:"date",value:p,onChange:e=>y(e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(i.Label,{htmlFor:"class",children:"Class"}),(0,t.jsxs)("select",{id:"class",value:b,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 dark:bg-gray-900 dark:text-gray-100 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Classes"}),(0,t.jsx)("option",{value:"grade8",children:"Grade 8"}),(0,t.jsx)("option",{value:"grade9",children:"Grade 9"}),(0,t.jsx)("option",{value:"grade10",children:"Grade 10"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(i.Label,{htmlFor:"section",children:"Section"}),(0,t.jsxs)("select",{id:"section",value:v,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 dark:bg-gray-900 dark:text-gray-100 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Sections"}),(0,t.jsx)("option",{value:"a",children:"Section A"}),(0,t.jsx)("option",{value:"b",children:"Section B"}),(0,t.jsx)("option",{value:"c",children:"Section C"})]})]})]})})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{children:[(0,t.jsx)(a.CardTitle,{children:"Attendance Records"}),(0,t.jsxs)(a.CardDescription,{children:["Attendance for ",new Date(p).toLocaleDateString()]})]}),(0,t.jsx)(a.CardContent,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-800",children:[(0,t.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-800",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Student"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Admission No"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Class"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Section"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Remarks"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Taken By"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Actions"})]})}),(0,t.jsx)("tbody",{className:"bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800",children:e.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-700 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e.studentName.split(" ").map(e=>e[0]).join("")})})}),(0,t.jsx)("div",{className:"ml-4",children:(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.studentName})})]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:e.admissionNo}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:e.className}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:e.sectionName}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(e=>{switch(e){case"PRESENT":return(0,t.jsx)(c.CheckCircle,{className:"w-4 h-4 text-green-600"});case"ABSENT":return(0,t.jsx)(o.XCircle,{className:"w-4 h-4 text-red-600"});case"LATE":return(0,t.jsx)(x.Clock,{className:"w-4 h-4 text-yellow-600"});case"HALF_DAY":return(0,t.jsx)(x.Clock,{className:"w-4 h-4 text-orange-600"});default:return null}})(e.status),(0,t.jsx)("span",{className:"ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((e=>{switch(e){case"PRESENT":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";case"ABSENT":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";case"LATE":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";case"HALF_DAY":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"}})(e.status)),children:e.status})]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:e.remarks||"-"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:e.takenBy}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:"Edit"})})]},e.id))})]})})})]})]})})}}]);