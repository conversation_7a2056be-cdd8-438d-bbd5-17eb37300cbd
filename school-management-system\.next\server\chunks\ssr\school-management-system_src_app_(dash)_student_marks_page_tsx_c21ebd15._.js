module.exports=[39027,a=>{"use strict";a.s(["default",()=>o]);var b=a.i(41825),c=a.i(54159),d=a.i(25384),e=a.i(4082),f=a.i(2331),g=a.i(62821),h=a.i(95788),i=a.i(32541),j=a.i(92761),k=a.i(66710),l=a.i(78402),m=a.i(14401),n=a.i(73211);function o(){let{data:a}=(0,d.useSession)(),[o,p]=(0,c.useState)([]),[q,r]=(0,c.useState)("all"),[s,t]=(0,c.useState)("all"),[u,v]=(0,c.useState)(!0),[w,x]=(0,c.useState)([]),[y,z]=(0,c.useState)([]);(0,c.useEffect)(()=>{A(),B()},[q,s]);let A=async()=>{try{v(!0);let a=new URLSearchParams;"all"!==q&&a.append("termId",q),"all"!==s&&a.append("subjectId",s);let b=await fetch(`/api/student/marks?${a}`);if(b.ok){let a=await b.json();p(a)}else console.error("Failed to fetch marks")}catch(a){console.error("Error fetching marks:",a)}finally{v(!1)}},B=async()=>{try{x([{id:"term1",name:"Term 1"},{id:"term2",name:"Term 2"},{id:"term3",name:"Term 3"}]),z([{id:"math",name:"Mathematics"},{id:"english",name:"English"},{id:"science",name:"Science"},{id:"social",name:"Social Studies"},{id:"computer",name:"Computer Science"}])}catch(a){console.error("Error fetching terms and subjects:",a)}},C=a=>{switch(a){case"A+":case"A":return"bg-green-100 text-green-800";case"B+":case"B":return"bg-blue-100 text-blue-800";case"C+":case"C":return"bg-yellow-100 text-yellow-800";case"D":return"bg-orange-100 text-orange-800";case"F":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},D=o.map(a=>{let b=Math.round(a.obtainedMarks/a.exam.maxMarks*1e4)/100,c=b>=90?"A+":b>=80?"A":b>=70?"B+":b>=60?"B":b>=50?"C+":b>=40?"C":b>=30?"D":"F";return{...a,percentage:b,grade:c}}),E={total:D.length,average:D.length>0?Math.round(D.reduce((a,b)=>a+b.percentage,0)/D.length):0,highest:D.length>0?Math.max(...D.map(a=>a.percentage)):0,lowest:D.length>0?Math.min(...D.map(a=>a.percentage)):0,passed:D.filter(a=>a.percentage>=40).length,failed:D.filter(a=>a.percentage<40).length};return(0,b.jsx)(g.default,{title:"My Marks",navigation:h.studentNavigation,children:(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"My Marks"}),(0,b.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"View your examination marks and grades"})]}),(0,b.jsxs)("div",{className:"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2",children:[(0,b.jsxs)(f.Button,{variant:"outline",className:"w-full sm:w-auto",children:[(0,b.jsx)(m.Download,{className:"w-4 h-4 mr-2"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Download"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Download Report"})]}),(0,b.jsxs)(f.Button,{className:"w-full sm:w-auto",children:[(0,b.jsx)(l.FileText,{className:"w-4 h-4 mr-2"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Report Card"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"View Report Card"})]})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6",children:[(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Total Exams"}),(0,b.jsx)(l.FileText,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsx)(e.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:E.total})})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Average Score"}),(0,b.jsx)(k.TrendingUp,{className:"h-4 w-4 text-blue-600"})]}),(0,b.jsx)(e.CardContent,{children:(0,b.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[E.average,"%"]})})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Highest Score"}),(0,b.jsx)(i.Award,{className:"h-4 w-4 text-green-600"})]}),(0,b.jsx)(e.CardContent,{children:(0,b.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[E.highest,"%"]})})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Lowest Score"}),(0,b.jsx)(k.TrendingUp,{className:"h-4 w-4 text-red-600"})]}),(0,b.jsx)(e.CardContent,{children:(0,b.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[E.lowest,"%"]})})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Passed"}),(0,b.jsx)(i.Award,{className:"h-4 w-4 text-green-600"})]}),(0,b.jsx)(e.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:E.passed})})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(e.CardTitle,{className:"text-sm font-medium",children:"Failed"}),(0,b.jsx)(i.Award,{className:"h-4 w-4 text-red-600"})]}),(0,b.jsx)(e.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-red-600",children:E.failed})})]})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsx)(e.CardHeader,{children:(0,b.jsx)(e.CardTitle,{children:"Filters"})}),(0,b.jsx)(e.CardContent,{children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"term",className:"block text-sm font-medium text-gray-700 mb-2",children:"Term"}),(0,b.jsxs)("select",{id:"term",value:q,onChange:a=>r(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:"All Terms"}),w.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),(0,b.jsxs)("select",{id:"subject",value:s,onChange:a=>t(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:"All Subjects"}),y.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))]})]})]})})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{children:[(0,b.jsx)(e.CardTitle,{children:"My Examination Marks"}),(0,b.jsx)(e.CardDescription,{children:"Detailed view of all your examination results"})]}),(0,b.jsx)(e.CardContent,{children:u?(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,b.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading marks..."})]}):0===D.length?(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)(l.FileText,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,b.jsx)("p",{className:"text-gray-600",children:"No marks found"})]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,b.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,b.jsx)("thead",{className:"bg-gray-50",children:(0,b.jsxs)("tr",{children:[(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Exam"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subject"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Term"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Marks"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentage"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Grade"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,b.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:D.map(a=>(0,b.jsxs)("tr",{children:[(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(j.BookOpen,{className:"w-4 h-4 mr-2 text-blue-600"}),(0,b.jsx)("span",{className:"text-sm font-medium text-gray-900",children:a.exam.name})]})}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.exam.subject.name}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.exam.term.name}),(0,b.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.obtainedMarks,"/",a.exam.maxMarks]}),(0,b.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.percentage,"%"]}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,b.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${C(a.grade)}`,children:a.grade})}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(a.exam.date).toLocaleDateString()}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,b.jsx)(f.Button,{variant:"outline",size:"sm",children:(0,b.jsx)(n.Eye,{className:"w-4 h-4"})})})]},a.id))})]})}),(0,b.jsx)("div",{className:"lg:hidden space-y-4",children:D.map(a=>(0,b.jsx)(e.Card,{className:"p-4",children:(0,b.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-start justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[(0,b.jsx)(j.BookOpen,{className:"w-5 h-5 text-blue-600 flex-shrink-0"}),(0,b.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 truncate",children:a.exam.name}),(0,b.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[a.exam.subject.name," • ",a.exam.term.name]})]})]}),(0,b.jsx)("div",{className:"flex items-center space-x-2 ml-4",children:(0,b.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${C(a.grade)}`,children:a.grade})})]}),(0,b.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Marks:"}),(0,b.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[a.obtainedMarks,"/",a.exam.maxMarks]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Percentage:"}),(0,b.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 font-semibold",children:[a.percentage,"%"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Date:"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:new Date(a.exam.date).toLocaleDateString()})]}),(0,b.jsx)("div",{className:"flex justify-end",children:(0,b.jsxs)(f.Button,{variant:"outline",size:"sm",children:[(0,b.jsx)(n.Eye,{className:"w-4 h-4 mr-1"}),"View Details"]})}),a.remarks&&(0,b.jsxs)("div",{className:"col-span-2",children:[(0,b.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Remarks:"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:a.remarks})]})]})]})},a.id))})]})})]})]})})}}];

//# sourceMappingURL=school-management-system_src_app_%28dash%29_student_marks_page_tsx_c21ebd15._.js.map