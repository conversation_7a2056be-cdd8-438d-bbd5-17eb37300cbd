(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push(["chunks/[root-of-the-server]__433d4839._.js",
"[externals]/node:buffer [external] (node:buffer, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}),
"[project]/school-management-system/src/middleware.ts [middleware-edge] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "config",
    ()=>config,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$4$2e$24$2e$11_next$40$15$2e$5_c2997e08dc728cda550a5ed739df8c9e$2f$node_modules$2f$next$2d$auth$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/school-management-system/node_modules/.pnpm/next-auth@4.24.11_next@15.5_c2997e08dc728cda550a5ed739df8c9e/node_modules/next-auth/middleware.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/esm/server/web/exports/index.js [middleware-edge] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$4$2e$24$2e$11_next$40$15$2e$5_c2997e08dc728cda550a5ed739df8c9e$2f$node_modules$2f$next$2d$auth$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["withAuth"])(function middleware(req) {
    const token = req.nextauth.token;
    const { pathname } = req.nextUrl;
    // If no token, redirect to login
    if (!token) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/login', req.url));
    }
    const userRole = token.role;
    // Role-based route protection
    if (pathname.startsWith('/admin')) {
        if (userRole !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/unauthorized', req.url));
        }
    }
    if (pathname.startsWith('/teacher')) {
        if (userRole !== 'TEACHER' && userRole !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/unauthorized', req.url));
        }
    }
    if (pathname.startsWith('/student')) {
        if (userRole !== 'STUDENT' && userRole !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/unauthorized', req.url));
        }
    }
    // API route protection
    if (pathname.startsWith('/api/admin')) {
        if (userRole !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
    }
    if (pathname.startsWith('/api/teacher')) {
        if (userRole !== 'TEACHER' && userRole !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
    }
    if (pathname.startsWith('/api/student')) {
        if (userRole !== 'STUDENT' && userRole !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}, {
    callbacks: {
        authorized: ({ token })=>!!token
    }
});
const config = {
    matcher: [
        '/admin/:path*',
        '/teacher/:path*',
        '/student/:path*',
        '/api/admin/:path*',
        '/api/teacher/:path*',
        '/api/student/:path*'
    ]
};
}),
]);

//# sourceMappingURL=%5Broot-of-the-server%5D__433d4839._.js.map