'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { adminNavigation } from '@/lib/navigation'
import {
  Users,
  GraduationCap,
  BookOpen,
  FileText,
  Calendar,
  BarChart3,
  Settings,
  UserPlus,
  ClipboardList,
  Award,
  Loader2
} from 'lucide-react'

interface DashboardStats {
  totalStudents: number
  totalTeachers: number
  totalClasses: number
  totalSubjects: number
  activeStudents: number
  activeTeachers: number
  attendanceRate: number
  averageMarks: number
  totalMarksRecords: number
  totalAttendanceRecords: number
}

export default function AdminDashboard() {
  const { data: session } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/admin/dashboard/stats')
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard statistics')
        }
        const data = await response.json()
        setStats(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        console.error('Error fetching dashboard stats:', err)
      } finally {
        setLoading(false)
      }
    }

    if (session?.user?.role === 'ADMIN') {
      fetchStats()
    }
  }, [session])

  const quickActions = [
    {
      title: 'Add New Student',
      description: 'Register a new student',
      icon: UserPlus,
      href: '/admin/students/new',
      color: 'bg-blue-500'
    },
    {
      title: 'Add New Teacher',
      description: 'Register a new teacher',
      icon: GraduationCap,
      href: '/admin/teachers/new',
      color: 'bg-indigo-500'
    },
    {
      title: 'Generate Reports',
      description: 'Create term reports',
      icon: FileText,
      href: '/admin/reports',
      color: 'bg-green-500'
    },
    {
      title: 'View Attendance',
      description: 'Check daily attendance',
      icon: ClipboardList,
      href: '/admin/attendance',
      color: 'bg-purple-500'
    },
    {
      title: 'Manage Marks',
      description: 'Enter and review marks',
      icon: Award,
      href: '/admin/marks',
      color: 'bg-orange-500'
    }
  ]

  return (
    <DashboardLayout title="Admin Dashboard" navigation={adminNavigation}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Welcome back, {session?.user?.firstName || 'Admin'}!
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Here's an overview of your school management system.
            </p>
          </CardContent>
        </Card>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading dashboard statistics...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">Error loading dashboard: {error}</p>
          </div>
        )}

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalStudents}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.activeStudents} active in last 30 days
                </p>
              </CardContent>
            </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Teachers</CardTitle>
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTeachers}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeTeachers} active in last 30 days
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalClasses}</div>
              <p className="text-xs text-muted-foreground">
                Across all grades
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.attendanceRate}%</div>
              <p className="text-xs text-muted-foreground">
                {stats.totalAttendanceRecords} records (last 30 days)
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Marks</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageMarks}%</div>
              <p className="text-xs text-muted-foreground">
                {stats.totalMarksRecords} marks recorded
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Subjects</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSubjects}</div>
              <p className="text-xs text-muted-foreground">
                Active curriculum
              </p>
            </CardContent>
          </Card>
        </div>
        )}

        {/* Quick Actions */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              {quickActions.map((action) => (
                <Card 
                  key={action.title} 
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => router.push(action.href)}
                >
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center text-center space-y-3 sm:flex-row sm:items-center sm:text-left sm:space-y-0 sm:space-x-3">
                      <div className={`p-2 rounded-lg ${action.color} flex-shrink-0`}>
                        <action.icon className="h-5 w-5 text-white" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">{action.title}</h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{action.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Recent Activity</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">New student registered</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">John Doe - Grade 8A</p>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">2 hours ago</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Attendance marked</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Grade 8A - 95% present</p>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">4 hours ago</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Marks uploaded</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Mathematics - Unit Test 1</p>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">1 day ago</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
