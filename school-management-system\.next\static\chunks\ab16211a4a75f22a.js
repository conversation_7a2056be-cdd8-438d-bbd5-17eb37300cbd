(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,t,r)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>a,"CardContent",()=>d,"CardDescription",()=>l,"CardHeader",()=>n,"CardTitle",()=>i]);var t=e.i(53379),r=e.i(46686),s=e.i(36946);let a=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,s.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...n})});a.displayName="Card";let n=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",a),...n})});n.displayName="CardHeader";let i=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)("h3",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",a),...n})});i.displayName="CardTitle";let l=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-gray-600 dark:text-gray-400",a),...n})});l.displayName="CardDescription";let d=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",a),...n})});d.displayName="CardContent",r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",a),...n})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>x],30151);var t=e.i(53379),r=e.i(46686);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{let r=!1,a=t.map(t=>{let a=s(t,e);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let e=0;e<a.length;e++){let r=a[e];"function"==typeof r?r():s(t[e],null)}}}}function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return r.useCallback(a(...t),t)}function i(e){let s=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...n}=e;if(r.isValidElement(s)){var i,l,d;let e,c,o=(c=(e=null==(l=Object.getOwnPropertyDescriptor((i=s).props,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(c=(e=null==(d=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,m=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let s=n(...t);return a(...t),s}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,s.props);return s.type!==r.Fragment&&(m.ref=t?a(t,o):o),r.cloneElement(s,m)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=r.forwardRef((e,a)=>{let{children:n,...i}=e,l=r.Children.toArray(n),d=l.find(c);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,t.jsx)(s,{...i,ref:a,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,t.jsx)(s,{...i,ref:a,children:n})});return n.displayName="".concat(e,".Slot"),n}e.s(["Slot",()=>l,"createSlot",()=>i],88338),e.s(["composeRefs",()=>a,"useComposedRefs",()=>n],35952);var l=i("Slot"),d=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var o=e.i(94323),m=e.i(36946);let h=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),x=r.forwardRef((e,r)=>{let{className:s,variant:a,size:n,asChild:i=!1,...d}=e;return(0,t.jsx)(i?l:"button",{className:(0,m.cn)(h({variant:a,size:n,className:s})),ref:r,...d})});x.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(46686),r=e.i(50321),s=e.i(88338),a=e.i(53379),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let n=(0,s.createSlot)("Primitive.".concat(r)),i=t.forwardRef((e,t)=>{let{asChild:s,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(s?n:r,{...i,ref:t})});return i.displayName="Primitive.".concat(r),{...e,[r]:i}},{});function i(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4741,e=>{"use strict";e.s(["default",()=>i],4741);var t=e.i(46686);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,r)=>{let{color:n="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:m,...h}=e;return(0,t.createElement)("svg",{ref:r,...a,width:i,height:i,stroke:n,strokeWidth:d?24*Number(l)/Number(i):l,className:s("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[r,s]=e;return(0,t.createElement)(r,s)}),...Array.isArray(o)?o:[o]])}),i=(e,a)=>{let i=(0,t.forwardRef)((i,l)=>{let{className:d,...c}=i;return(0,t.createElement)(n,{ref:l,iconNode:a,className:s("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...c})});return i.displayName=r(e),i}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>a,"studentNavigation",()=>s,"teacherNavigation",()=>r]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"FileText"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],r=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],s=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function a(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},44670,e=>{"use strict";e.s(["Loader2",()=>t],44670);let t=(0,e.i(4741).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},21244,e=>{"use strict";e.s(["TrendingUp",()=>t],21244);let t=(0,e.i(4741).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},9422,e=>{"use strict";e.s(["Clock",()=>t],9422);let t=(0,e.i(4741).default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},79969,e=>{"use strict";e.s(["default",()=>g]);var t=e.i(53379),r=e.i(69758),s=e.i(46686),a=e.i(32668),n=e.i(89559),i=e.i(84633),l=e.i(87523),d=e.i(20067),c=e.i(10535),o=e.i(9422),m=e.i(21244),h=e.i(96274),x=e.i(69556),u=e.i(66505),f=e.i(44670);function g(){var e;let{data:g}=(0,r.useSession)(),[p,j]=(0,s.useState)(null),[b,y]=(0,s.useState)(!0),[v,N]=(0,s.useState)(null);(0,s.useEffect)(()=>{var e;let t=async()=>{try{let e=await fetch("/api/teacher/dashboard/stats");if(!e.ok)throw Error("Failed to fetch dashboard statistics");let t=await e.json();j(t)}catch(e){N(e instanceof Error?e.message:"An error occurred"),console.error("Error fetching teacher dashboard stats:",e)}finally{y(!1)}};(null==g||null==(e=g.user)?void 0:e.role)==="TEACHER"&&t()},[g]);let C=[{title:"Take Attendance",description:"Mark today's attendance",icon:d.ClipboardList,href:"/teacher/attendance",color:"bg-green-500"},{title:"Enter Marks",description:"Upload exam results",icon:c.Award,href:"/teacher/marks",color:"bg-blue-500"},{title:"Generate Report",description:"Create class reports",icon:h.FileText,href:"/teacher/reports",color:"bg-purple-500"},{title:"View Schedule",description:"Check your timetable",icon:o.Clock,href:"/teacher/schedule",color:"bg-orange-500"}];return(0,t.jsx)(n.default,{title:"Teacher Dashboard",navigation:i.teacherNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:["Welcome back, ",(null==g||null==(e=g.user)?void 0:e.firstName)||"Teacher","!"]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Here's your teaching overview and quick actions for today."})]}),b&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(f.Loader2,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading dashboard statistics..."})]}),v&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("p",{className:"text-red-800",children:["Error loading dashboard: ",v]})}),p&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Total Classes"}),(0,t.jsx)(x.BookOpen,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:p.totalClasses}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Assigned classes"})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Total Students"}),(0,t.jsx)(l.Users,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:p.totalStudents}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Across all classes"})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Average Attendance"}),(0,t.jsx)(m.TrendingUp,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[p.averageAttendance,"%"]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Last 30 days"})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Average Marks"}),(0,t.jsx)(c.Award,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[p.averageMarks,"%"]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[p.totalMarksGraded," marks graded"]})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Upcoming Exams"}),(0,t.jsx)(u.Calendar,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:p.upcomingExams}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Scheduled exams"})]})]})]}),p&&(0,t.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"My Classes"}),p.assignedClasses.length>0?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.assignedClasses.map(e=>(0,t.jsxs)(a.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:[(0,t.jsxs)(a.CardHeader,{children:[(0,t.jsx)(a.CardTitle,{className:"text-lg",children:e.name}),(0,t.jsx)(a.CardDescription,{children:e.subject})]}),(0,t.jsx)(a.CardContent,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Students:"}),(0,t.jsx)("span",{className:"font-medium",children:e.students})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Attendance:"}),(0,t.jsxs)("span",{className:"font-medium text-green-600",children:[e.attendance,"%"]})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Next Exam:"}),(0,t.jsx)("span",{className:"font-medium",children:e.nextExam?"".concat(e.nextExam.name," - ").concat(new Date(e.nextExam.date).toLocaleDateString()):"No upcoming exams"})]})]})})]},e.id))}):(0,t.jsx)("p",{className:"text-gray-600",children:"No classes assigned yet."})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:C.map(e=>(0,t.jsx)(a.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsx)(a.CardContent,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg ".concat(e.color),children:(0,t.jsx)(e.icon,{className:"h-5 w-5 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:e.title}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.description})]})]})})},e.title))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Today's Schedule"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Grade 8A - Mathematics"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Room 101"})]})]}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"9:00 AM - 10:00 AM"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Grade 9A - Physics"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Lab 2"})]})]}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"2:00 PM - 3:00 PM"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-purple-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-purple-500 rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Grade 8B - Mathematics"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Room 102"})]})]}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"4:00 PM - 5:00 PM"})]})]})]})]})})}}]);