module.exports=[15579,37906,57012,a=>{"use strict";a.s(["Search",()=>c],15579);var b=a.i(32639);let c=(0,b.default)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);a.s(["Plus",()=>d],37906);let d=(0,b.default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);a.s(["Edit",()=>e],57012);let e=(0,b.default)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14401,a=>{"use strict";a.s(["Download",()=>b],14401);let b=(0,a.i(32639).default)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},62867,a=>{"use strict";a.s(["Upload",()=>b],62867);let b=(0,a.i(32639).default)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},42879,a=>{"use strict";a.s(["ChevronRight",()=>b],42879);let b=(0,a.i(32639).default)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},11073,a=>{"use strict";a.s(["X",()=>b],11073);let b=(0,a.i(32639).default)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},78402,a=>{"use strict";a.s(["FileText",()=>b],78402);let b=(0,a.i(32639).default)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},62821,74751,82193,91662,92761,77233,3815,72613,99739,32541,a=>{"use strict";a.s(["default",()=>d9],62821);var b,c,d,e=a.i(41825),f=a.i(54159),g=a.i(25384),h=a.i(52963),i=a.i(2331),j=a.i(32639);let k=(0,j.default)("moon",[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]]),l=(0,j.default)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),m=(0,j.default)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);var n=a.i(40140);function o(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}var p=a.i(98752);function q(a,b=[]){let c=[],d=()=>{let b=c.map(a=>f.createContext(a));return function(c){let d=c?.[a]||b;return f.useMemo(()=>({[`__scope${a}`]:{...c,[a]:d}}),[c,d])}};return d.scopeName=a,[function(b,d){let g=f.createContext(d),h=c.length;c=[...c,d];let i=b=>{let{scope:c,children:d,...i}=b,j=c?.[a]?.[h]||g,k=f.useMemo(()=>i,Object.values(i));return(0,e.jsx)(j.Provider,{value:k,children:d})};return i.displayName=b+"Provider",[i,function(c,e){let i=e?.[a]?.[h]||g,j=f.useContext(i);if(j)return j;if(void 0!==d)return d;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let d=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return f.useMemo(()=>({[`__scope${b.scopeName}`]:d}),[d])}};return c.scopeName=b.scopeName,c}(d,...b)]}var r=globalThis?.document?f.useLayoutEffect:()=>{};f[" useEffectEvent ".trim().toString()],f[" useInsertionEffect ".trim().toString()];var s=f[" useInsertionEffect ".trim().toString()]||r;function t({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[e,g,h]=function({defaultProp:a,onChange:b}){let[c,d]=f.useState(a),e=f.useRef(c),g=f.useRef(b);return s(()=>{g.current=b},[b]),f.useEffect(()=>{e.current!==c&&(g.current?.(c),e.current=c)},[c,e]),[c,d,g]}({defaultProp:b,onChange:c}),i=void 0!==a,j=i?a:e;{let b=f.useRef(void 0!==a);f.useEffect(()=>{let a=b.current;if(a!==i){let b=i?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=i},[i,d])}return[j,f.useCallback(b=>{if(i){let c="function"==typeof b?b(a):b;c!==a&&h.current?.(c)}else g(b)},[i,a,g,h])]}Symbol("RADIX:SYNC_STATE");var u=a.i(36870),v=a.i(54472);function w(a){let b=a+"CollectionProvider",[c,d]=q(b),[g,h]=c(b,{collectionRef:{current:null},itemMap:new Map}),i=a=>{let{scope:b,children:c}=a,d=f.default.useRef(null),h=f.default.useRef(new Map).current;return(0,e.jsx)(g,{scope:b,itemMap:h,collectionRef:d,children:c})};i.displayName=b;let j=a+"CollectionSlot",k=(0,v.createSlot)(j),l=f.default.forwardRef((a,b)=>{let{scope:c,children:d}=a,f=h(j,c),g=(0,p.useComposedRefs)(b,f.collectionRef);return(0,e.jsx)(k,{ref:g,children:d})});l.displayName=j;let m=a+"CollectionItemSlot",n="data-radix-collection-item",o=(0,v.createSlot)(m),r=f.default.forwardRef((a,b)=>{let{scope:c,children:d,...g}=a,i=f.default.useRef(null),j=(0,p.useComposedRefs)(b,i),k=h(m,c);return f.default.useEffect(()=>(k.itemMap.set(i,{ref:i,...g}),()=>void k.itemMap.delete(i))),(0,e.jsx)(o,{...{[n]:""},ref:j,children:d})});return r.displayName=m,[{Provider:i,Slot:l,ItemSlot:r},function(b){let c=h(a+"CollectionConsumer",b);return f.default.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${n}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},d]}var x=new WeakMap;function y(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=z(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function z(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],x.set(this,!0)}set(a,b){return x.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=z(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],l=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{l||k[a-1]!==b||(l=!0);let c=k[l?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=y(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=y(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return y(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}});var A=f.createContext(void 0);function B(a){let b=f.useContext(A);return a||b||"ltr"}function C(a){let b=f.useRef(a);return f.useEffect(()=>{b.current=a}),f.useMemo(()=>(...a)=>b.current?.(...a),[])}var D="dismissableLayer.update",E=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),F=f.forwardRef((a,b)=>{let{disableOutsidePointerEvents:d=!1,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:i,onInteractOutside:j,onDismiss:k,...l}=a,m=f.useContext(E),[n,q]=f.useState(null),r=n?.ownerDocument??globalThis?.document,[,s]=f.useState({}),t=(0,p.useComposedRefs)(b,a=>q(a)),v=Array.from(m.layers),[w]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),x=v.indexOf(w),y=n?v.indexOf(n):-1,z=m.layersWithOutsidePointerEventsDisabled.size>0,A=y>=x,B=function(a,b=globalThis?.document){let c=C(a),d=f.useRef(!1),e=f.useRef(()=>{});return f.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){H("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...m.branches].some(a=>a.contains(b));A&&!c&&(h?.(a),j?.(a),a.defaultPrevented||k?.())},r),F=function(a,b=globalThis?.document){let c=C(a),d=f.useRef(!1);return f.useEffect(()=>{let a=a=>{a.target&&!d.current&&H("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...m.branches].some(a=>a.contains(b))&&(i?.(a),j?.(a),a.defaultPrevented||k?.())},r);return!function(a,b=globalThis?.document){let c=C(a);f.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{y===m.layers.size-1&&(g?.(a),!a.defaultPrevented&&k&&(a.preventDefault(),k()))},r),f.useEffect(()=>{if(n)return d&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(c=r.body.style.pointerEvents,r.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(n)),m.layers.add(n),G(),()=>{d&&1===m.layersWithOutsidePointerEventsDisabled.size&&(r.body.style.pointerEvents=c)}},[n,r,d,m]),f.useEffect(()=>()=>{n&&(m.layers.delete(n),m.layersWithOutsidePointerEventsDisabled.delete(n),G())},[n,m]),f.useEffect(()=>{let a=()=>s({});return document.addEventListener(D,a),()=>document.removeEventListener(D,a)},[]),(0,e.jsx)(u.Primitive.div,{...l,ref:t,style:{pointerEvents:z?A?"auto":"none":void 0,...a.style},onFocusCapture:o(a.onFocusCapture,F.onFocusCapture),onBlurCapture:o(a.onBlurCapture,F.onBlurCapture),onPointerDownCapture:o(a.onPointerDownCapture,B.onPointerDownCapture)})});function G(){let a=new CustomEvent(D);document.dispatchEvent(a)}function H(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,u.dispatchDiscreteCustomEvent)(e,f):e.dispatchEvent(f)}F.displayName="DismissableLayer",f.forwardRef((a,b)=>{let c=f.useContext(E),d=f.useRef(null),g=(0,p.useComposedRefs)(b,d);return f.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,e.jsx)(u.Primitive.div,{...a,ref:g})}).displayName="DismissableLayerBranch";var I=0;function J(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var K="focusScope.autoFocusOnMount",L="focusScope.autoFocusOnUnmount",M={bubbles:!1,cancelable:!0},N=f.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:g,onUnmountAutoFocus:h,...i}=a,[j,k]=f.useState(null),l=C(g),m=C(h),n=f.useRef(null),o=(0,p.useComposedRefs)(b,a=>k(a)),q=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(d){let a=function(a){if(q.paused||!j)return;let b=a.target;j.contains(b)?n.current=b:Q(n.current,{select:!0})},b=function(a){if(q.paused||!j)return;let b=a.relatedTarget;null!==b&&(j.contains(b)||Q(n.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&Q(j)});return j&&c.observe(j,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,j,q.paused]),f.useEffect(()=>{if(j){R.add(q);let a=document.activeElement;if(!j.contains(a)){let b=new CustomEvent(K,M);j.addEventListener(K,l),j.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(Q(d,{select:b}),document.activeElement!==c)return}(O(j).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&Q(j))}return()=>{j.removeEventListener(K,l),setTimeout(()=>{let b=new CustomEvent(L,M);j.addEventListener(L,m),j.dispatchEvent(b),b.defaultPrevented||Q(a??document.body,{select:!0}),j.removeEventListener(L,m),R.remove(q)},0)}}},[j,l,m,q]);let r=f.useCallback(a=>{if(!c&&!d||q.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=O(a);return[P(b,a),P(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&Q(f,{select:!0})):(a.preventDefault(),c&&Q(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,q.paused]);return(0,e.jsx)(u.Primitive.div,{tabIndex:-1,...i,ref:o,onKeyDown:r})});function O(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function P(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function Q(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}N.displayName="FocusScope";var R=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=S(a,b)).unshift(b)},remove(b){a=S(a,b),a[0]?.resume()}}}();function S(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var T=f[" useId ".trim().toString()]||(()=>void 0),U=0;function V(a){let[b,c]=f.useState(T());return r(()=>{a||c(a=>a??String(U++))},[a]),a||(b?`radix-${b}`:"")}let W=["top","right","bottom","left"],X=Math.min,Y=Math.max,Z=Math.round,$=Math.floor,_=a=>({x:a,y:a}),aa={left:"right",right:"left",bottom:"top",top:"bottom"},ab={start:"end",end:"start"};function ac(a,b){return"function"==typeof a?a(b):a}function ad(a){return a.split("-")[0]}function ae(a){return a.split("-")[1]}function af(a){return"x"===a?"y":"x"}function ag(a){return"y"===a?"height":"width"}let ah=new Set(["top","bottom"]);function ai(a){return ah.has(ad(a))?"y":"x"}function aj(a){return a.replace(/start|end/g,a=>ab[a])}let ak=["left","right"],al=["right","left"],am=["top","bottom"],an=["bottom","top"];function ao(a){return a.replace(/left|right|bottom|top/g,a=>aa[a])}function ap(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function aq(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function ar(a,b,c){let d,{reference:e,floating:f}=a,g=ai(b),h=af(ai(b)),i=ag(h),j=ad(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(ae(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let as=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=ar(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=ar(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function at(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=ac(b,a),o=ap(n),p=h[m?"floating"===l?"reference":"floating":l],q=aq(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=aq(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function au(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function av(a){return W.some(b=>a[b]>=0)}let aw=new Set(["left","top"]);async function ax(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=ad(c),h=ae(c),i="y"===ai(c),j=aw.has(g)?-1:1,k=f&&i?-1:1,l=ac(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function ay(a){return function(a){return!1}(a)?(a.nodeName||"").toLowerCase():"#document"}function az(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function aA(a){var b;return null==(b=(function(a){return!1}(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function aB(a){return!1}let aC=new Set(["inline","contents"]);function aD(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aO(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!aC.has(e)}let aE=new Set(["table","td","th"]),aF=[":popover-open",":modal"];function aG(a){return aF.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let aH=["transform","translate","scale","rotate","perspective"],aI=["transform","translate","scale","rotate","perspective","filter"],aJ=["paint","layout","strict","content"];function aK(a){let b=aL(),c=a;return aH.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||aI.some(a=>(c.willChange||"").includes(a))||aJ.some(a=>(c.contain||"").includes(a))}function aL(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aM=new Set(["html","body","#document"]);function aN(a){return aM.has(ay(a))}function aO(a){return az(a).getComputedStyle(a)}function aP(a){return{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function aQ(a){if("html"===ay(a))return a;let b=a.assignedSlot||a.parentNode||!1||aA(a);return b}function aR(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=aQ(b);return aN(c)?b.ownerDocument?b.ownerDocument.body:b.body:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=az(e);if(f){let a=aS(g);return b.concat(g,g.visualViewport||[],aD(e)?e:[],a&&c?aR(a):[])}return b.concat(e,aR(e,[],c))}function aS(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aT(a){let b=aO(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=!1,f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=Z(c)!==f||Z(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function aU(a){return 0,a.contextElement}function aV(a){let b=aU(a);1;return _(1)}let aW=_(0);function aX(a){let b=az(a);return aL()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:aW}function aY(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=aU(a),h=_(1);b&&(d||(h=aV(a)));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===az(g))&&e)?aX(g):_(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=az(g),b=d,c=a,e=aS(c);for(;e&&d&&b!==c;){let a=aV(e),b=e.getBoundingClientRect(),d=aO(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=aS(c=az(e))}}return aq({width:l,height:m,x:j,y:k})}function aZ(a,b){let c=aP(a).scrollLeft;return b?b.left+c:aY(aA(a)).left+c}function a$(a,b){let c=a.getBoundingClientRect();return{x:c.left+b.scrollLeft-aZ(a,c),y:c.top+b.scrollTop}}function a_(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=az(a),d=aA(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=aL();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}let j=aZ(d);if(j<=0){let a=d.ownerDocument,b=a.body,c=getComputedStyle(b),e="CSS1Compat"===a.compatMode&&parseFloat(c.marginLeft)+parseFloat(c.marginRight)||0,g=Math.abs(d.clientWidth-b.clientWidth-e);g<=25&&(f-=g)}else j<=25&&(f+=j);return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=aA(a),c=aP(a),d=a.ownerDocument.body,e=Y(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=Y(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+aZ(a),h=-c.scrollTop;return"rtl"===aO(d).direction&&(g+=Y(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(aA(a));else{1;{let c=aX(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}}return aq(d)}function a0(a){return"static"===aO(a).position}function a1(a,b){1;return null}function a2(a,b){var c;let d=az(a);if(aG(a))return d;1;{let b=aQ(a);for(;b&&!aN(b);){0;b=aQ(b)}return d}}let a3=async function(a){let b=this.getOffsetParent||a2,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){var d;let e=(d=0,!1),f=aA(b),g="fixed"===c,h=aY(a,!0,g,b),i={scrollLeft:0,scrollTop:0},j=_(0);if(e||!e&&!g)if(("body"!==ay(b)||aD(f))&&(i=aP(b)),e){let a=aY(b,!0,g,b);j.x=a.x+b.clientLeft,j.y=a.y+b.clientTop}else f&&(j.x=aZ(f));g&&!e&&f&&(j.x=aZ(f));let k=!f||e||g?_(0):a$(f,i);return{x:h.left+i.scrollLeft-j.x-k.x,y:h.top+i.scrollTop-j.y-k.y,width:h.width,height:h.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},a4={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){var b,c;let{elements:d,rect:e,offsetParent:f,strategy:g}=a,h="fixed"===g,i=aA(f),j=!!d&&aG(d.floating);if(f===i||j&&h)return e;let k={scrollLeft:0,scrollTop:0},l=_(1),m=_(0),n=(b=0,!1);(n||!n&&!h)&&(("body"!==ay(f)||aD(i))&&(k=aP(f)),c=0,0);let o=!i||n||h?_(0):a$(i,k);return{width:e.width*l.x,height:e.height*l.y,x:e.x*l.x-k.scrollLeft*l.x+m.x+o.x,y:e.y*l.y-k.scrollTop*l.y+m.y+o.y}},getDocumentElement:aA,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?aG(b)?[]:function(a,b){var c;let d=b.get(a);if(d)return d;let e=aR(a,[],!1).filter(a=>{var b;return b=0,!1}),f="fixed"===aO(a).position,g=f?aQ(a):a;return c=0,b.set(a,e),e}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=a_(b,c,e);return a.top=Y(d.top,a.top),a.right=X(d.right,a.right),a.bottom=X(d.bottom,a.bottom),a.left=Y(d.left,a.left),a},a_(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:a2,getElementRects:a3,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aT(a);return{width:b,height:c}},getScale:aV,isElement:aB,isRTL:function(a){return"rtl"===aO(a).direction}};function a5(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let a6=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=ac(a,b)||{};if(null==j)return{};let l=ap(k),m={x:c,y:d},n=af(ai(e)),o=ag(n),p=await g.getDimensions(j),q="y"===n,r=q?"clientHeight":"clientWidth",s=f.reference[o]+f.reference[n]-m[n]-f.floating[o],t=m[n]-f.reference[n],u=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),v=u?u[r]:0;v&&await (null==g.isElement?void 0:g.isElement(u))||(v=h.floating[r]||f.floating[o]);let w=v/2-p[o]/2-1,x=X(l[q?"top":"left"],w),y=X(l[q?"bottom":"right"],w),z=v-p[o]-y,A=v/2-p[o]/2+(s/2-t/2),B=Y(x,X(A,z)),C=!i.arrow&&null!=ae(e)&&A!==B&&f.reference[o]/2-(A<x?x:y)-p[o]/2<0,D=C?A<x?A-x:A-z:0;return{[n]:m[n]+D,data:{[n]:B,centerOffset:A-B-D,...C&&{alignmentOffset:D}},reset:C}}});var a7=a.i(81453),a8="undefined"!=typeof document?f.useLayoutEffect:function(){};function a9(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!a9(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!a9(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function ba(a,b){let c=1;return Math.round(b*c)/c}function bb(a){let b=f.useRef(a);return a8(()=>{b.current=a}),b}var bc=f.forwardRef((a,b)=>{let{children:c,width:d=10,height:f=5,...g}=a;return(0,e.jsx)(u.Primitive.svg,{...g,ref:b,width:d,height:f,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,e.jsx)("polygon",{points:"0,0 30,0 15,10"})})});bc.displayName="Arrow";var bd="Popper",[be,bf]=q(bd),[bg,bh]=be(bd),bi=a=>{let{__scopePopper:b,children:c}=a,[d,g]=f.useState(null);return(0,e.jsx)(bg,{scope:b,anchor:d,onAnchorChange:g,children:c})};bi.displayName=bd;var bj="PopperAnchor",bk=f.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:d,...g}=a,h=bh(bj,c),i=f.useRef(null),j=(0,p.useComposedRefs)(b,i),k=f.useRef(null);return f.useEffect(()=>{let a=k.current;k.current=d?.current||i.current,a!==k.current&&h.onAnchorChange(k.current)}),d?null:(0,e.jsx)(u.Primitive.div,{...g,ref:j})});bk.displayName=bj;var bl="PopperContent",[bm,bn]=be(bl),bo=f.forwardRef((a,b)=>{let{__scopePopper:c,side:d="bottom",sideOffset:g=0,align:h="center",alignOffset:i=0,arrowPadding:j=0,avoidCollisions:k=!0,collisionBoundary:l=[],collisionPadding:m=0,sticky:n="partial",hideWhenDetached:o=!1,updatePositionStrategy:q="optimized",onPlaced:s,...t}=a,v=bh(bl,c),[w,x]=f.useState(null),y=(0,p.useComposedRefs)(b,a=>x(a)),[z,A]=f.useState(null),B=function(a){let[b,c]=f.useState(void 0);return r(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}(z),D=B?.width??0,E=B?.height??0,F="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},G=Array.isArray(l)?l:[l],H=G.length>0,I={padding:F,boundary:G.filter(bs),altBoundary:H},{refs:J,floatingStyles:K,placement:L,isPositioned:M,middlewareData:N}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:d=[],platform:e,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=f.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=f.useState(d);a9(n,d)||o(d);let[p,q]=f.useState(null),[r,s]=f.useState(null),t=f.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=f.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=f.useRef(null),y=f.useRef(null),z=f.useRef(l),A=null!=j,B=bb(j),C=bb(e),D=bb(k),E=f.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};C.current&&(a.platform=C.current),((a,b,c)=>{let d=new Map,e={platform:a4,...c},f={...e.platform,_c:d};return as(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==D.current};F.current&&!a9(z.current,b)&&(z.current=b,a7.flushSync(()=>{m(b)}))})},[n,b,c,C,D]);a8(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let F=f.useRef(!1);a8(()=>(F.current=!0,()=>{F.current=!1}),[]),a8(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,E);E()}},[v,w,E,B,A]);let G=f.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),H=f.useMemo(()=>({reference:v,floating:w}),[v,w]),I=f.useMemo(()=>{let a={position:c,left:0,top:0};if(!H.floating)return a;let b=ba(H.floating,l.x),d=ba(H.floating,l.y);if(i)return{...a,transform:"translate("+b+"px, "+d+"px)",...(H.floating,false)};return{position:c,left:b,top:d}},[c,i,H.floating,l.x,l.y]);return f.useMemo(()=>({...l,update:E,refs:G,elements:H,floatingStyles:I}),[l,E,G,H,I])}({strategy:"fixed",placement:d+("center"!==h?"-"+h:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=aU(a),l=f||g?[...k?aR(k):[],...aR(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=aA(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=$(l),p=$(e.clientWidth-(k+m)),q={rootMargin:-o+"px "+-p+"px "+-$(e.clientHeight-(l+n))+"px "+-$(k)+"px",threshold:Y(0,X(1,i))||1},r=!0;function s(b){let d=b[0].intersectionRatio;if(d!==i){if(!r)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||a5(j,a.getBoundingClientRect())||g(),r=!1}try{d=new IntersectionObserver(s,{...q,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(s,q)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?aY(a):null;return j&&function b(){let d=aY(a);p&&!a5(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===q}),elements:{reference:v.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await ax(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:g+E,alignmentAxis:i}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=ac(a,b),j={x:c,y:d},k=await at(b,i),l=ai(ad(e)),m=af(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=Y(c,X(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=Y(c,X(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===n?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=ac(a,b),k={x:c,y:d},l=ai(e),m=af(l),n=k[m],o=k[l],p=ac(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=aw.has(ad(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,...I}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=ac(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=ad(h),v=ai(k),w=ad(k)===k,x=await (null==l.isRTL?void 0:l.isRTL(m.floating)),y=p||(w||!s?[ao(k)]:function(a){let b=ao(a);return[aj(a),b,aj(b)]}(k)),z="none"!==r;!p&&z&&y.push(...function(a,b,c,d){let e=ae(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?al:ak;return b?ak:al;case"left":case"right":return b?am:an;default:return[]}}(ad(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(aj)))),f}(k,s,r,x));let A=[k,...y],B=await at(b,t),C=[],D=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&C.push(B[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=ae(a),e=af(ai(a)),f=ag(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=ao(g)),[g,ao(g)]}(h,j,x);C.push(B[a[0]],B[a[1]])}if(D=[...D,{placement:h,overflows:C}],!C.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=A[a];if(b&&("alignment"!==o||v===ai(b)||D.every(a=>ai(a.placement)!==v||a.overflows[0]>0)))return{data:{index:a,overflows:D},reset:{placement:b}};let c=null==(f=D.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=D.filter(a=>{if(z){let b=ai(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...I}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=ac(a,b),m=await at(b,l),n=ad(g),o=ae(g),p="y"===ai(g),{width:q,height:r}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let s=r-m.top-m.bottom,t=q-m.left-m.right,u=X(r-m[e],s),v=X(q-m[f],t),w=!b.middlewareData.shift,x=u,y=v;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(y=t),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(x=s),w&&!o){let a=Y(m.left,0),b=Y(m.right,0),c=Y(m.top,0),d=Y(m.bottom,0);p?y=q-2*(0!==a||0!==b?a+b:Y(m.left,m.right)):x=r-2*(0!==c||0!==d?c+d:Y(m.top,m.bottom))}await k({...b,availableWidth:y,availableHeight:x});let z=await i.getDimensions(j.floating);return q!==z.width||r!==z.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...I,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),z&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?a6({element:c.current,padding:d}).fn(b):{}:c?a6({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:z,padding:j}),bt({arrowWidth:D,arrowHeight:E}),o&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=ac(a,b);switch(d){case"referenceHidden":{let a=au(await at(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:av(a)}}}case"escaped":{let a=au(await at(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:av(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...I})]}),[O,P]=bu(L),Q=C(s);r(()=>{M&&Q?.()},[M,Q]);let R=N.arrow?.x,S=N.arrow?.y,T=N.arrow?.centerOffset!==0,[U,V]=f.useState();return r(()=>{w&&V(window.getComputedStyle(w).zIndex)},[w]),(0,e.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:M?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[N.transformOrigin?.x,N.transformOrigin?.y].join(" "),...N.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,e.jsx)(bm,{scope:c,placedSide:O,onArrowChange:A,arrowX:R,arrowY:S,shouldHideArrow:T,children:(0,e.jsx)(u.Primitive.div,{"data-side":O,"data-align":P,...t,ref:y,style:{...t.style,animation:M?void 0:"none"}})})})});bo.displayName=bl;var bp="PopperArrow",bq={top:"bottom",right:"left",bottom:"top",left:"right"},br=f.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,f=bn(bp,c),g=bq[f.placedSide];return(0,e.jsx)("span",{ref:f.onArrowChange,style:{position:"absolute",left:f.arrowX,top:f.arrowY,[g]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f.placedSide],visibility:f.shouldHideArrow?"hidden":void 0},children:(0,e.jsx)(bc,{...d,ref:b,style:{...d.style,display:"block"}})})});function bs(a){return null!==a}br.displayName=bp;var bt=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=bu(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function bu(a){let[b,c="center"]=a.split("-");return[b,c]}var bv=f.forwardRef((a,b)=>{let{container:c,...d}=a,[g,h]=f.useState(!1);r(()=>h(!0),[]);let i=c||g&&globalThis?.document?.body;return i?a7.default.createPortal((0,e.jsx)(u.Primitive.div,{...d,ref:b}),i):null});bv.displayName="Portal";var bw=a=>{let{present:b,children:c}=a,d=function(a){var b,c;let[d,e]=f.useState(),g=f.useRef(null),h=f.useRef(a),i=f.useRef("none"),[j,k]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},f.useReducer((a,b)=>c[a][b]??a,b));return f.useEffect(()=>{let a=bx(g.current);i.current="mounted"===j?a:"none"},[j]),r(()=>{let b=g.current,c=h.current;if(c!==a){let d=i.current,e=bx(b);a?k("MOUNT"):"none"===e||b?.display==="none"?k("UNMOUNT"):c&&d!==e?k("ANIMATION_OUT"):k("UNMOUNT"),h.current=a}},[a,k]),r(()=>{if(d){let a,b=d.ownerDocument.defaultView??window,c=c=>{let e=bx(g.current).includes(CSS.escape(c.animationName));if(c.target===d&&e&&(k("ANIMATION_END"),!h.current)){let c=d.style.animationFillMode;d.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===d.style.animationFillMode&&(d.style.animationFillMode=c)})}},e=a=>{a.target===d&&(i.current=bx(g.current))};return d.addEventListener("animationstart",e),d.addEventListener("animationcancel",c),d.addEventListener("animationend",c),()=>{b.clearTimeout(a),d.removeEventListener("animationstart",e),d.removeEventListener("animationcancel",c),d.removeEventListener("animationend",c)}}k("ANIMATION_END")},[d,k]),{isPresent:["mounted","unmountSuspended"].includes(j),ref:f.useCallback(a=>{g.current=a?getComputedStyle(a):null,e(a)},[])}}(b),e="function"==typeof c?c({present:d.isPresent}):f.Children.only(c),g=(0,p.useComposedRefs)(d.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(e));return"function"==typeof c||d.isPresent?f.cloneElement(e,{ref:g}):null};function bx(a){return a?.animationName||"none"}bw.displayName="Presence";var by="rovingFocusGroup.onEntryFocus",bz={bubbles:!1,cancelable:!0},bA="RovingFocusGroup",[bB,bC,bD]=w(bA),[bE,bF]=q(bA,[bD]),[bG,bH]=bE(bA),bI=f.forwardRef((a,b)=>(0,e.jsx)(bB.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,e.jsx)(bB.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,e.jsx)(bJ,{...a,ref:b})})}));bI.displayName=bA;var bJ=f.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:d,loop:g=!1,dir:h,currentTabStopId:i,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:k,onEntryFocus:l,preventScrollOnEntryFocus:m=!1,...n}=a,q=f.useRef(null),r=(0,p.useComposedRefs)(b,q),s=B(h),[v,w]=t({prop:i,defaultProp:j??null,onChange:k,caller:bA}),[x,y]=f.useState(!1),z=C(l),A=bC(c),D=f.useRef(!1),[E,F]=f.useState(0);return f.useEffect(()=>{let a=q.current;if(a)return a.addEventListener(by,z),()=>a.removeEventListener(by,z)},[z]),(0,e.jsx)(bG,{scope:c,orientation:d,dir:s,loop:g,currentTabStopId:v,onItemFocus:f.useCallback(a=>w(a),[w]),onItemShiftTab:f.useCallback(()=>y(!0),[]),onFocusableItemAdd:f.useCallback(()=>F(a=>a+1),[]),onFocusableItemRemove:f.useCallback(()=>F(a=>a-1),[]),children:(0,e.jsx)(u.Primitive.div,{tabIndex:x||0===E?-1:0,"data-orientation":d,...n,ref:r,style:{outline:"none",...a.style},onMouseDown:o(a.onMouseDown,()=>{D.current=!0}),onFocus:o(a.onFocus,a=>{let b=!D.current;if(a.target===a.currentTarget&&b&&!x){let b=new CustomEvent(by,bz);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=A().filter(a=>a.focusable);bN([a.find(a=>a.active),a.find(a=>a.id===v),...a].filter(Boolean).map(a=>a.ref.current),m)}}D.current=!1}),onBlur:o(a.onBlur,()=>y(!1))})})}),bK="RovingFocusGroupItem",bL=f.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:d=!0,active:g=!1,tabStopId:h,children:i,...j}=a,k=V(),l=h||k,m=bH(bK,c),n=m.currentTabStopId===l,p=bC(c),{onFocusableItemAdd:q,onFocusableItemRemove:r,currentTabStopId:s}=m;return f.useEffect(()=>{if(d)return q(),()=>r()},[d,q,r]),(0,e.jsx)(bB.ItemSlot,{scope:c,id:l,focusable:d,active:g,children:(0,e.jsx)(u.Primitive.span,{tabIndex:n?0:-1,"data-orientation":m.orientation,...j,ref:b,onMouseDown:o(a.onMouseDown,a=>{d?m.onItemFocus(l):a.preventDefault()}),onFocus:o(a.onFocus,()=>m.onItemFocus(l)),onKeyDown:o(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void m.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return bM[e]}(a,m.orientation,m.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=p().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=m.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>bN(c))}}),children:"function"==typeof i?i({isCurrentTabStop:n,hasTabStop:null!=s}):i})})});bL.displayName=bK;var bM={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function bN(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var bO=new WeakMap,bP=new WeakMap,bQ={},bR=0,bS=function(a){return a&&(a.host||bS(a.parentNode))},bT=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=bS(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});bQ[c]||(bQ[c]=new WeakMap);var f=bQ[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(bO.get(a)||0)+1,j=(f.get(a)||0)+1;bO.set(a,i),f.set(a,j),g.push(a),1===i&&e&&bP.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),bR++,function(){g.forEach(function(a){var b=bO.get(a)-1,e=f.get(a)-1;bO.set(a,b),f.set(a,e),b||(bP.has(a)||a.removeAttribute(d),bP.delete(a)),e||a.removeAttribute(c)}),--bR||(bO=new WeakMap,bO=new WeakMap,bP=new WeakMap,bQ={})}},bU=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),bT(d,e,c,"aria-hidden")):function(){return null}},bV=function(){return(bV=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function bW(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var bX=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),bY="width-before-scroll-bar";function bZ(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var b$=f.useEffect,b_=new WeakMap;function b0(a){return a}var b1=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=b0),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=bV({async:!0,ssr:!1},a),e}(),b2=function(){},b3=f.forwardRef(function(a,b){var c,d,e,g,h=f.useRef(null),i=f.useState({onScrollCapture:b2,onWheelCapture:b2,onTouchMoveCapture:b2}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=bW(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[h,b],d=function(a){return c.forEach(function(b){return bZ(b,a)})},(e=(0,f.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,g=e.facade,b$(function(){var a=b_.get(g);if(a){var b=new Set(a),d=new Set(c),e=g.current;b.forEach(function(a){d.has(a)||bZ(a,null)}),d.forEach(function(a){b.has(a)||bZ(a,e)})}b_.set(g,c)},[c]),g),A=bV(bV({},y),j);return f.createElement(f.Fragment,null,p&&f.createElement(r,{sideCar:b1,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:h,gapMode:x}),l?f.cloneElement(f.Children.only(m),bV(bV({},A),{ref:z})):f.createElement(void 0===w?"div":w,bV({},A,{className:n,ref:z}),m))});b3.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b3.classNames={fullWidth:bY,zeroRight:bX};var b4=function(a){var b=a.sideCar,c=bW(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return f.createElement(d,bV({},c))};b4.isSideCarExport=!0;var b5=function(){var a=0,b=null;return{add:function(c){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=d||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0);return b&&a.setAttribute("nonce",b),a}())){var e,f;(e=b).styleSheet?e.styleSheet.cssText=c:e.appendChild(document.createTextNode(c)),f=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(f)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},b6=function(){var a=b5();return function(b,c){f.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},b7=function(){var a=b6();return function(b){return a(b.styles,b.dynamic),null}},b8={left:0,top:0,right:0,gap:0},b9=b7(),ca="data-scroll-locked",cb=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(ca,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(bX," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bY," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bX," .").concat(bX," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(bY," .").concat(bY," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(ca,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},cc=function(){var a=parseInt(document.body.getAttribute(ca)||"0",10);return isFinite(a)?a:0},cd=function(){f.useEffect(function(){return document.body.setAttribute(ca,(cc()+1).toString()),function(){var a=cc()-1;a<=0?document.body.removeAttribute(ca):document.body.setAttribute(ca,a.toString())}},[])},ce=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;cd();var g=f.useMemo(function(){return void 0===e,b8},[e]);return f.createElement(b9,{styles:cb(g,!b,e,c?"":"!important")})},cf=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},cg=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),ch(a,d)){var e=ci(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body)return!1},ch=function(a,b){return"v"===a?cf(b,"overflowY"):cf(b,"overflowX")},ci=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},cj=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=ci(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&ch(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i))return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},ck=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},cl=function(a){return[a.deltaX,a.deltaY]},cm=function(a){return a&&"current"in a?a.current:a},cn=0,co=[];let cp=(b=function(a){var b=f.useRef([]),c=f.useRef([0,0]),d=f.useRef(),e=f.useState(cn++)[0],g=f.useState(b7)[0],h=f.useRef(a);f.useEffect(function(){h.current=a},[a]),f.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(cm),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=f.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!h.current.allowPinchZoom;var e,f=ck(a),g=c.current,i="deltaX"in a?a.deltaX:g[0]-f[0],j="deltaY"in a?a.deltaY:g[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=cg(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=cg(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return cj(n,b,a,"h"===n?i:j,!0)},[]),j=f.useCallback(function(a){if(co.length&&co[co.length-1]===g){var c="deltaY"in a?cl(a):ck(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(h.current.shards||[]).map(cm).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!h.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=f.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=f.useCallback(function(a){c.current=ck(a),d.current=void 0},[]),m=f.useCallback(function(b){k(b.type,cl(b),b.target,i(b,a.lockRef.current))},[]),n=f.useCallback(function(b){k(b.type,ck(b),b.target,i(b,a.lockRef.current))},[]);f.useEffect(function(){return co.push(g),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,!1),document.addEventListener("touchmove",j,!1),document.addEventListener("touchstart",l,!1),function(){co=co.filter(function(a){return a!==g}),document.removeEventListener("wheel",j,!1),document.removeEventListener("touchmove",j,!1),document.removeEventListener("touchstart",l,!1)}},[]);var o=a.removeScrollBar,p=a.inert;return f.createElement(f.Fragment,null,p?f.createElement(g,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?f.createElement(ce,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},b1.useMedium(b),b4);var cq=f.forwardRef(function(a,b){return f.createElement(b3,bV({},a,{ref:b,sideCar:cp}))});cq.classNames=b3.classNames;var cr=["Enter"," "],cs=["ArrowUp","PageDown","End"],ct=["ArrowDown","PageUp","Home",...cs],cu={ltr:[...cr,"ArrowRight"],rtl:[...cr,"ArrowLeft"]},cv={ltr:["ArrowLeft"],rtl:["ArrowRight"]},cw="Menu",[cx,cy,cz]=w(cw),[cA,cB]=q(cw,[cz,bf,bF]),cC=bf(),cD=bF(),[cE,cF]=cA(cw),[cG,cH]=cA(cw),cI=a=>{let{__scopeMenu:b,open:c=!1,children:d,dir:g,onOpenChange:h,modal:i=!0}=a,j=cC(b),[k,l]=f.useState(null),m=f.useRef(!1),n=C(h),o=B(g);return f.useEffect(()=>{let a=()=>{m.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>m.current=!1;return document.addEventListener("keydown",a,{capture:!0}),()=>{document.removeEventListener("keydown",a,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),(0,e.jsx)(bi,{...j,children:(0,e.jsx)(cE,{scope:b,open:c,onOpenChange:n,content:k,onContentChange:l,children:(0,e.jsx)(cG,{scope:b,onClose:f.useCallback(()=>n(!1),[n]),isUsingKeyboardRef:m,dir:o,modal:i,children:d})})})};cI.displayName=cw;var cJ=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,f=cC(c);return(0,e.jsx)(bk,{...f,...d,ref:b})});cJ.displayName="MenuAnchor";var cK="MenuPortal",[cL,cM]=cA(cK,{forceMount:void 0}),cN=a=>{let{__scopeMenu:b,forceMount:c,children:d,container:f}=a,g=cF(cK,b);return(0,e.jsx)(cL,{scope:b,forceMount:c,children:(0,e.jsx)(bw,{present:c||g.open,children:(0,e.jsx)(bv,{asChild:!0,container:f,children:d})})})};cN.displayName=cK;var cO="MenuContent",[cP,cQ]=cA(cO),cR=f.forwardRef((a,b)=>{let c=cM(cO,a.__scopeMenu),{forceMount:d=c.forceMount,...f}=a,g=cF(cO,a.__scopeMenu),h=cH(cO,a.__scopeMenu);return(0,e.jsx)(cx.Provider,{scope:a.__scopeMenu,children:(0,e.jsx)(bw,{present:d||g.open,children:(0,e.jsx)(cx.Slot,{scope:a.__scopeMenu,children:h.modal?(0,e.jsx)(cS,{...f,ref:b}):(0,e.jsx)(cT,{...f,ref:b})})})})}),cS=f.forwardRef((a,b)=>{let c=cF(cO,a.__scopeMenu),d=f.useRef(null),g=(0,p.useComposedRefs)(b,d);return f.useEffect(()=>{let a=d.current;if(a)return bU(a)},[]),(0,e.jsx)(cV,{...a,ref:g,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:o(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),cT=f.forwardRef((a,b)=>{let c=cF(cO,a.__scopeMenu);return(0,e.jsx)(cV,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),cU=(0,v.createSlot)("MenuContent.ScrollLock"),cV=f.forwardRef((a,b)=>{let{__scopeMenu:c,loop:d=!1,trapFocus:g,onOpenAutoFocus:h,onCloseAutoFocus:i,disableOutsidePointerEvents:j,onEntryFocus:k,onEscapeKeyDown:l,onPointerDownOutside:m,onFocusOutside:n,onInteractOutside:q,onDismiss:r,disableOutsideScroll:s,...t}=a,u=cF(cO,c),v=cH(cO,c),w=cC(c),x=cD(c),y=cy(c),[z,A]=f.useState(null),B=f.useRef(null),C=(0,p.useComposedRefs)(b,B,u.onContentChange),D=f.useRef(0),E=f.useRef(""),G=f.useRef(0),H=f.useRef(null),K=f.useRef("right"),L=f.useRef(0),M=s?cq:f.Fragment;f.useEffect(()=>()=>window.clearTimeout(D.current),[]),f.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??J()),document.body.insertAdjacentElement("beforeend",a[1]??J()),I++,()=>{1===I&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),I--}},[]);let O=f.useCallback(a=>K.current===H.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,H.current?.area),[]);return(0,e.jsx)(cP,{scope:c,searchRef:E,onItemEnter:f.useCallback(a=>{O(a)&&a.preventDefault()},[O]),onItemLeave:f.useCallback(a=>{O(a)||(B.current?.focus(),A(null))},[O]),onTriggerLeave:f.useCallback(a=>{O(a)&&a.preventDefault()},[O]),pointerGraceTimerRef:G,onPointerGraceIntentChange:f.useCallback(a=>{H.current=a},[]),children:(0,e.jsx)(M,{...s?{as:cU,allowPinchZoom:!0}:void 0,children:(0,e.jsx)(N,{asChild:!0,trapped:g,onMountAutoFocus:o(h,a=>{a.preventDefault(),B.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:(0,e.jsx)(F,{asChild:!0,disableOutsidePointerEvents:j,onEscapeKeyDown:l,onPointerDownOutside:m,onFocusOutside:n,onInteractOutside:q,onDismiss:r,children:(0,e.jsx)(bI,{asChild:!0,...x,dir:v.dir,orientation:"vertical",loop:d,currentTabStopId:z,onCurrentTabStopIdChange:A,onEntryFocus:o(k,a=>{v.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,e.jsx)(bo,{role:"menu","aria-orientation":"vertical","data-state":dj(u.open),"data-radix-menu-content":"",dir:v.dir,...w,...t,ref:C,style:{outline:"none",...t.style},onKeyDown:o(t.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=E.current+a,c=y().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){E.current=b,window.clearTimeout(D.current),""!==b&&(D.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=B.current;if(a.target!==e||!ct.includes(a.key))return;a.preventDefault();let f=y().filter(a=>!a.disabled).map(a=>a.ref.current);cs.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:o(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(D.current),E.current="")}),onPointerMove:o(a.onPointerMove,dm(a=>{let b=a.target,c=L.current!==a.clientX;a.currentTarget.contains(b)&&c&&(K.current=a.clientX>L.current?"right":"left",L.current=a.clientX)}))})})})})})})});cR.displayName=cO;var cW=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,e.jsx)(u.Primitive.div,{role:"group",...d,ref:b})});cW.displayName="MenuGroup";var cX=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,e.jsx)(u.Primitive.div,{...d,ref:b})});cX.displayName="MenuLabel";var cY="MenuItem",cZ="menu.itemSelect",c$=f.forwardRef((a,b)=>{let{disabled:c=!1,onSelect:d,...g}=a,h=f.useRef(null),i=cH(cY,a.__scopeMenu),j=cQ(cY,a.__scopeMenu),k=(0,p.useComposedRefs)(b,h),l=f.useRef(!1);return(0,e.jsx)(c_,{...g,ref:k,disabled:c,onClick:o(a.onClick,()=>{let a=h.current;if(!c&&a){let b=new CustomEvent(cZ,{bubbles:!0,cancelable:!0});a.addEventListener(cZ,a=>d?.(a),{once:!0}),(0,u.dispatchDiscreteCustomEvent)(a,b),b.defaultPrevented?l.current=!1:i.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),l.current=!0},onPointerUp:o(a.onPointerUp,a=>{l.current||a.currentTarget?.click()}),onKeyDown:o(a.onKeyDown,a=>{let b=""!==j.searchRef.current;c||b&&" "===a.key||cr.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});c$.displayName=cY;var c_=f.forwardRef((a,b)=>{let{__scopeMenu:c,disabled:d=!1,textValue:g,...h}=a,i=cQ(cY,c),j=cD(c),k=f.useRef(null),l=(0,p.useComposedRefs)(b,k),[m,n]=f.useState(!1),[q,r]=f.useState("");return f.useEffect(()=>{let a=k.current;a&&r((a.textContent??"").trim())},[h.children]),(0,e.jsx)(cx.ItemSlot,{scope:c,disabled:d,textValue:g??q,children:(0,e.jsx)(bL,{asChild:!0,...j,focusable:!d,children:(0,e.jsx)(u.Primitive.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":d||void 0,"data-disabled":d?"":void 0,...h,ref:l,onPointerMove:o(a.onPointerMove,dm(a=>{d?i.onItemLeave(a):(i.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:o(a.onPointerLeave,dm(a=>i.onItemLeave(a))),onFocus:o(a.onFocus,()=>n(!0)),onBlur:o(a.onBlur,()=>n(!1))})})})}),c0=f.forwardRef((a,b)=>{let{checked:c=!1,onCheckedChange:d,...f}=a;return(0,e.jsx)(c8,{scope:a.__scopeMenu,checked:c,children:(0,e.jsx)(c$,{role:"menuitemcheckbox","aria-checked":dk(c)?"mixed":c,...f,ref:b,"data-state":dl(c),onSelect:o(f.onSelect,()=>d?.(!!dk(c)||!c),{checkForDefaultPrevented:!1})})})});c0.displayName="MenuCheckboxItem";var c1="MenuRadioGroup",[c2,c3]=cA(c1,{value:void 0,onValueChange:()=>{}}),c4=f.forwardRef((a,b)=>{let{value:c,onValueChange:d,...f}=a,g=C(d);return(0,e.jsx)(c2,{scope:a.__scopeMenu,value:c,onValueChange:g,children:(0,e.jsx)(cW,{...f,ref:b})})});c4.displayName=c1;var c5="MenuRadioItem",c6=f.forwardRef((a,b)=>{let{value:c,...d}=a,f=c3(c5,a.__scopeMenu),g=c===f.value;return(0,e.jsx)(c8,{scope:a.__scopeMenu,checked:g,children:(0,e.jsx)(c$,{role:"menuitemradio","aria-checked":g,...d,ref:b,"data-state":dl(g),onSelect:o(d.onSelect,()=>f.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});c6.displayName=c5;var c7="MenuItemIndicator",[c8,c9]=cA(c7,{checked:!1}),da=f.forwardRef((a,b)=>{let{__scopeMenu:c,forceMount:d,...f}=a,g=c9(c7,c);return(0,e.jsx)(bw,{present:d||dk(g.checked)||!0===g.checked,children:(0,e.jsx)(u.Primitive.span,{...f,ref:b,"data-state":dl(g.checked)})})});da.displayName=c7;var db=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,e.jsx)(u.Primitive.div,{role:"separator","aria-orientation":"horizontal",...d,ref:b})});db.displayName="MenuSeparator";var dc=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,f=cC(c);return(0,e.jsx)(br,{...f,...d,ref:b})});dc.displayName="MenuArrow";var[dd,de]=cA("MenuSub"),df="MenuSubTrigger",dg=f.forwardRef((a,b)=>{let c=cF(df,a.__scopeMenu),d=cH(df,a.__scopeMenu),g=de(df,a.__scopeMenu),h=cQ(df,a.__scopeMenu),i=f.useRef(null),{pointerGraceTimerRef:j,onPointerGraceIntentChange:k}=h,l={__scopeMenu:a.__scopeMenu},m=f.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return f.useEffect(()=>m,[m]),f.useEffect(()=>{let a=j.current;return()=>{window.clearTimeout(a),k(null)}},[j,k]),(0,e.jsx)(cJ,{asChild:!0,...l,children:(0,e.jsx)(c_,{id:g.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":g.contentId,"data-state":dj(c.open),...a,ref:(0,p.composeRefs)(b,g.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:o(a.onPointerMove,dm(b=>{h.onItemEnter(b),!b.defaultPrevented&&(a.disabled||c.open||i.current||(h.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{c.onOpenChange(!0),m()},100)))})),onPointerLeave:o(a.onPointerLeave,dm(a=>{m();let b=c.content?.getBoundingClientRect();if(b){let d=c.content?.dataset.side,e="right"===d,f=b[e?"left":"right"],g=b[e?"right":"left"];h.onPointerGraceIntentChange({area:[{x:a.clientX+(e?-5:5),y:a.clientY},{x:f,y:b.top},{x:g,y:b.top},{x:g,y:b.bottom},{x:f,y:b.bottom}],side:d}),window.clearTimeout(j.current),j.current=window.setTimeout(()=>h.onPointerGraceIntentChange(null),300)}else{if(h.onTriggerLeave(a),a.defaultPrevented)return;h.onPointerGraceIntentChange(null)}})),onKeyDown:o(a.onKeyDown,b=>{let e=""!==h.searchRef.current;a.disabled||e&&" "===b.key||cu[d.dir].includes(b.key)&&(c.onOpenChange(!0),c.content?.focus(),b.preventDefault())})})})});dg.displayName=df;var dh="MenuSubContent",di=f.forwardRef((a,b)=>{let c=cM(cO,a.__scopeMenu),{forceMount:d=c.forceMount,...g}=a,h=cF(cO,a.__scopeMenu),i=cH(cO,a.__scopeMenu),j=de(dh,a.__scopeMenu),k=f.useRef(null),l=(0,p.useComposedRefs)(b,k);return(0,e.jsx)(cx.Provider,{scope:a.__scopeMenu,children:(0,e.jsx)(bw,{present:d||h.open,children:(0,e.jsx)(cx.Slot,{scope:a.__scopeMenu,children:(0,e.jsx)(cV,{id:j.contentId,"aria-labelledby":j.triggerId,...g,ref:l,align:"start",side:"rtl"===i.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{i.isUsingKeyboardRef.current&&k.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:o(a.onFocusOutside,a=>{a.target!==j.trigger&&h.onOpenChange(!1)}),onEscapeKeyDown:o(a.onEscapeKeyDown,a=>{i.onClose(),a.preventDefault()}),onKeyDown:o(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=cv[i.dir].includes(a.key);b&&c&&(h.onOpenChange(!1),j.trigger?.focus(),a.preventDefault())})})})})})});function dj(a){return a?"open":"closed"}function dk(a){return"indeterminate"===a}function dl(a){return dk(a)?"indeterminate":a?"checked":"unchecked"}function dm(a){return b=>"mouse"===b.pointerType?a(b):void 0}di.displayName=dh;var dn="DropdownMenu",[dp,dq]=q(dn,[cB]),dr=cB(),[ds,dt]=dp(dn),du=a=>{let{__scopeDropdownMenu:b,children:c,dir:d,open:g,defaultOpen:h,onOpenChange:i,modal:j=!0}=a,k=dr(b),l=f.useRef(null),[m,n]=t({prop:g,defaultProp:h??!1,onChange:i,caller:dn});return(0,e.jsx)(ds,{scope:b,triggerId:V(),triggerRef:l,contentId:V(),open:m,onOpenChange:n,onOpenToggle:f.useCallback(()=>n(a=>!a),[n]),modal:j,children:(0,e.jsx)(cI,{...k,open:m,onOpenChange:n,dir:d,modal:j,children:c})})};du.displayName=dn;var dv="DropdownMenuTrigger",dw=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,disabled:d=!1,...f}=a,g=dt(dv,c),h=dr(c);return(0,e.jsx)(cJ,{asChild:!0,...h,children:(0,e.jsx)(u.Primitive.button,{type:"button",id:g.triggerId,"aria-haspopup":"menu","aria-expanded":g.open,"aria-controls":g.open?g.contentId:void 0,"data-state":g.open?"open":"closed","data-disabled":d?"":void 0,disabled:d,...f,ref:(0,p.composeRefs)(b,g.triggerRef),onPointerDown:o(a.onPointerDown,a=>{!d&&0===a.button&&!1===a.ctrlKey&&(g.onOpenToggle(),g.open||a.preventDefault())}),onKeyDown:o(a.onKeyDown,a=>{!d&&(["Enter"," "].includes(a.key)&&g.onOpenToggle(),"ArrowDown"===a.key&&g.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});dw.displayName=dv;var dx=a=>{let{__scopeDropdownMenu:b,...c}=a,d=dr(b);return(0,e.jsx)(cN,{...d,...c})};dx.displayName="DropdownMenuPortal";var dy="DropdownMenuContent",dz=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,g=dt(dy,c),h=dr(c),i=f.useRef(!1);return(0,e.jsx)(cR,{id:g.contentId,"aria-labelledby":g.triggerId,...h,...d,ref:b,onCloseAutoFocus:o(a.onCloseAutoFocus,a=>{i.current||g.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:o(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!g.modal||d)&&(i.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});dz.displayName=dy,f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(cW,{...f,...d,ref:b})}).displayName="DropdownMenuGroup";var dA=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(cX,{...f,...d,ref:b})});dA.displayName="DropdownMenuLabel";var dB=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(c$,{...f,...d,ref:b})});dB.displayName="DropdownMenuItem";var dC=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(c0,{...f,...d,ref:b})});dC.displayName="DropdownMenuCheckboxItem",f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(c4,{...f,...d,ref:b})}).displayName="DropdownMenuRadioGroup";var dD=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(c6,{...f,...d,ref:b})});dD.displayName="DropdownMenuRadioItem";var dE=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(da,{...f,...d,ref:b})});dE.displayName="DropdownMenuItemIndicator";var dF=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(db,{...f,...d,ref:b})});dF.displayName="DropdownMenuSeparator",f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(dc,{...f,...d,ref:b})}).displayName="DropdownMenuArrow";var dG=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(dg,{...f,...d,ref:b})});dG.displayName="DropdownMenuSubTrigger";var dH=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=dr(c);return(0,e.jsx)(di,{...f,...d,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});dH.displayName="DropdownMenuSubContent";let dI=(0,j.default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var dJ=a.i(42879);let dK=(0,j.default)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var dL=a.i(18688);f.forwardRef(({className:a,inset:b,children:c,...d},f)=>(0,e.jsxs)(dG,{ref:f,className:(0,dL.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",b&&"pl-8",a),...d,children:[c,(0,e.jsx)(dJ.ChevronRight,{className:"ml-auto h-4 w-4"})]})).displayName=dG.displayName,f.forwardRef(({className:a,...b},c)=>(0,e.jsx)(dH,{ref:c,className:(0,dL.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...b})).displayName=dH.displayName;let dM=f.forwardRef(({className:a,sideOffset:b=4,...c},d)=>(0,e.jsx)(dx,{children:(0,e.jsx)(dz,{ref:d,sideOffset:b,className:(0,dL.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...c})}));dM.displayName=dz.displayName;let dN=f.forwardRef(({className:a,inset:b,...c},d)=>(0,e.jsx)(dB,{ref:d,className:(0,dL.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",b&&"pl-8",a),...c}));function dO(){let{theme:a,actualTheme:b,mounted:c,setTheme:d}=(0,n.useTheme)();return(0,e.jsxs)(du,{children:[(0,e.jsx)(dw,{asChild:!0,children:(0,e.jsxs)(i.Button,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{c&&d("light"===b?"dark":"light")},title:`Switch to ${"light"===b?"dark":"light"} mode`,children:[(0,e.jsx)(l,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,e.jsx)(k,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,e.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,e.jsxs)(dM,{align:"end",children:[(0,e.jsxs)(dN,{onClick:()=>d("light"),children:[(0,e.jsx)(l,{className:"mr-2 h-4 w-4"}),(0,e.jsx)("span",{children:"Light"})]}),(0,e.jsxs)(dN,{onClick:()=>d("dark"),children:[(0,e.jsx)(k,{className:"mr-2 h-4 w-4"}),(0,e.jsx)("span",{children:"Dark"})]}),(0,e.jsxs)(dN,{onClick:()=>d("system"),children:[(0,e.jsx)(m,{className:"mr-2 h-4 w-4"}),(0,e.jsx)("span",{children:"System"})]})]})]})}dN.displayName=dB.displayName,f.forwardRef(({className:a,children:b,checked:c,...d},f)=>(0,e.jsxs)(dC,{ref:f,className:(0,dL.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:c,...d,children:[(0,e.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,e.jsx)(dE,{children:(0,e.jsx)(dI,{className:"h-4 w-4"})})}),b]})).displayName=dC.displayName,f.forwardRef(({className:a,children:b,...c},d)=>(0,e.jsxs)(dD,{ref:d,className:(0,dL.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,e.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,e.jsx)(dE,{children:(0,e.jsx)(dK,{className:"h-2 w-2 fill-current"})})}),b]})).displayName=dD.displayName,f.forwardRef(({className:a,inset:b,...c},d)=>(0,e.jsx)(dA,{ref:d,className:(0,dL.cn)("px-2 py-1.5 text-sm font-semibold",b&&"pl-8",a),...c})).displayName=dA.displayName,f.forwardRef(({className:a,...b},c)=>(0,e.jsx)(dF,{ref:c,className:(0,dL.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=dF.displayName;let dP=(0,j.default)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var dQ=a.i(11073),dR=a.i(59844);let dS=(0,j.default)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),dT=(0,j.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);a.s(["Bell",()=>dU],74751);let dU=(0,j.default)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);var dV=a.i(15579);a.s(["School",()=>dW],82193);let dW=(0,j.default)("school",[["path",{d:"M14 21v-3a2 2 0 0 0-4 0v3",key:"1rgiei"}],["path",{d:"M18 5v16",key:"1ethyx"}],["path",{d:"m4 6 7.106-3.79a2 2 0 0 1 1.788 0L20 6",key:"zywc2d"}],["path",{d:"m6 11-3.52 2.147a1 1 0 0 0-.48.854V19a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-5a1 1 0 0 0-.48-.853L18 11",key:"1d4ql0"}],["path",{d:"M6 5v16",key:"1sn0nx"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]]);var dX=a.i(37906),dY=a.i(62867),dZ=a.i(14401);a.s(["Users",()=>d$],91662);let d$=(0,j.default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);a.s(["BookOpen",()=>d_],92761);let d_=(0,j.default)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);a.s(["GraduationCap",()=>d0],77233);let d0=(0,j.default)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);var d1=a.i(78402);a.s(["BarChart3",()=>d2],3815);let d2=(0,j.default)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);a.s(["Calendar",()=>d3],72613);let d3=(0,j.default)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var d4=a.i(40770),d5=a.i(57012);a.s(["ClipboardList",()=>d6],99739);let d6=(0,j.default)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);a.s(["Award",()=>d7],32541);let d7=(0,j.default)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),d8={Plus:dX.Plus,Upload:dY.Upload,Download:dZ.Download,Users:d$,BookOpen:d_,GraduationCap:d0,FileText:d1.FileText,BarChart3:d2,Calendar:d3,Home:d4.Home,Settings:dT,Bell:dU,User:dR.User,Edit:d5.Edit,ClipboardList:d6,Award:d7};function d9({children:a,title:b,navigation:c}){let{data:d}=(0,g.useSession)(),j=(0,h.useRouter)(),[k,l]=(0,f.useState)(!1),m=async()=>{await (0,g.signOut)({callbackUrl:"/"})},n=a=>d8[a]||d4.Home;return(0,e.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-950",children:[(0,e.jsxs)("div",{className:`fixed inset-0 z-50 lg:hidden ${k?"block":"hidden"}`,children:[(0,e.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>l(!1)}),(0,e.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900",children:[(0,e.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)(dW,{className:"h-8 w-8 text-blue-600"}),(0,e.jsx)("span",{className:"ml-2 text-lg font-semibold",children:"SMS"})]}),(0,e.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>l(!1),children:(0,e.jsx)(dQ.X,{className:"h-5 w-5"})})]}),(0,e.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:c.map(a=>{let b=n(a.icon);return(0,e.jsxs)(i.Button,{variant:"ghost",className:"w-full justify-start",onClick:()=>{j.push(a.href),l(!1)},children:[(0,e.jsx)(b,{className:"mr-3 h-5 w-5"}),a.name]},`mobile-${a.name}`)})})]})]}),(0,e.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,e.jsxs)("div",{className:"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800",children:[(0,e.jsxs)("div",{className:"flex h-16 items-center px-4",children:[(0,e.jsx)(dW,{className:"h-8 w-8 text-blue-600"}),(0,e.jsx)("span",{className:"ml-2 text-lg font-semibold hidden xl:inline",children:"School Management System"}),(0,e.jsx)("span",{className:"ml-2 text-lg font-semibold xl:hidden",children:"SMS"})]}),(0,e.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:c.map(a=>{let b=n(a.icon);return(0,e.jsxs)(i.Button,{variant:"ghost",className:"w-full justify-start",onClick:()=>j.push(a.href),children:[(0,e.jsx)(b,{className:"mr-3 h-5 w-5"}),a.name]},`desktop-${a.name}`)})})]})}),(0,e.jsxs)("div",{className:"lg:pl-64",children:[(0,e.jsxs)("div",{className:"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8",children:[(0,e.jsx)(i.Button,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>l(!0),children:(0,e.jsx)(dP,{className:"h-5 w-5"})}),(0,e.jsx)("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6",children:(0,e.jsxs)("div",{className:"relative flex flex-1",children:[(0,e.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,e.jsx)(dV.Search,{className:"h-5 w-5 text-gray-400"})}),(0,e.jsx)("input",{type:"text",placeholder:"Search...",className:"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent"})]})}),(0,e.jsxs)("div",{className:"flex items-center gap-x-4 lg:gap-x-6",children:[(0,e.jsx)(dO,{}),(0,e.jsx)(i.Button,{variant:"ghost",size:"sm",children:(0,e.jsx)(dU,{className:"h-5 w-5"})}),(0,e.jsx)("div",{className:"relative",children:(0,e.jsxs)("div",{className:"flex items-center gap-x-3",children:[(0,e.jsxs)("div",{className:"text-sm hidden sm:block",children:[(0,e.jsxs)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:[d?.user?.firstName," ",d?.user?.lastName]}),(0,e.jsx)("p",{className:"text-gray-500 dark:text-gray-400 capitalize",children:d?.user?.role?.toLowerCase()})]}),(0,e.jsx)("div",{className:"flex items-center gap-x-2",children:(0,e.jsxs)(i.Button,{variant:"ghost",size:"sm",onClick:m,className:"flex items-center gap-2",children:[(0,e.jsx)(dS,{className:"h-4 w-4"}),(0,e.jsx)("span",{className:"hidden sm:inline",children:"Sign Out"})]})})]})})]})]}),(0,e.jsx)("main",{className:"py-6",children:(0,e.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,e.jsx)("div",{className:"mb-6",children:(0,e.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:b})}),a]})})]})]})}}];

//# sourceMappingURL=school-management-system_b21a3587._.js.map