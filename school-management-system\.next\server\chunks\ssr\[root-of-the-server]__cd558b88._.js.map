{"version": 3, "sources": ["turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/school-management-system/src/components/providers/theme-provider.tsx", "turbopack:///[project]/school-management-system/src/components/providers/session-provider.tsx"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "'use client'\n\nimport { createContext, useContext, useEffect, useState, ReactNode } from 'react'\n\ntype Theme = 'light' | 'dark' | 'system'\n\ninterface ThemeContextType {\n  theme: Theme\n  setTheme: (theme: Theme) => void\n  actualTheme: 'light' | 'dark'\n  mounted: boolean\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined)\n\nexport function useTheme() {\n  const context = useContext(ThemeContext)\n  if (!context) {\n    // Return default values instead of throwing error during SSR\n    return {\n      theme: 'system' as Theme,\n      setTheme: () => {},\n      actualTheme: 'light' as 'light' | 'dark',\n      mounted: false\n    }\n  }\n  return context\n}\n\ninterface ThemeProviderProps {\n  children: ReactNode\n}\n\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  const [theme, setTheme] = useState<Theme>('system')\n  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light')\n  const [mounted, setMounted] = useState(false)\n\n  // Debug theme changes\n  const setThemeWithLogging = (newTheme: Theme) => {\n    console.log('ThemeProvider: setTheme called with:', newTheme)\n    setTheme(newTheme)\n  }\n\n  useEffect(() => {\n    setMounted(true)\n    // Load theme from localStorage on mount\n    const savedTheme = localStorage.getItem('theme') as Theme\n    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {\n      setTheme(savedTheme)\n    } else {\n      // Default to light theme if no saved theme\n      setTheme('light')\n    }\n  }, [])\n\n  useEffect(() => {\n    console.log('ThemeProvider: theme effect triggered', { theme, mounted })\n\n    if (!mounted) return\n\n    const root = window.document.documentElement\n\n    // Remove existing theme classes\n    root.classList.remove('light', 'dark')\n\n    let effectiveTheme: 'light' | 'dark'\n\n    if (theme === 'system') {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'\n      effectiveTheme = systemTheme\n    } else {\n      effectiveTheme = theme\n    }\n\n    console.log('ThemeProvider: applying theme', { theme, effectiveTheme })\n\n    // Apply the theme\n    root.classList.add(effectiveTheme)\n    setActualTheme(effectiveTheme)\n\n    console.log('ThemeProvider: DOM classes after applying theme:', root.className)\n\n    // Save to localStorage\n    localStorage.setItem('theme', theme)\n  }, [theme, mounted])\n\n  useEffect(() => {\n    if (!mounted) return\n\n    // Listen for system theme changes when theme is set to 'system'\n    if (theme === 'system') {\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n\n      const handleChange = (e: MediaQueryListEvent) => {\n        const root = window.document.documentElement\n        root.classList.remove('light', 'dark')\n        const systemTheme = e.matches ? 'dark' : 'light'\n        root.classList.add(systemTheme)\n        setActualTheme(systemTheme)\n      }\n\n      mediaQuery.addEventListener('change', handleChange)\n      return () => mediaQuery.removeEventListener('change', handleChange)\n    }\n  }, [theme, mounted])\n\n  const value = {\n    theme,\n    setTheme: setThemeWithLogging,\n    actualTheme,\n    mounted,\n  }\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  )\n}\n", "'use client'\r\n\r\nimport { SessionProvider } from 'next-auth/react'\r\nimport { ReactNode } from 'react'\r\nimport { ThemeProvider } from '@/components/providers/theme-provider'\r\n\r\ninterface Props {\r\n  children: ReactNode\r\n}\r\n\r\nexport default function AuthSessionProvider({ children }: Props) {\r\n  return (\r\n    <SessionProvider>\r\n      <ThemeProvider>\r\n        {children}\r\n      </ThemeProvider>\r\n    </SessionProvider>\r\n  )\r\n}\r\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "vendored", "ReactJsxRuntime", "React"], "mappings": "0NA0BQG,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,iCC1BjCF,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEC,eAAe,+BCFxCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEE,KAAK,wFCA9B,EAAA,EAAA,CAAA,CAAA,OAWA,IAAM,EAAe,CAAA,EAAA,EAAA,aAAA,AAAa,OAA+B,GAE1D,SAAS,IACd,IAAM,EAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,UACtB,AAAL,GAES,CACL,AAHA,KAAU,CAGH,SACP,SAAU,KAAO,EACjB,YAAa,QACb,SAAS,CACX,CAGJ,CAMO,SAAS,EAAc,UAAE,CAAQ,CAAsB,EAC5D,GAAM,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAQ,UACpC,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAmB,SAC3D,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,SAQvC,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,GAAW,GAEX,IAAM,EAAa,aAAa,OAAO,CAAC,SACpC,GAAc,CAAC,QAAS,OAAQ,SAAS,CAAC,QAAQ,CAAC,GACrD,EAAS,GAGT,EAAS,GAJyD,KAMtE,EAAG,EAAE,EAEL,CAAA,EAAA,EAAA,SAAS,AAAT,EAAU,SAUJ,EAPJ,GAFA,QAAQ,GAAG,CAAC,wCAAyC,OAAE,UAAO,CAAQ,GAElE,CAAC,EAAS,OAEd,IAAM,EAAO,OAAO,QAAQ,CAAC,eAAe,CAG5C,EAAK,SAAS,CAAC,MAAM,CAAC,QAAS,QAM7B,EAFE,AAAU,UAAU,GACF,EACH,KADU,UAAU,CAAC,gCAAgC,OAAO,CAAG,OAAS,QAGxE,EAGnB,QAAQ,GAAG,CAAC,gCAAiC,CAAE,uBAAO,CAAe,GAGrE,EAAK,SAAS,CAAC,GAAG,CAAC,GACnB,EAAe,GAEf,QAAQ,GAAG,CAAC,mDAAoD,EAAK,SAAS,EAG9E,aAAa,OAAO,CAAC,QAAS,EAChC,EAAG,CAAC,EAAO,EAAQ,EAEnB,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,GAAK,CAAD,EAGU,MAHA,KAGV,EAAoB,CACtB,IAAM,EAAa,OAAO,UAAU,CAAC,gCAE/B,EAAe,AAAC,IACpB,IAAM,EAAO,OAAO,QAAQ,CAAC,eAAe,CAC5C,EAAK,SAAS,CAAC,MAAM,CAAC,QAAS,QAC/B,IAAM,EAAc,EAAE,OAAO,CAAG,OAAS,QACzC,EAAK,SAAS,CAAC,GAAG,CAAC,GACnB,EAAe,EACjB,EAGA,OADA,EAAW,gBAAgB,CAAC,SAAU,GAC/B,IAAM,EAAW,mBAAmB,CAAC,SAAU,EACxD,CACF,EAAG,CAAC,EAAO,EAAQ,EAUjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAa,QAAQ,CAAA,CAAC,MARX,CAQkB,MAP9B,EACA,SAtE0B,AAAC,CAsEjB,GArEV,QAAQ,GAAG,CAAC,uCAAwC,GACpD,EAAS,EACX,cAoEE,UACA,CACF,WAIK,GAGP,kECrHA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OAMe,SAAS,EAAoB,UAAE,CAAQ,CAAS,EAC7D,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACX,KAIT", "ignoreList": [0, 1, 2]}