import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testFixedAdminStats() {
  try {
    console.log('🔍 Testing fixed admin dashboard stats queries...')
    
    // Test the complete stats query as it would be in the API
    const [
      totalStudents,
      totalTeachers,
      totalClasses,
      totalSubjects,
      attendanceStats,
      marksStats,
      activeStudents,
      activeTeachers
    ] = await Promise.all([
      // Total students count
      prisma.student.count(),
      
      // Total teachers count
      prisma.teacher.count(),
      
      // Total classes count
      prisma.class.count(),
      
      // Total subjects count
      prisma.subject.count(),
      
      // Attendance statistics (last 30 days)
      prisma.attendance.aggregate({
        where: {
          date: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        _count: {
          id: true
        }
      }).then(async (total) => {
        const present = await prisma.attendance.count({
          where: {
            date: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            status: 'PRESENT'
          }
        })
        return {
          total: total._count.id,
          present,
          rate: total._count.id > 0 ? (present / total._count.id) * 100 : 0
        }
      }),
      
      // Marks statistics (average marks)
      prisma.mark.aggregate({
        _avg: {
          obtainedMarks: true
        },
        _count: {
          id: true
        }
      }),
      
      // Active students (students with recent attendance)
      prisma.student.count({
        where: {
          attendances: {
            some: {
              date: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
              }
            }
          }
        }
      }),
      
      // Active teachers (teachers who took attendance recently)
      prisma.teacher.count({
        where: {
          attendances: {
            some: {
              date: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
              }
            }
          }
        }
      })
    ])

    // Calculate additional statistics
    const stats = {
      totalStudents,
      totalTeachers,
      totalClasses,
      totalSubjects,
      activeStudents,
      activeTeachers,
      attendanceRate: Math.round(attendanceStats.rate * 10) / 10, // Round to 1 decimal
      averageMarks: marksStats._avg.obtainedMarks 
        ? Math.round(marksStats._avg.obtainedMarks * 10) / 10 
        : 0,
      totalMarksRecords: marksStats._count.id,
      totalAttendanceRecords: attendanceStats.total
    }

    console.log('✅ All queries successful!')
    console.log('📊 Dashboard Stats:', JSON.stringify(stats, null, 2))
    
  } catch (error) {
    console.error('❌ Error testing fixed admin stats:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testFixedAdminStats()
