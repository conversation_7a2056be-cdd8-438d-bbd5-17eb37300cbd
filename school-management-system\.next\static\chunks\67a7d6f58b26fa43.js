(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18498,e=>{"use strict";e.s(["Home",()=>r],18498);let r=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},18125,(e,r,t)=>{r.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>n,"CardContent",()=>o,"CardDescription",()=>s,"CardHeader",()=>l,"CardTitle",()=>i]);var r=e.i(53379),t=e.i(46686),a=e.i(36946);let n=t.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",n),...l})});n.displayName="Card";let l=t.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",n),...l})});l.displayName="CardHeader";let i=t.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",n),...l})});i.displayName="CardTitle";let s=t.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",n),...l})});s.displayName="CardDescription";let o=t.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",n),...l})});o.displayName="CardContent",t.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",n),...l})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>h],30151);var r=e.i(53379),t=e.i(46686);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e=>{let t=!1,n=r.map(r=>{let n=a(r,e);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let e=0;e<n.length;e++){let t=n[e];"function"==typeof t?t():a(r[e],null)}}}}function l(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return t.useCallback(n(...r),r)}function i(e){let a=function(e){let r=t.forwardRef((e,r)=>{let{children:a,...l}=e;if(t.isValidElement(a)){var i,s,o;let e,d,c=(d=(e=null==(s=Object.getOwnPropertyDescriptor((i=a).props,"ref"))?void 0:s.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(d=(e=null==(o=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:o.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,u=function(e,r){let t={...r};for(let a in r){let n=e[a],l=r[a];/^on[A-Z]/.test(a)?n&&l?t[a]=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];let a=l(...r);return n(...r),a}:n&&(t[a]=n):"style"===a?t[a]={...n,...l}:"className"===a&&(t[a]=[n,l].filter(Boolean).join(" "))}return{...e,...t}}(l,a.props);return a.type!==t.Fragment&&(u.ref=r?n(r,c):c),t.cloneElement(a,u)}return t.Children.count(a)>1?t.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),l=t.forwardRef((e,n)=>{let{children:l,...i}=e,s=t.Children.toArray(l),o=s.find(d);if(o){let e=o.props.children,l=s.map(r=>r!==o?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,r.jsx)(a,{...i,ref:n,children:t.isValidElement(e)?t.cloneElement(e,void 0,l):null})}return(0,r.jsx)(a,{...i,ref:n,children:l})});return l.displayName="".concat(e,".Slot"),l}e.s(["Slot",()=>s,"createSlot",()=>i],88338),e.s(["composeRefs",()=>n,"useComposedRefs",()=>l],35952);var s=i("Slot"),o=Symbol("radix.slottable");function d(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var c=e.i(94323),u=e.i(36946);let f=(0,c.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),h=t.forwardRef((e,t)=>{let{className:a,variant:n,size:l,asChild:i=!1,...o}=e;return(0,r.jsx)(i?s:"button",{className:(0,u.cn)(f({variant:n,size:l,className:a})),ref:t,...o})});h.displayName="Button"},4741,e=>{"use strict";e.s(["default",()=>i],4741);var r=e.i(46686);let t=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},a=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((e,t)=>{let{color:l="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...f}=e;return(0,r.createElement)("svg",{ref:t,...n,width:i,height:i,stroke:l,strokeWidth:o?24*Number(s)/Number(i):s,className:a("lucide",d),...!c&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(f)&&{"aria-hidden":"true"},...f},[...u.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(c)?c:[c]])}),i=(e,n)=>{let i=(0,r.forwardRef)((i,s)=>{let{className:o,...d}=i;return(0,r.createElement)(l,{ref:s,iconNode:n,className:a("lucide-".concat(t(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),o),...d})});return i.displayName=t(e),i}},7254,e=>{"use strict";e.s(["default",()=>d],7254);var r=e.i(53379),t=e.i(18125),a=e.i(30151),n=e.i(32668),l=e.i(4741);let i=(0,l.default)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var s=e.i(18498);let o=(0,l.default)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function d(){let e=(0,t.useRouter)();return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 p-4",children:(0,r.jsxs)(n.Card,{className:"w-full max-w-md text-center",children:[(0,r.jsxs)(n.CardHeader,{children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)(i,{className:"h-16 w-16 text-red-600"})}),(0,r.jsx)(n.CardTitle,{className:"text-2xl font-bold text-gray-900",children:"Access Denied"}),(0,r.jsx)(n.CardDescription,{children:"You don't have permission to access this page"})]}),(0,r.jsxs)(n.CardContent,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600",children:"The page you're trying to access requires specific permissions that your account doesn't have."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,r.jsxs)(a.Button,{onClick:()=>e.push("/"),variant:"outline",className:"flex-1",children:[(0,r.jsx)(s.Home,{className:"h-4 w-4 mr-2"}),"Go Home"]}),(0,r.jsxs)(a.Button,{onClick:()=>e.push("/login"),className:"flex-1",children:[(0,r.jsx)(o,{className:"h-4 w-4 mr-2"}),"Sign In"]})]})]})]})})}}]);