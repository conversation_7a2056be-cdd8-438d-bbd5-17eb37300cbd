import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testAdminStats() {
  try {
    console.log('🔍 Testing admin dashboard stats queries...')
    
    // Test basic counts (these should work)
    console.log('Testing basic counts...')
    const totalStudents = await prisma.student.count()
    const totalTeachers = await prisma.teacher.count()
    const totalClasses = await prisma.class.count()
    const totalSubjects = await prisma.subject.count()
    
    console.log(`✅ Basic counts work:`)
    console.log(`  Students: ${totalStudents}`)
    console.log(`  Teachers: ${totalTeachers}`)
    console.log(`  Classes: ${totalClasses}`)
    console.log(`  Subjects: ${totalSubjects}`)
    
    // Test attendance stats
    console.log('\nTesting attendance stats...')
    const attendanceStats = await prisma.attendance.aggregate({
      where: {
        date: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      _count: {
        id: true
      }
    }).then(async (total) => {
      const present = await prisma.attendance.count({
        where: {
          date: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          },
          status: 'PRESENT'
        }
      })
      return {
        total: total._count.id,
        present,
        rate: total._count.id > 0 ? (present / total._count.id) * 100 : 0
      }
    })
    
    console.log(`✅ Attendance stats work: ${JSON.stringify(attendanceStats)}`)
    
    // Test marks stats
    console.log('\nTesting marks stats...')
    const marksStats = await prisma.mark.aggregate({
      _avg: {
        obtainedMarks: true
      },
      _count: {
        id: true
      }
    })
    
    console.log(`✅ Marks stats work: ${JSON.stringify(marksStats)}`)
    
    // Test the problematic queries with lastLoginAt
    console.log('\nTesting active students query (this might fail)...')
    try {
      const activeStudents = await prisma.student.count({
        where: {
          user: {
            lastLoginAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          }
        }
      })
      console.log(`✅ Active students: ${activeStudents}`)
    } catch (error) {
      console.log(`❌ Active students query failed: ${error.message}`)
    }
    
    console.log('\nTesting active teachers query (this might fail)...')
    try {
      const activeTeachers = await prisma.teacher.count({
        where: {
          user: {
            lastLoginAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          }
        }
      })
      console.log(`✅ Active teachers: ${activeTeachers}`)
    } catch (error) {
      console.log(`❌ Active teachers query failed: ${error.message}`)
    }
    
  } catch (error) {
    console.error('❌ Error testing admin stats:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAdminStats()
