module.exports=[36870,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(54159),c=a.i(81453),d=a.i(54472),e=a.i(41825),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},81453,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactDOM},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},59844,a=>{"use strict";a.s(["User",()=>b],59844);let b=(0,a.i(32639).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},32639,a=>{"use strict";a.s(["default",()=>g],32639);var b=a.i(54159);let c=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},d=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(0,b.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:f=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,b.createElement)("svg",{ref:l,...e,width:c,height:c,stroke:a,strokeWidth:g?24*Number(f)/Number(c):f,className:d("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,c])=>(0,b.createElement)(a,c)),...Array.isArray(i)?i:[i]])),g=(a,e)=>{let g=(0,b.forwardRef)(({className:g,...h},i)=>(0,b.createElement)(f,{ref:i,iconNode:e,className:d(`lucide-${c(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,g),...h}));return g.displayName=c(a),g}},40770,a=>{"use strict";a.s(["Home",()=>b],40770);let b=(0,a.i(32639).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},11090,a=>{"use strict";a.s(["Label",()=>i],11090);var b=a.i(41825),c=a.i(54159),d=a.i(36870),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(24311),g=a.i(18688);let h=(0,f.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e,{ref:d,className:(0,g.cn)(h(),a),...c}));i.displayName=e.displayName},5492,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},75422,a=>{"use strict";a.s(["Alert",()=>g,"AlertDescription",()=>h]);var b=a.i(41825),c=a.i(54159),d=a.i(24311),e=a.i(18688);let f=(0,d.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),g=c.forwardRef(({className:a,variant:c,...d},g)=>(0,b.jsx)("div",{ref:g,role:"alert",className:(0,e.cn)(f({variant:c}),a),...d}));g.displayName="Alert",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("h5",{ref:d,className:(0,e.cn)("mb-1 font-medium leading-none tracking-tight",a),...c})).displayName="AlertTitle";let h=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:(0,e.cn)("text-sm [&_p]:leading-relaxed",a),...c}));h.displayName="AlertDescription"},40579,a=>{"use strict";a.s(["default",()=>j]);var b=a.i(41825),c=a.i(54159),d=a.i(52963),e=a.i(2331),f=a.i(5492),g=a.i(11090),h=a.i(4082),i=a.i(75422);function j({teacher:a,mode:j}){let k=(0,d.useRouter)(),[l,m]=(0,c.useState)(!1),[n,o]=(0,c.useState)(""),[p,q]=(0,c.useState)(""),[r,s]=(0,c.useState)(null),[t,u]=(0,c.useState)({firstName:"",lastName:"",email:"",phone:"",dateOfBirth:"",gender:"MALE",address:"",qualification:"",experience:"",joiningDate:"",salary:"",isActive:!0});(0,c.useEffect)(()=>{a&&u({firstName:a.firstName||"",lastName:a.lastName||"",email:a.email||"",phone:a.phone||"",dateOfBirth:a.dateOfBirth?new Date(a.dateOfBirth).toISOString().split("T")[0]:"",gender:a.gender||"MALE",address:a.address||"",qualification:a.qualification||"",experience:a.experience?.toString()||"",joiningDate:a.joiningDate?new Date(a.joiningDate).toISOString().split("T")[0]:"",salary:a.salary?.toString()||"",isActive:a.isActive??!0})},[a]);let v=async b=>{b.preventDefault(),m(!0),o(""),q(""),s(null);try{let b={...t,experience:t.experience?parseInt(t.experience):void 0,salary:t.salary?parseFloat(t.salary):void 0},c="create"===j?"/api/admin/teachers":`/api/admin/teachers/${a.id}`,d=await fetch(c,{method:"create"===j?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)}),e=await d.json();if(!d.ok)throw Error(e.error||"Failed to save teacher");q(e.message),"create"===j&&e.credentials&&s(e.credentials),"create"===j&&setTimeout(()=>{k.push("/admin/teachers")},2e3)}catch(a){o(a.message)}finally{m(!1)}},w=(a,b)=>{u(c=>({...c,[a]:b}))};return(0,b.jsxs)(h.Card,{className:"w-full max-w-2xl mx-auto",children:[(0,b.jsx)(h.CardHeader,{children:(0,b.jsx)(h.CardTitle,{children:"create"===j?"Add New Teacher":"Edit Teacher"})}),(0,b.jsx)(h.CardContent,{children:(0,b.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[n&&(0,b.jsx)(i.Alert,{variant:"destructive",children:(0,b.jsx)(i.AlertDescription,{children:n})}),p&&(0,b.jsx)(i.Alert,{children:(0,b.jsx)(i.AlertDescription,{children:p})}),r&&(0,b.jsx)(i.Alert,{children:(0,b.jsx)(i.AlertDescription,{children:(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)("p",{className:"font-semibold",children:"Teacher created successfully!"}),(0,b.jsx)("p",{children:"Login credentials:"}),(0,b.jsxs)("div",{className:"bg-gray-100 p-3 rounded",children:[(0,b.jsxs)("p",{children:[(0,b.jsx)("strong",{children:"Email:"})," ",r.email]}),(0,b.jsxs)("p",{children:[(0,b.jsx)("strong",{children:"Password:"})," ",r.password]})]}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Please share these credentials with the teacher."})]})})}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"firstName",children:"First Name *"}),(0,b.jsx)(f.Input,{id:"firstName",value:t.firstName,onChange:a=>w("firstName",a.target.value),required:!0,disabled:l})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"lastName",children:"Last Name *"}),(0,b.jsx)(f.Input,{id:"lastName",value:t.lastName,onChange:a=>w("lastName",a.target.value),required:!0,disabled:l})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"email",children:"Email *"}),(0,b.jsx)(f.Input,{id:"email",type:"email",value:t.email,onChange:a=>w("email",a.target.value),required:!0,disabled:l})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"phone",children:"Phone"}),(0,b.jsx)(f.Input,{id:"phone",value:t.phone,onChange:a=>w("phone",a.target.value),disabled:l})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"dateOfBirth",children:"Date of Birth"}),(0,b.jsx)(f.Input,{id:"dateOfBirth",type:"date",value:t.dateOfBirth,onChange:a=>w("dateOfBirth",a.target.value),disabled:l})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"gender",children:"Gender"}),(0,b.jsxs)("select",{id:"gender",value:t.gender,onChange:a=>w("gender",a.target.value),disabled:l,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,b.jsx)("option",{value:"MALE",children:"Male"}),(0,b.jsx)("option",{value:"FEMALE",children:"Female"}),(0,b.jsx)("option",{value:"OTHER",children:"Other"})]})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"address",children:"Address"}),(0,b.jsx)(f.Input,{id:"address",value:t.address,onChange:a=>w("address",a.target.value),disabled:l})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"qualification",children:"Qualification"}),(0,b.jsx)(f.Input,{id:"qualification",value:t.qualification,onChange:a=>w("qualification",a.target.value),disabled:l})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"experience",children:"Years of Experience"}),(0,b.jsx)(f.Input,{id:"experience",type:"number",min:"0",value:t.experience,onChange:a=>w("experience",a.target.value),disabled:l})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"joiningDate",children:"Joining Date"}),(0,b.jsx)(f.Input,{id:"joiningDate",type:"date",value:t.joiningDate,onChange:a=>w("joiningDate",a.target.value),disabled:l})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"salary",children:"Salary"}),(0,b.jsx)(f.Input,{id:"salary",type:"number",min:"0",step:"0.01",value:t.salary,onChange:a=>w("salary",a.target.value),disabled:l})]})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("input",{id:"isActive",type:"checkbox",checked:t.isActive,onChange:a=>w("isActive",a.target.checked),disabled:l,className:"rounded"}),(0,b.jsx)(g.Label,{htmlFor:"isActive",children:"Active"})]}),(0,b.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,b.jsx)(e.Button,{type:"button",variant:"outline",onClick:()=>k.back(),disabled:l,children:"Cancel"}),(0,b.jsx)(e.Button,{type:"submit",disabled:l,children:l?"Saving...":"create"===j?"Create Teacher":"Update Teacher"})]})]})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__0ef6423c._.js.map