{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\r\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\r\nimport bcrypt from 'bcryptjs'\r\nimport { prisma } from './db'\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null\r\n        }\r\n\r\n        try {\r\n          const user = await prisma.user.findUnique({\r\n            where: {\r\n              email: credentials.email\r\n            }\r\n          })\r\n\r\n          if (!user || !user.hashedPassword) {\r\n            return null\r\n          }\r\n\r\n          const isCorrectPassword = await bcrypt.compare(\r\n            credentials.password,\r\n            user.hashedPassword\r\n          )\r\n\r\n          if (!isCorrectPassword) {\r\n            return null\r\n          }\r\n\r\n          return {\r\n            id: user.id,\r\n            email: user.email,\r\n            name: `${user.firstName} ${user.lastName}`,\r\n            role: user.role,\r\n            firstName: user.firstName,\r\n            lastName: user.lastName\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth error:', error)\r\n          return null\r\n        }\r\n      }\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.role = user.role\r\n        token.firstName = user.firstName\r\n        token.lastName = user.lastName\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token) {\r\n        session.user.id = token.sub!\r\n        session.user.role = token.role as string\r\n        session.user.firstName = token.firstName as string\r\n        session.user.lastName = token.lastName as string\r\n      }\r\n      return session\r\n    }\r\n  },\r\n  pages: {\r\n    signIn: '/login',\r\n    error: '/login'\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,IAAA,mTAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,OAAO,YAAY,KAAK;wBAC1B;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,MAAM,oBAAoB,MAAM,qOAAM,CAAC,OAAO,CAC5C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,mBAAmB;wBACtB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/admin/subjects/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { getServerSession } from 'next-auth';\r\nimport { authOptions } from '@/lib/auth';\r\nimport { prisma } from '@/lib/db';\r\nimport { hasPermission } from '@/lib/rbac';\r\nimport { z } from 'zod';\r\n\r\n// Validation schema for subject data\r\nconst SubjectSchema = z.object({\r\n  name: z.string().min(1, 'Subject name is required'),\r\n  code: z.string().min(1, 'Subject code is required'),\r\n  description: z.string().optional(),\r\n  classId: z.string().min(1, 'Class ID is required'),\r\n});\r\n\r\n// GET /api/admin/subjects - List all subjects\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions);\r\n    if (!session?.user || session.user.role !== 'ADMIN') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n    }\r\n\r\n    const { searchParams } = new URL(request.url);\r\n    const page = parseInt(searchParams.get('page') || '1');\r\n    const limit = parseInt(searchParams.get('limit') || '10');\r\n    const search = searchParams.get('search') || '';\r\n\r\n    const skip = (page - 1) * limit;\r\n\r\n    // Build where clause\r\n    const where: any = {};\r\n    if (search) {\r\n      where.OR = [\r\n        { name: { contains: search, mode: 'insensitive' } },\r\n        { code: { contains: search, mode: 'insensitive' } },\r\n        { description: { contains: search, mode: 'insensitive' } },\r\n      ];\r\n    }\r\n\r\n    // Get subjects with pagination\r\n    const [subjects, total] = await Promise.all([\r\n      prisma.subject.findMany({\r\n        where,\r\n        skip,\r\n        take: limit,\r\n        orderBy: { name: 'asc' },\r\n        include: {\r\n          class: {\r\n            select: {\r\n              id: true,\r\n              name: true,\r\n            },\r\n          },\r\n          _count: {\r\n            select: {\r\n              exams: true,\r\n            },\r\n          },\r\n        },\r\n      }),\r\n      prisma.subject.count({ where }),\r\n    ]);\r\n\r\n    const totalPages = Math.ceil(total / limit);\r\n\r\n    return NextResponse.json({\r\n      subjects,\r\n      pagination: {\r\n        page,\r\n        limit,\r\n        total,\r\n        totalPages,\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error('Error fetching subjects:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to fetch subjects' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/admin/subjects - Create new subject\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions);\r\n    if (!session?.user || session.user.role !== 'ADMIN') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n    }\r\n\r\n    const body = await request.json();\r\n    const validatedData = SubjectSchema.parse(body);\r\n\r\n    // Check if subject with same name or code already exists\r\n    const existingSubject = await prisma.subject.findFirst({\r\n      where: {\r\n        OR: [\r\n          { name: validatedData.name },\r\n          { code: validatedData.code },\r\n        ],\r\n      },\r\n    });\r\n\r\n    if (existingSubject) {\r\n      return NextResponse.json(\r\n        { error: 'Subject with this name or code already exists' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Check if class exists\r\n    const classExists = await prisma.class.findUnique({\r\n      where: { id: validatedData.classId },\r\n    });\r\n\r\n    if (!classExists) {\r\n      return NextResponse.json(\r\n        { error: 'Class not found' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Create subject\r\n    const newSubject = await prisma.subject.create({\r\n      data: validatedData,\r\n      include: {\r\n        class: {\r\n          select: {\r\n            id: true,\r\n            name: true,\r\n          },\r\n        },\r\n      },\r\n    });\r\n\r\n    return NextResponse.json(\r\n      { \r\n        message: 'Subject created successfully',\r\n        subject: newSubject,\r\n      },\r\n      { status: 201 }\r\n    );\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return NextResponse.json(\r\n        { error: 'Validation error', details: error.errors },\r\n        { status: 400 }\r\n      );\r\n    }\r\n    console.error('Error creating subject:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to create subject' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/admin/subjects - Update subject\r\nexport async function PUT(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions);\r\n    if (!session?.user || session.user.role !== 'ADMIN') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n    }\r\n\r\n    const body = await request.json();\r\n    const { id, ...updateData } = body;\r\n\r\n    if (!id) {\r\n      return NextResponse.json(\r\n        { error: 'Subject ID is required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    const validatedData = SubjectSchema.partial().parse(updateData);\r\n\r\n    // Check if subject exists\r\n    const existingSubject = await prisma.subject.findUnique({\r\n      where: { id: parseInt(id) },\r\n    });\r\n\r\n    if (!existingSubject) {\r\n      return NextResponse.json(\r\n        { error: 'Subject not found' },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Check if class exists (if being updated)\r\n    if (validatedData.classId) {\r\n      const classExists = await prisma.class.findUnique({\r\n        where: { id: validatedData.classId },\r\n      });\r\n\r\n      if (!classExists) {\r\n        return NextResponse.json(\r\n          { error: 'Class not found' },\r\n          { status: 400 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Update subject\r\n    const updatedSubject = await prisma.subject.update({\r\n      where: { id: parseInt(id) },\r\n      data: validatedData,\r\n      include: {\r\n        class: {\r\n          select: {\r\n            id: true,\r\n            name: true,\r\n          },\r\n        },\r\n      },\r\n    });\r\n\r\n    return NextResponse.json({\r\n      message: 'Subject updated successfully',\r\n      subject: updatedSubject,\r\n    });\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return NextResponse.json(\r\n        { error: 'Validation error', details: error.errors },\r\n        { status: 400 }\r\n      );\r\n    }\r\n    console.error('Error updating subject:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to update subject' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;AAEA,qCAAqC;AACrC,MAAM,gBAAgB,sQAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,aAAa,sQAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAClD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACnD,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,qBAAqB;QACrB,MAAM,QAAa,CAAC;QACpB,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,MAAM;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBAClD;oBAAE,MAAM;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBAClD;oBAAE,aAAa;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aAC1D;QACH;QAEA,+BAA+B;QAC/B,MAAM,CAAC,UAAU,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC1C,8JAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB;gBACA;gBACA,MAAM;gBACN,SAAS;oBAAE,MAAM;gBAAM;gBACvB,SAAS;oBACP,OAAO;wBACL,QAAQ;4BACN,IAAI;4BACJ,MAAM;wBACR;oBACF;oBACA,QAAQ;wBACN,QAAQ;4BACN,OAAO;wBACT;oBACF;gBACF;YACF;YACA,8JAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC9B;QAED,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;QAErC,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAClD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACnD,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,cAAc,KAAK,CAAC;QAE1C,yDAAyD;QACzD,MAAM,kBAAkB,MAAM,8JAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,IAAI;oBACF;wBAAE,MAAM,cAAc,IAAI;oBAAC;oBAC3B;wBAAE,MAAM,cAAc,IAAI;oBAAC;iBAC5B;YACH;QACF;QAEA,IAAI,iBAAiB;YACnB,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgD,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,cAAc,MAAM,8JAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI,cAAc,OAAO;YAAC;QACrC;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,aAAa,MAAM,8JAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC7C,MAAM;YACN,SAAS;gBACP,OAAO;oBACL,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,iSAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,sQAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QACA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAClD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACnD,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAE9B,IAAI,CAAC,IAAI;YACP,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gBAAgB,cAAc,OAAO,GAAG,KAAK,CAAC;QAEpD,0BAA0B;QAC1B,MAAM,kBAAkB,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE,IAAI,SAAS;YAAI;QAC5B;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,2CAA2C;QAC3C,IAAI,cAAc,OAAO,EAAE;YACzB,MAAM,cAAc,MAAM,8JAAM,CAAC,KAAK,CAAC,UAAU,CAAC;gBAChD,OAAO;oBAAE,IAAI,cAAc,OAAO;gBAAC;YACrC;YAEA,IAAI,CAAC,aAAa;gBAChB,OAAO,iSAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAkB,GAC3B;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,iBAAiB;QACjB,MAAM,iBAAiB,MAAM,8JAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACjD,OAAO;gBAAE,IAAI,SAAS;YAAI;YAC1B,MAAM;YACN,SAAS;gBACP,OAAO;oBACL,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,sQAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QACA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}