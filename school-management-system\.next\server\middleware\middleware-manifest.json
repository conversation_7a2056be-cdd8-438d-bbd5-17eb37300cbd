{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/school-management-system_8c1ea602._.js", "server/edge/chunks/[root-of-the-server]__433d4839._.js", "server/edge/chunks/turbopack-school-management-system_edge-wrapper_db311ba0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/teacher/:path*{(\\\\.json)}?", "originalSource": "/teacher/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/student/:path*{(\\\\.json)}?", "originalSource": "/student/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/admin/:path*{(\\\\.json)}?", "originalSource": "/api/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/teacher/:path*{(\\\\.json)}?", "originalSource": "/api/teacher/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/student/:path*{(\\\\.json)}?", "originalSource": "/api/student/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "O72Sp333G9CD2xefaiYCeef/o971MfYvqNDNBykp5go=", "__NEXT_PREVIEW_MODE_ID": "b9cd5b2f2ebf277294f352a3d24eaefe", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bec9669231a7b801c6e460bd843481f7d33aaa3a73d8c26b1bd1b477c5030d05", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d93c08417ebc408befa6c7d08b4d5f0965d2042f8fc0ac2265facec23a3b780f"}}}, "instrumentation": null, "functions": {}}