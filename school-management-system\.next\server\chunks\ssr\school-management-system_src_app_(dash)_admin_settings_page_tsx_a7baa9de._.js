module.exports=[56471,a=>{"use strict";a.s(["default",()=>u],56471);var b=a.i(41825),c=a.i(54159),d=a.i(4082),e=a.i(2331),f=a.i(5492),g=a.i(11090),h=a.i(62821),i=a.i(72613),j=a.i(82193),k=a.i(74751),l=a.i(32639);let m=(0,l.default)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),n=(0,l.default)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),o=(0,l.default)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var p=a.i(26006);let q=(0,l.default)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var r=a.i(29683),s=a.i(43001),t=a.i(95788);function u(){let[a,l]=(0,c.useState)("general"),[u,v]=(0,c.useState)(!1),[w,x]=(0,c.useState)(null),[y,z]=(0,c.useState)({name:"Advance School",address:"123 Education Street, City, State 12345",phone:"+****************",email:"<EMAIL>",website:"www.advanceschool.edu",principal:"Dr. John Smith",establishedYear:"1995"}),[A,B]=(0,c.useState)({academicYear:"2024-2025",currentTerm:"Term 1",gradingSystem:"LETTER",passPercentage:40,maxAttendancePercentage:75}),[C,D]=(0,c.useState)({attendanceAlerts:!0,examResults:!0,reportCardGeneration:!1,systemUpdates:!0}),[E,F]=(0,c.useState)({sessionTimeout:30,passwordPolicy:"strong",twoFactorAuth:!1,loginAttempts:!0});(0,c.useEffect)(()=>{(async()=>{try{let a=await fetch("/api/admin/settings");if(a.ok){let b=await a.json();b.general&&z({name:b.general.schoolName,address:b.general.address,phone:b.general.phone,email:b.general.email,website:b.general.website,principal:b.general.principal,establishedYear:b.general.establishedYear}),b.academic&&B({academicYear:b.academic.academicYear,currentTerm:b.academic.currentTerm,gradingSystem:b.academic.gradingSystem,passPercentage:b.academic.passPercentage,maxAttendancePercentage:b.academic.maxAttendancePercentage}),b.notifications&&D({attendanceAlerts:b.notifications.attendanceAlerts,examResults:b.notifications.examResults,reportCardGeneration:b.notifications.reportCardGeneration,systemUpdates:b.notifications.systemUpdates}),b.security&&F({sessionTimeout:b.security.sessionTimeout,passwordPolicy:b.security.passwordPolicy,twoFactorAuth:b.security.twoFactorAuth,loginAttempts:b.security.loginAttempts})}}catch(a){console.error("Error loading settings:",a)}})()},[]);let G=(a,b)=>{x({type:a,text:b}),setTimeout(()=>x(null),3e3)},H=async()=>{v(!0);try{(await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"general",data:y})})).ok?G("success","School information saved successfully!"):G("error","Failed to save school information")}catch(a){G("error","Error saving school information")}finally{v(!1)}},I=async()=>{v(!0);try{(await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"academic",data:A})})).ok?G("success","Academic settings saved successfully!"):G("error","Failed to save academic settings")}catch(a){G("error","Error saving academic settings")}finally{v(!1)}},J=async()=>{v(!0);try{(await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"notifications",data:C})})).ok?G("success","Notification settings saved successfully!"):G("error","Failed to save notification settings")}catch(a){G("error","Error saving notification settings")}finally{v(!1)}},K=async()=>{v(!0);try{(await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"security",data:E})})).ok?G("success","Security settings saved successfully!"):G("error","Failed to save security settings")}catch(a){G("error","Error saving security settings")}finally{v(!1)}};return(0,b.jsx)(h.default,{title:"System Settings",navigation:t.adminNavigation,children:(0,b.jsxs)("div",{className:"space-y-6",children:[w&&(0,b.jsxs)("div",{className:`p-4 rounded-md flex items-center ${"success"===w.type?"bg-green-50 border border-green-200 text-green-800":"bg-red-50 border border-red-200 text-red-800"}`,children:["success"===w.type?(0,b.jsx)(r.CheckCircle,{className:"w-5 h-5 mr-2"}):(0,b.jsx)(s.AlertCircle,{className:"w-5 h-5 mr-2"}),w.text]}),(0,b.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"System Settings"}),(0,b.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Manage school configuration and preferences"})]}),(0,b.jsxs)("div",{className:"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2",children:[(0,b.jsxs)(e.Button,{variant:"outline",disabled:u,className:"w-full sm:w-auto",children:[(0,b.jsx)(q,{className:"w-4 h-4 mr-2"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Reset"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Reset to Default"})]}),(0,b.jsxs)(e.Button,{disabled:u,className:"w-full sm:w-auto",children:[(0,b.jsx)(p.Save,{className:"w-4 h-4 mr-2"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Save All"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Save All Changes"})]})]})]}),(0,b.jsx)("div",{className:"border-b border-gray-200",children:(0,b.jsxs)("nav",{className:"-mb-px flex flex-wrap gap-2 sm:gap-8",children:[(0,b.jsxs)("button",{onClick:()=>l("general"),className:`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${"general"===a?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,b.jsx)(j.School,{className:"inline w-4 h-4 mr-1 sm:mr-2"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"General Settings"}),(0,b.jsx)("span",{className:"sm:hidden",children:"General"})]}),(0,b.jsxs)("button",{onClick:()=>l("academic"),className:`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${"academic"===a?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,b.jsx)(i.Calendar,{className:"inline w-4 h-4 mr-1 sm:mr-2"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Academic Settings"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Academic"})]}),(0,b.jsxs)("button",{onClick:()=>l("notifications"),className:`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${"notifications"===a?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,b.jsx)(k.Bell,{className:"inline w-4 h-4 mr-1 sm:mr-2"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Notifications"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Alerts"})]}),(0,b.jsxs)("button",{onClick:()=>l("security"),className:`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${"security"===a?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,b.jsx)(m,{className:"inline w-4 h-4 mr-1 sm:mr-2"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Security"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Security"})]})]})}),"general"===a&&(0,b.jsx)("div",{className:"space-y-6",children:(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,b.jsx)(j.School,{className:"w-5 h-5 mr-2"}),"School Information"]}),(0,b.jsx)(d.CardDescription,{children:"Update basic school information and contact details"})]}),(0,b.jsxs)(d.CardContent,{children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"schoolName",children:"School Name"}),(0,b.jsx)(f.Input,{id:"schoolName",value:y.name,onChange:a=>z({...y,name:a.target.value}),placeholder:"Enter school name"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"principal",children:"Principal Name"}),(0,b.jsx)(f.Input,{id:"principal",value:y.principal,onChange:a=>z({...y,principal:a.target.value}),placeholder:"Enter principal name"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"phone",children:"Phone Number"}),(0,b.jsx)(f.Input,{id:"phone",value:y.phone,onChange:a=>z({...y,phone:a.target.value}),placeholder:"Enter phone number"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"email",children:"Email Address"}),(0,b.jsx)(f.Input,{id:"email",type:"email",value:y.email,onChange:a=>z({...y,email:a.target.value}),placeholder:"Enter email address"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"website",children:"Website"}),(0,b.jsx)(f.Input,{id:"website",value:y.website,onChange:a=>z({...y,website:a.target.value}),placeholder:"Enter website URL"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"establishedYear",children:"Established Year"}),(0,b.jsx)(f.Input,{id:"establishedYear",value:y.establishedYear,onChange:a=>z({...y,establishedYear:a.target.value}),placeholder:"Enter established year"})]}),(0,b.jsxs)("div",{className:"md:col-span-2",children:[(0,b.jsx)(g.Label,{htmlFor:"address",children:"Address"}),(0,b.jsx)(f.Input,{id:"address",value:y.address,onChange:a=>z({...y,address:a.target.value}),placeholder:"Enter complete address"})]})]}),(0,b.jsx)("div",{className:"mt-6",children:(0,b.jsxs)(e.Button,{onClick:H,disabled:u,children:[(0,b.jsx)(p.Save,{className:"w-4 h-4 mr-2"}),u?"Saving...":"Save School Information"]})})]})]})}),"academic"===a&&(0,b.jsx)("div",{className:"space-y-6",children:(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,b.jsx)(i.Calendar,{className:"w-5 h-5 mr-2"}),"Academic Configuration"]}),(0,b.jsx)(d.CardDescription,{children:"Configure academic year, grading system, and performance criteria"})]}),(0,b.jsxs)(d.CardContent,{children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"academicYear",children:"Academic Year"}),(0,b.jsx)(f.Input,{id:"academicYear",value:A.academicYear,onChange:a=>B({...A,academicYear:a.target.value}),placeholder:"e.g., 2024-2025"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"currentTerm",children:"Current Term"}),(0,b.jsxs)("select",{id:"currentTerm",value:A.currentTerm,onChange:a=>B({...A,currentTerm:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"Term 1",children:"Term 1"}),(0,b.jsx)("option",{value:"Term 2",children:"Term 2"}),(0,b.jsx)("option",{value:"Term 3",children:"Term 3"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"gradingSystem",children:"Grading System"}),(0,b.jsxs)("select",{id:"gradingSystem",value:A.gradingSystem,onChange:a=>B({...A,gradingSystem:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"LETTER",children:"Letter Grades (A+, A, B+, B, etc.)"}),(0,b.jsx)("option",{value:"PERCENTAGE",children:"Percentage"}),(0,b.jsx)("option",{value:"NUMERIC",children:"Numeric (1-10)"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"passPercentage",children:"Pass Percentage"}),(0,b.jsx)(f.Input,{id:"passPercentage",type:"number",value:A.passPercentage,onChange:a=>B({...A,passPercentage:parseInt(a.target.value)}),placeholder:"40"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"maxAttendancePercentage",children:"Minimum Attendance %"}),(0,b.jsx)(f.Input,{id:"maxAttendancePercentage",type:"number",value:A.maxAttendancePercentage,onChange:a=>B({...A,maxAttendancePercentage:parseInt(a.target.value)}),placeholder:"75"})]})]}),(0,b.jsx)("div",{className:"mt-6",children:(0,b.jsxs)(e.Button,{onClick:I,disabled:u,children:[(0,b.jsx)(p.Save,{className:"w-4 h-4 mr-2"}),u?"Saving...":"Save Academic Settings"]})})]})]})}),"notifications"===a&&(0,b.jsx)("div",{className:"space-y-6",children:(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,b.jsx)(k.Bell,{className:"w-5 h-5 mr-2"}),"Notification Settings"]}),(0,b.jsx)(d.CardDescription,{children:"Configure email notifications and alerts"})]}),(0,b.jsxs)(d.CardContent,{children:[(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium",children:"Attendance Alerts"}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Send notifications for low attendance"})]}),(0,b.jsx)("input",{type:"checkbox",className:"rounded",checked:C.attendanceAlerts,onChange:a=>D({...C,attendanceAlerts:a.target.checked})})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium",children:"Exam Results"}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Notify when exam results are published"})]}),(0,b.jsx)("input",{type:"checkbox",className:"rounded",checked:C.examResults,onChange:a=>D({...C,examResults:a.target.checked})})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium",children:"Report Card Generation"}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Notify when report cards are ready"})]}),(0,b.jsx)("input",{type:"checkbox",className:"rounded",checked:C.reportCardGeneration,onChange:a=>D({...C,reportCardGeneration:a.target.checked})})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium",children:"System Updates"}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Receive system maintenance notifications"})]}),(0,b.jsx)("input",{type:"checkbox",className:"rounded",checked:C.systemUpdates,onChange:a=>D({...C,systemUpdates:a.target.checked})})]})]}),(0,b.jsx)("div",{className:"mt-6",children:(0,b.jsxs)(e.Button,{onClick:J,disabled:u,children:[(0,b.jsx)(p.Save,{className:"w-4 h-4 mr-2"}),u?"Saving...":"Save Notification Settings"]})})]})]})}),"security"===a&&(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,b.jsx)(m,{className:"w-5 h-5 mr-2"}),"Security Settings"]}),(0,b.jsx)(d.CardDescription,{children:"Manage password policies and security settings"})]}),(0,b.jsxs)(d.CardContent,{children:[(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"sessionTimeout",children:"Session Timeout (minutes)"}),(0,b.jsx)(f.Input,{id:"sessionTimeout",type:"number",value:E.sessionTimeout,onChange:a=>F({...E,sessionTimeout:parseInt(a.target.value)}),placeholder:"30"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(g.Label,{htmlFor:"passwordPolicy",children:"Password Policy"}),(0,b.jsxs)("select",{id:"passwordPolicy",value:E.passwordPolicy,onChange:a=>F({...E,passwordPolicy:a.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"strong",children:"Strong (8+ chars, mixed case, numbers)"}),(0,b.jsx)("option",{value:"medium",children:"Medium (6+ chars, mixed case)"}),(0,b.jsx)("option",{value:"weak",children:"Weak (4+ chars)"})]})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium",children:"Two-Factor Authentication"}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Require 2FA for admin accounts"})]}),(0,b.jsx)("input",{type:"checkbox",className:"rounded",checked:E.twoFactorAuth,onChange:a=>F({...E,twoFactorAuth:a.target.checked})})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium",children:"Login Attempts"}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Lock account after failed attempts"})]}),(0,b.jsx)("input",{type:"checkbox",className:"rounded",checked:E.loginAttempts,onChange:a=>F({...E,loginAttempts:a.target.checked})})]})]}),(0,b.jsx)("div",{className:"mt-6",children:(0,b.jsxs)(e.Button,{onClick:K,disabled:u,children:[(0,b.jsx)(p.Save,{className:"w-4 h-4 mr-2"}),u?"Saving...":"Save Security Settings"]})})]})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,b.jsx)(n,{className:"w-5 h-5 mr-2"}),"System Maintenance"]}),(0,b.jsx)(d.CardDescription,{children:"Database backup and system maintenance options"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full",children:[(0,b.jsx)(n,{className:"w-4 h-4 mr-2"}),"Create Database Backup"]}),(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full",children:[(0,b.jsx)(q,{className:"w-4 h-4 mr-2"}),"Clear Cache"]}),(0,b.jsxs)(e.Button,{variant:"outline",className:"w-full",children:[(0,b.jsx)(o,{className:"w-4 h-4 mr-2"}),"Test Email Configuration"]})]})})]})]})]})})}}];

//# sourceMappingURL=school-management-system_src_app_%28dash%29_admin_settings_page_tsx_a7baa9de._.js.map